# Project Structure & Organization

## Root Level Structure

```
zx_vanx_parent/
├── gateway/                    # API Gateway (Spring Cloud Gateway)
├── service/                    # Microservices implementations
├── service_api/                # Service API definitions (interfaces)
├── common/                     # Shared utilities and configurations
├── code_generator/             # MyBatis-Plus code generation tools
├── docker/                     # Docker Compose and container configs
├── sql/                        # Database schema and migration scripts
├── doc/                        # Project documentation
└── pom.xml                     # Parent Maven POM
```

## Microservices Architecture

### Service Layer (`service/`)
Business logic implementations:
- `user_permiss_serv` - User permissions and authentication
- `app_sys_media_serv` - System media management (core service)
- `app_user_media_serv` - User media content
- `content_recommend_serv` - Recommendation engine
- `public_creation_serv` - Content creation and publishing
- `social_circle_serv` - Social interactions and circles
- `shop_item_serv` - E-commerce product management
- `trade_order_serv` - Order processing and management
- `pay_withdra_serv` - Payment and withdrawal processing
- `electro_resource_serv` - Electronic resource management

### API Layer (`service_api/`)
Interface definitions and DTOs:
- Mirrors service structure with `_api` suffix
- Contains Feign clients, request/response models
- Additional APIs: chat, contacts, job recruitment, market management

### Common Modules (`common/`)
Shared components:
- `common-core` - Core business utilities
- `common-redis` - Redis configuration and utilities
- `common-satoken` - Sa-Token authentication setup
- `common-security` - Security configurations
- `common-justauth` - Social login integration
- `common_util` - General utilities
- `sevice_util` - Service-specific utilities
- `minio_util` - MinIO file storage utilities
- `rabbit_util` - RabbitMQ messaging utilities

## Naming Conventions

### Package Structure
- Root package: `com.zx`
- Service packages follow pattern: `com.zx.service.[service_name]`
- API packages: `com.zx.api.[service_name]`
- Common packages: `com.zx.common.[module_name]`

### Module Naming
- Services: `[domain]_serv` (e.g., `user_permiss_serv`)
- APIs: `[domain]_api` (e.g., `user_permiss_api`)
- Common modules: `common-[function]` or `[function]_util`

### Artifact IDs
- Follow module directory names exactly
- Version: `1.0-SNAPSHOT` across all modules
- Group ID: `com.zx` for all modules

## Database Organization

### SQL Scripts (`sql/`)
- `vanx_user_permiss.sql` - User and permission tables
- `vanx_sys_media.sql` - System media tables
- `vanx_user_media.sql` - User media content tables
- `vanx_public_creation.sql` - Content creation tables
- `vanx_shop_item.sql` - E-commerce tables
- `vanx_trade_order.sql` - Order management tables

### Database Naming
- Database prefix: `vanx_`
- Table naming: snake_case
- Follow domain-driven design principles

## Infrastructure (`docker/`)

### Container Services
- `mysql/` - MySQL 8.0 configuration and data
- `redis/` - Redis cluster configuration
- `rabbitmq/` - RabbitMQ with management plugin
- `minio/` - MinIO object storage
- `nacos/` - Service discovery configuration
- `nginx/` - Load balancer and reverse proxy

### Volume Mapping
- Data persistence: `/docker/[service]/data/`
- Configuration: `/docker/[service]/conf/`
- Logs: `/docker/[service]/logs/`

## Development Guidelines

### Module Dependencies
- Services depend on their corresponding APIs
- All modules can use common utilities
- Gateway has minimal dependencies (no web/mybatis)
- Avoid circular dependencies between services

### Code Organization
- Controllers in `controller` package
- Services in `service` package
- Data access in `mapper` package
- Models/entities in `entity` package
- Configuration in `config` package

### Build Artifacts
- Gateway: `vanx_gateway.jar`
- Services: `[service-name]-1.0.0.jar`
- Each service builds independently