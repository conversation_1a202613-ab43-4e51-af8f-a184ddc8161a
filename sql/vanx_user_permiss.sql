/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80031
 Source Host           : *************:3306
 Source Schema         : vanx_user_permiss

 Target Server Type    : MySQL
 Target Server Version : 80031
 File Encoding         : 65001

 Date: 02/07/2024 11:21:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for user_address
-- ----------------------------
DROP TABLE IF EXISTS `user_address`;
CREATE TABLE `user_address`  (
  `user_address_id` bigint(0) NOT NULL COMMENT '主键id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户的id',
  `country_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在国家国名',
  `province_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在省',
  `city_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在市',
  `district_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在区',
  `town_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在的镇，街道名称',
  `full_address` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户详细地址',
  `postal_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '邮编',
  `location_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户位置类型 1 、个人资料中的所在地 2 、个人资料中的故乡3、收件地址',
  `is_default` tinyint(0) NULL DEFAULT NULL COMMENT '地址是否默认使用 0 1',
  `info_address_status` tinyint(0) NULL DEFAULT NULL COMMENT '是否有效1 2',
  `other_info_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除 0 1',
  PRIMARY KEY (`user_address_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用户地址表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_address
-- ----------------------------
INSERT INTO `user_address` VALUES (421054829645824005, '2023-12-06 15:47:48', '2024-03-06 15:52:20', NULL, NULL, 421054829645824000, NULL, '吉林省', '四平市', '梨树县', NULL, NULL, NULL, '故乡', 1, 1, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (421369491834830853, '2023-12-07 12:08:51', '2023-12-07 12:08:51', NULL, NULL, 421369491834830848, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (425450591234326533, '2023-12-18 12:05:36', '2023-12-18 12:05:36', NULL, NULL, 425450591234326528, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (452999623867400197, '2024-03-01 17:49:55', '2024-03-01 17:49:55', NULL, NULL, 452999623867400192, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (454814818780610560, '2024-03-06 15:13:48', '2024-03-06 15:52:20', NULL, NULL, 421054829645824000, NULL, '江苏省', '常州市', '新北区', NULL, NULL, NULL, '所在地', 1, 1, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (471083055386230789, '2024-04-19 11:22:52', '2024-04-19 11:22:52', NULL, NULL, 471083055386230784, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_address` VALUES (482646202563723269, '2024-05-20 15:13:46', '2024-05-20 15:13:46', NULL, NULL, 482646202563723264, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for user_authorize
-- ----------------------------
DROP TABLE IF EXISTS `user_authorize`;
CREATE TABLE `user_authorize`  (
  `authorize_id` bigint(0) NOT NULL COMMENT '主键id号',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `user_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '1' COMMENT '用户状态 1.正常 2.禁用;3.其他',
  `identity_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '登录类型(手机号 邮箱 用户名称)或第三方应用的名称( 微信 微博等)',
  `identifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标识(手机号 邮箱 用户名或第三方应用的唯一标识)',
  `credential` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '密码凭证(站内的保存密码，站外的不保存或保存token)',
  `is_thirdlogin` tinyint(0) NULL DEFAULT NULL COMMENT '是第三方登录。还是站内登录',
  `login_time` date NULL DEFAULT NULL COMMENT '用户登录时间',
  `login_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户登录的设备ip',
  `login_note` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '登录情况备注(是否异常，异常情况)',
  `login_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '登录情况状态 1 正常 2 异常 3 其他',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除(1-未删除，2-删除，默认1)',
  PRIMARY KEY (`authorize_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用户授权表(即各种登录方式，支持第三方登录)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_authorize
-- ----------------------------
INSERT INTO `user_authorize` VALUES (421054829645824004, '2023-12-06 15:47:48', '2023-12-06 15:47:48', NULL, NULL, 421054829645824000, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_authorize` VALUES (421369491834830852, '2023-12-07 12:08:51', '2023-12-07 12:08:51', NULL, NULL, 421369491834830848, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_authorize` VALUES (425450591234326532, '2023-12-18 12:05:36', '2023-12-18 12:05:36', NULL, NULL, 425450591234326528, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_authorize` VALUES (452999623867400196, '2024-03-01 17:49:55', '2024-03-01 17:49:55', NULL, NULL, 452999623867400192, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_authorize` VALUES (471083055386230788, '2024-04-19 11:22:52', '2024-04-19 11:22:52', NULL, NULL, 471083055386230784, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_authorize` VALUES (482646202563723268, '2024-05-20 15:13:46', '2024-05-20 15:13:46', NULL, NULL, 482646202563723264, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for user_consume_points_log
-- ----------------------------
DROP TABLE IF EXISTS `user_consume_points_log`;
CREATE TABLE `user_consume_points_log`  (
  `user_points_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `order_id` bigint(0) NULL DEFAULT NULL COMMENT '用户已经支付的订单id',
  `order_items_id` bigint(0) NULL DEFAULT NULL COMMENT 'trade_order_items的id',
  `sku_id` bigint(0) NULL DEFAULT NULL COMMENT 'sku的id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '用户购买某服务或商品所在的店铺id',
  `user_behavior_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户行为类型',
  `value_change_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '此次获取的成长值的改变类型：增加、减少',
  `user_points_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户积分类型',
  `consume_points` double(14, 2) NULL DEFAULT NULL COMMENT '用户积分变换的取值',
  `consume_points_description` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户积分变换的描述情况',
  `consume_points_source` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户积分获取或消减的来源：1、系统平台积分2、店铺积分',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_points_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_consume_points_log
-- ----------------------------
INSERT INTO `user_consume_points_log` VALUES (1, 421054829645824000, NULL, NULL, NULL, NULL, '购买礼物', '减少', '购物积分', 10.00, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for user_extend_info
-- ----------------------------
DROP TABLE IF EXISTS `user_extend_info`;
CREATE TABLE `user_extend_info`  (
  `user_extend_id` bigint(0) NOT NULL COMMENT '主键id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `entry_time` datetime(0) NULL DEFAULT NULL COMMENT '入职时间',
  `company` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `career_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '职业类型',
  `qualific_certifi_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的职业等级',
  `qualific_certifi_photo` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的职业照片',
  `exhibition_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的展业证照片',
  `integrity_level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户诚信等级',
  `user_introduction` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户自我介绍',
  `user_personal_signa` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户个性签名',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `is_deleted` tinyint(0) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除(1 未删除 2 删除 默认 1)',
  PRIMARY KEY (`user_extend_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_extend_info
-- ----------------------------
INSERT INTO `user_extend_info` VALUES (421054829645824003, '2023-12-06 15:47:48', '2024-06-14 14:58:28', NULL, 421054829645824000, 421054829645824000, NULL, '南京展信息科技有限公有', '商业/服务业/个体经营', NULL, NULL, NULL, NULL, '刘涛是一个好人呢！', '刘涛真帅呀！', NULL, NULL, NULL, 0);
INSERT INTO `user_extend_info` VALUES (421369491834830851, '2023-12-07 12:08:51', '2024-01-23 14:39:20', NULL, 421369491834830848, 421369491834830848, NULL, NULL, '律师/法务', NULL, NULL, NULL, NULL, '可惜没如果~~', '可惜没如果~', NULL, NULL, NULL, 0);
INSERT INTO `user_extend_info` VALUES (425450591234326531, '2023-12-18 12:05:36', '2023-12-18 12:05:36', NULL, NULL, 425450591234326528, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_extend_info` VALUES (452999623867400195, '2024-03-01 17:49:55', '2024-03-01 17:55:33', NULL, 452999623867400192, 452999623867400192, NULL, NULL, '计算机/互联网/通信', NULL, NULL, NULL, NULL, '不想努力，找个富婆吧！', '我要进阿里巴巴当老总！！！', NULL, NULL, NULL, 0);
INSERT INTO `user_extend_info` VALUES (471083055386230787, '2024-04-19 11:22:52', '2024-04-19 11:22:52', NULL, NULL, 471083055386230784, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_extend_info` VALUES (482646202563723267, '2024-05-20 15:13:46', '2024-05-20 15:13:46', NULL, NULL, 482646202563723264, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for user_info
-- ----------------------------
DROP TABLE IF EXISTS `user_info`;
CREATE TABLE `user_info`  (
  `user_id` bigint(0) NOT NULL COMMENT '主键id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `user_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户真实姓名(加密)',
  `user_nickname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `user_photo` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `user_motto` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的座右铭或个人签名',
  `user_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户(代理人)编号',
  `sex` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `credential_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的证件类型(身份证,护照)',
  `credential_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户证件号码(加密)',
  `user_internal_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '系统生成的账号（唯一），即：Vanx号',
  `tel` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户手机号(加密)',
  `email` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户邮箱(加密)',
  `role_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的角色类型:1.代理人:agent;4.销售主管:sales_manager',
  `other_info_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` int(0) NULL DEFAULT 0 COMMENT '是否删除（1-未删除，2-删除默认1）',
  `age` int(0) NULL DEFAULT NULL COMMENT '用户年龄',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用户基本信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_info
-- ----------------------------
INSERT INTO `user_info` VALUES (421054829645824000, '2023-12-06 15:47:48', '2024-06-21 17:51:21', NULL, 421054829645824000, NULL, 'LiuTBaby', 'https://gitee.com/dongyanxiao/hello-gitee/raw/master/dongImg/defaut_user_photo.jpg', '好好当黑奴', NULL, '男', '2000-04-03', NULL, NULL, 'vanx_879753819', 'f4fc6d7bbdc8ddfc641efe53bc0ad028', NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `user_info` VALUES (421369491834830848, '2023-12-07 12:08:51', '2024-01-23 14:39:10', NULL, 421369491834830848, NULL, '嘻嘻嘻', NULL, NULL, NULL, '男', NULL, NULL, NULL, 'vanx_507737118', 'e2e662a36c7564949e26f478fa3b441c', NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `user_info` VALUES (425450591234326528, '2023-12-18 12:05:36', '2024-05-15 17:15:24', NULL, 425450591234326528, NULL, 'Zzzzdf', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'vanx_196011683', 'e1c81931402dd26336832f2deee9215c', NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `user_info` VALUES (452999623867400192, '2024-03-01 17:49:55', '2024-03-04 15:43:28', NULL, 452999623867400192, NULL, 'xiaoliu', NULL, NULL, NULL, '男', NULL, NULL, NULL, 'vanx_547434777', '1a63d7967930fd8a49194f04c3a4f7ed', NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `user_info` VALUES (471083055386230784, '2024-04-19 11:22:51', '2024-04-19 11:22:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'vanx_422942060', '51252a57dbe5d8899e26f478fa3b441c', NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `user_info` VALUES (482646202563723264, '2024-05-20 15:13:46', '2024-05-20 15:13:46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'vanx_146442703', 'eaba4a3b612e808a3c6551154e4e5471', NULL, NULL, NULL, NULL, NULL, 0, NULL);

-- ----------------------------
-- Table structure for user_info_fast
-- ----------------------------
DROP TABLE IF EXISTS `user_info_fast`;
CREATE TABLE `user_info_fast`  (
  `user_fast_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `longitude` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户所在位置的经度',
  `latitude` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户所在位置纬度',
  `media_current_page` bigint(0) NULL DEFAULT 1 COMMENT '用户浏览推荐视频当前页码',
  `growth_value` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的成长值',
  `user_virtual_balance` decimal(14, 2) NULL DEFAULT NULL COMMENT '用户系统中虚拟币的余额',
  `is_paid_memb_user` tinyint(1) NULL DEFAULT 0 COMMENT '该用户是否为付费会员用户，即：购买了会员卡的用户',
  `need_pop_up_advert` tinyint(1) NULL DEFAULT 0 COMMENT '该用户是否被需要弹出广告',
  `user_behavior_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的行为积分，即该字段只能增加，不能进行兑换，仅为计算用户成长值',
  `user_shopping_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的购物积分，即该字段不能进行兑换，仅为计算用户成长值',
  `user_game_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的游戏积分，即该字段不能进行兑换，仅为计算用户成长值',
  `dynamic_behavior_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的动态行为积分，即该积分可以增加或减扣',
  `dynamic_shopping_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的动态购物积分，即该积分可以增加或减扣',
  `dynamic_game_points` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户的动态游戏积分，即该积分可以增加或减扣',
  `used_total_disk_size` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户已经使用的全部云盘大小',
  `user_public_disk_size` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户公开、粉丝、好友圈使用的云盘大小',
  `user_priv_disk_size` double(14, 2) NULL DEFAULT 0.00 COMMENT '用户使用的私有空间大小',
  `recommend_value` double(14, 2) NULL DEFAULT NULL COMMENT '该用户的人气；或者推荐指数',
  `money_of_received_item` decimal(14, 2) NULL DEFAULT NULL COMMENT '该用户收到别人送的礼物的金钱数量(金额)',
  `money_for_recommend` decimal(14, 2) NULL DEFAULT NULL COMMENT '为提高该用户的人气；或者推荐指数而消费的金钱数量(金额)',
  `num_of_followers` tinyint(0) NULL DEFAULT NULL COMMENT '该用户的粉丝数量',
  `user_nickname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `role_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户角色类型',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_fast_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_info_fast
-- ----------------------------
INSERT INTO `user_info_fast` VALUES (421054829645824001, 421054829645824000, 0.00, 0.00, 5, 22.32, 888888.00, 0, 1, 5000.00, 5000.00, 5000.00, 5000.00, 5000.00, 5000.00, 732303115.00, 309523311.00, 422779804.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-06 15:47:48', '2024-07-02 11:21:31', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (421369491834830849, 421369491834830848, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-07 12:08:51', '2024-05-16 14:00:00', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (425450591234326529, 425450591234326528, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-18 12:05:36', '2024-07-01 17:13:40', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (452999623867400193, 452999623867400192, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-03-01 17:49:54', '2024-05-16 14:00:00', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (459936283813249025, 459936283813249024, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-03-20 10:27:41', '2024-05-16 14:00:00', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (471083055386230785, 471083055386230784, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-19 11:22:51', '2024-05-16 14:00:00', 0, NULL, NULL, NULL);
INSERT INTO `user_info_fast` VALUES (482646202563723265, 482646202563723264, 0.00, 0.00, 1, 0.00, 0.00, 0, 0, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 15:13:46', '2024-07-01 17:13:40', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for user_picture
-- ----------------------------
DROP TABLE IF EXISTS `user_picture`;
CREATE TABLE `user_picture`  (
  `user_picture_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `picture_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的照片标题',
  `picture_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的照片类型：1、精选照片2、证件照片',
  `picture_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的照片url',
  `picture_minio_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的照片的minio名称（用于minio删除）',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '封面添加方式：平台添加、自定义添加',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `image_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '图片的认证状态0、未认证1、待审核2、通过3、驳回4、已取消',
  `reject_reason` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '图片认证失败的原因',
  `check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `check_user_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '审核人的姓名',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_picture_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'user_picture' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_picture
-- ----------------------------
INSERT INTO `user_picture` VALUES (435876121834192896, NULL, NULL, '个人封面照片', 'http://101.43.154.175:9003/zx-vanx/421369491834830848/1705992030702.jpg', '421054829645824000/1705048385925.jpg', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-15 14:21:58', '2024-01-15 14:21:58', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (435876121834192897, NULL, NULL, '个人头像', 'http://101.43.154.175:9003/zx-vanx/421369491834830848/1705991985643.jpg', '421054829645824000/1705393085606.jpg', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 11:27:33', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (461907467643682816, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-03-25/1711360612490.jpg', '421054829645824000/2024-03-25/1711360612490.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-03-25 17:56:53', '2024-03-25 17:56:53', 1, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (467466869016657920, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-04-09/1712655012224.jpg', '421054829645824000/2024-04-09/1712655012224.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-09 17:30:13', '2024-04-09 17:30:13', 1, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (471084330991517696, 421054829645824000, NULL, '个人头像', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-04-19/1713497915581.jpg', '421054829645824000/2024-04-19/1713497915581.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-04-19 11:27:48', '2024-04-19 11:38:36', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482677152098058240, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716196432020.jpg', '421054829645824000/2024-05-20/1716196432020.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:13:53', '2024-05-20 17:13:53', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482677199342698496, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716196443210.jpg', '421054829645824000/2024-05-20/1716196443210.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:14:03', '2024-05-20 17:14:03', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482677229407469568, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716196450014.jpg', '421054829645824000/2024-05-20/1716196450014.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:14:10', '2024-05-20 17:14:10', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482677259472240640, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716196457117.jpg', '421054829645824000/2024-05-20/1716196457117.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:14:17', '2024-05-20 17:14:17', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482682761325346816, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716197738580.jpg', '421054829645824000/2024-05-20/1716197738580.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:35:39', '2024-05-20 17:35:39', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482685145032196096, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716198292568.jpg', '421054829645824000/2024-05-20/1716198292568.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:44:53', '2024-05-20 17:44:53', 1, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (482685510104416256, 421054829645824000, NULL, '精选照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-05-20/1716198378821.jpg', '421054829645824000/2024-05-20/1716198378821.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-20 17:46:19', '2024-05-20 17:46:19', 0, NULL, NULL, NULL);
INSERT INTO `user_picture` VALUES (483059597460930560, 421054829645824000, NULL, '个人封面照片', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-19/1718784714441.jpg', '421054829645824000/2024-06-19/1718784714441.jpg', '自定义添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-05-21 17:57:57', '2024-06-19 16:11:55', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for user_register
-- ----------------------------
DROP TABLE IF EXISTS `user_register`;
CREATE TABLE `user_register`  (
  `register_id` bigint(0) NOT NULL COMMENT '注册的id号',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `user_id` bigint(0) NOT NULL COMMENT '用户id号(唯一)',
  `regist_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '注册用户名(唯一)',
  `user_internal_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '系统生成的账号（唯一），即：Vanx号',
  `regist_psw` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '登录系统的密码(加密)',
  `tel` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '注册手机号',
  `email` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '注册邮箱号',
  `regist_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '注册设备ip',
  `login_num` int(0) NULL DEFAULT NULL COMMENT '登录次数',
  `online_time` time(0) NULL DEFAULT NULL COMMENT '用户在线时间',
  `user_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户状态:1:正常;2:禁用；3:其他',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除（1-未删除，2-删除默认1）',
  PRIMARY KEY (`register_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用户注册，登录验证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_register
-- ----------------------------
INSERT INTO `user_register` VALUES (421054829645824002, '2024-06-27 15:47:48', '2023-12-06 15:47:48', NULL, NULL, 421054829645824000, NULL, 'vanx_879753819', '071f5212c4332ae6', 'f4fc6d7bbdc8ddfc641efe53bc0ad028', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_register` VALUES (421369491834830850, '2024-06-27 12:08:51', '2023-12-07 12:08:51', NULL, NULL, 421369491834830848, NULL, 'vanx_507737118', '071f5212c4332ae6', 'e2e662a36c7564949e26f478fa3b441c', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_register` VALUES (425450591234326530, '2023-12-18 12:05:36', '2023-12-18 12:05:36', NULL, NULL, 425450591234326528, NULL, 'vanx_196011683', NULL, 'e1c81931402dd26336832f2deee9215c', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_register` VALUES (452999623867400194, '2024-03-01 17:49:55', '2024-03-01 17:49:55', NULL, NULL, 452999623867400192, NULL, 'vanx_547434777', '071f5212c4332ae6', '1a63d7967930fd8a49194f04c3a4f7ed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_register` VALUES (471083055386230786, '2024-04-19 11:22:52', '2024-04-19 11:22:52', NULL, NULL, 471083055386230784, NULL, 'vanx_422942060', NULL, '51252a57dbe5d8899e26f478fa3b441c', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_register` VALUES (482646202563723266, '2024-05-20 15:13:46', '2024-05-20 15:13:46', NULL, NULL, 482646202563723264, NULL, 'vanx_146442703', NULL, 'eaba4a3b612e808a3c6551154e4e5471', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_ad_disp_for_user
-- ----------------------------
DROP TABLE IF EXISTS `vanx_ad_disp_for_user`;
CREATE TABLE `vanx_ad_disp_for_user`  (
  `ad_for_user_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '登录用户的id',
  `display_ad_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '弹出广告的类型',
  `display_interval_hours` bigint(0) NULL DEFAULT NULL COMMENT '广告弹出的时间间隔时长单位：小时',
  `ad_object_count` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '红包面额',
  `display_ad_id` bigint(0) NULL DEFAULT NULL COMMENT '弹出广告的id',
  `ad_obj_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告所代表的对象名称（红包、优惠卷、会员卡等）的名称',
  `ad_obj_pop_pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告所代表的对象弹出的时候图片的url',
  `ad_page_route_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告的跳转的页面路径的名称id',
  `ad_page_route` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告的跳转页面路径名称，不是链接url。这个路径是前端设置好的，后端要能拿到所有前端的路径',
  `ad_page_route_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告的跳转页面路径名称，不是链接url。这个路径是前端设置好的，后端要能拿到所有前端的路径',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ad_for_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统将要弹出广告的记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_ad_disp_for_user
-- ----------------------------
INSERT INTO `vanx_ad_disp_for_user` VALUES (497054117034754048, 421054829645824000, '红包', 24, '20.00', 496676838517571584, '红包包', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-28 11:03:50', '2024-06-28 11:03:50', 0, NULL, NULL, NULL);
INSERT INTO `vanx_ad_disp_for_user` VALUES (497054117034754049, 421369491834830848, '红包', 24, '20.00', 496676838517571584, '红包包', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-28 11:03:50', '2024-06-28 11:03:50', 0, NULL, NULL, NULL);
INSERT INTO `vanx_ad_disp_for_user` VALUES (498543010397585408, 421054829645824000, '红包', 24, '2.00', 497160434655232000, '大红包', NULL, NULL, NULL, NULL, NULL, NULL, '2024-07-02 11:21:30', '2024-07-02 11:21:30', 0, NULL, NULL, NULL);
INSERT INTO `vanx_ad_disp_for_user` VALUES (498543010397585409, 421369491834830848, '红包', 24, '2.00', 497160434655232000, '大红包', NULL, NULL, NULL, NULL, NULL, NULL, '2024-07-02 11:21:30', '2024-07-02 11:21:30', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_ad_disp_user_log
-- ----------------------------
DROP TABLE IF EXISTS `vanx_ad_disp_user_log`;
CREATE TABLE `vanx_ad_disp_user_log`  (
  `ad_user_log_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '登录的用户id',
  `display_ad_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '弹出广告的类型：1、红包2、优惠卷3、会员卡等',
  `display_ad_id` bigint(0) NULL DEFAULT NULL COMMENT '弹出广告的id',
  `ad_display_time` datetime(0) NULL DEFAULT NULL COMMENT '弹出广告的弹出时间',
  `does_click_to_claim` tinyint(1) NULL DEFAULT 0 COMMENT '用户是否点击以领取：1、已经点击0、没有点击',
  `does_click_to_close` tinyint(1) NULL DEFAULT 0 COMMENT '用户是否点击以关闭：1、已经点击0、没有点击',
  `does_claim` tinyint(1) NULL DEFAULT 0 COMMENT '用户是否领取：1、已经领取0、没有领取',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ad_user_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统弹出广告的用户端记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_ad_disp_user_log
-- ----------------------------
INSERT INTO `vanx_ad_disp_user_log` VALUES (1, 421054829645824000, '周期性弹出', 497160434655232000, '2024-06-25 09:42:45', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_ad_disp_user_log` VALUES (2, 421369491834830848, '周期性弹出', 497160434655232000, '2024-06-25 09:42:45', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_app_resource_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_app_resource_info`;
CREATE TABLE `vanx_app_resource_info`  (
  `resource_info_id` bigint(0) NOT NULL COMMENT 'id',
  `resource_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '资源的名称',
  `resource_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '资源的编码',
  `resource_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '资源若为图片，即图片的url',
  `resource_url_minio_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '资源图片的minio名称。以便删除',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '序号',
  `resource_templa_id` bigint(0) NULL DEFAULT NULL COMMENT '该资源对应的资源模板id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`resource_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '平台的app端系统资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_app_resource_info
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_app_resource_template
-- ----------------------------
DROP TABLE IF EXISTS `vanx_app_resource_template`;
CREATE TABLE `vanx_app_resource_template`  (
  `resource_templa_id` bigint(0) NOT NULL COMMENT 'id',
  `template_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '模板的名称',
  `template_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '模板的编码',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '序号',
  `theme_id` bigint(0) NULL DEFAULT NULL COMMENT '模板所在的频道对应的id',
  `theme_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '频道名称',
  `is_free` tinyint(1) NULL DEFAULT NULL COMMENT '是否免费',
  `is_member_templa` tinyint(1) NULL DEFAULT NULL COMMENT '该模板是否为会员模板',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`resource_templa_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '平台的app端系统资源模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_app_resource_template
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_app_setting_rules
-- ----------------------------
DROP TABLE IF EXISTS `vanx_app_setting_rules`;
CREATE TABLE `vanx_app_setting_rules`  (
  `app_rule_id` bigint(0) NOT NULL COMMENT 'id',
  `app_rule_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'app系统设置规则名称',
  `app_rule_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'app系统设置规则编码',
  `app_rule_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'app系统设置规则类型',
  `app_rule_icon` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'APP端系统设置规则图标的URL',
  `rule_icon_minio_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'APP端系统设置规则图标的存放在MinIO上面的文件名称',
  `belong_to_column` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '属于哪一个页面(栏目)：1：笔记；2:纪念日；3：事项',
  `parent_app_rule_id` bigint(0) NULL DEFAULT NULL COMMENT '规则父级id，顶级为0',
  `is_leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否为叶子节点',
  `has_rules_value_for_user` tinyint(1) NULL DEFAULT NULL COMMENT '该规则是否在规则取值表中有相应的取值可供用户选择',
  `is_enumeration` tinyint(1) NULL DEFAULT NULL COMMENT '是否枚举属性',
  `option_type` tinyint(1) NULL DEFAULT NULL COMMENT '是否必填',
  `select_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '选择类型：1、单选2、多选',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `add_source` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的类目来源：1、自定义添加2、平台库选择',
  `reject_reason` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类认证失败原因',
  `platform_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`app_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '平台系统设置规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_app_setting_rules
-- ----------------------------
INSERT INTO `vanx_app_setting_rules` VALUES (1, '隐私', NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules` VALUES (2, '隐藏设置', NULL, '笔记;纪念日', NULL, NULL, NULL, 1, 1, 1, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules` VALUES (3, '分享设置', NULL, '视频;图片', NULL, NULL, NULL, 1, 1, 1, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules` VALUES (4, '加密设置', NULL, '笔记;纪念日', NULL, NULL, NULL, 1, 0, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules` VALUES (9, '密码类型', NULL, '启动时加密', NULL, NULL, NULL, 4, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_app_setting_rules_value
-- ----------------------------
DROP TABLE IF EXISTS `vanx_app_setting_rules_value`;
CREATE TABLE `vanx_app_setting_rules_value`  (
  `rule_value_id` bigint(0) NOT NULL COMMENT 'id',
  `app_rule_id` bigint(0) NULL DEFAULT NULL COMMENT 'app系统设置规则id',
  `app_rule_value_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '规则值名称',
  `app_rule_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '规则的取值',
  `app_rule_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '属性值的描述信息',
  `is_default_value` tinyint(1) NULL DEFAULT NULL COMMENT '是否为默认取值',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `value_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '属性取值状态：1、申请2、通过3、驳回4、关闭',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '类目来源：1、自定义添加2、从平台库选择',
  `reject_reason` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类认证失败原因',
  `platform_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`rule_value_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '平台规则取值表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_app_setting_rules_value
-- ----------------------------
INSERT INTO `vanx_app_setting_rules_value` VALUES (1, 2, '不隐藏', '', NULL, 1, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (2, 2, '隐藏', '', NULL, 0, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (3, 3, '公开', '', NULL, 1, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (4, 3, '粉丝', '', NULL, 0, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (5, 3, '好友圈', '', NULL, 0, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (6, 3, '仅自己可见', '', NULL, 0, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (7, 3, '指定好友可见', '', NULL, 0, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (20, 4, '不加密', '', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (21, 4, '启动时加密', '', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (22, 4, '进入应用加密', '', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (23, 4, '查看作品加密', '', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (24, 9, '数字密码', '', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_app_setting_rules_value` VALUES (25, 9, '手势密码', '', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_growth_value_weight
-- ----------------------------
DROP TABLE IF EXISTS `vanx_growth_value_weight`;
CREATE TABLE `vanx_growth_value_weight`  (
  `weight_id` bigint(0) NOT NULL COMMENT 'id',
  `behavior_points_weight` double(10, 2) NULL DEFAULT NULL COMMENT '行为积分占比，或者该积分对应的权重值',
  `shopping_points_weight` double(10, 2) NULL DEFAULT NULL COMMENT '购物积分占比，或者该积分对应的权重值',
  `game_points_weight` double(10, 2) NULL DEFAULT NULL COMMENT '游戏积分占比，或者该积分对应的权重值',
  `level_info_id` bigint(0) NULL DEFAULT NULL COMMENT '该规则使用应该对应的用户等级id',
  `user_level_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '等级的分类：0、成长值等级',
  `user_level_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '级别的名称',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`weight_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_growth_value_weight' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_growth_value_weight
-- ----------------------------
INSERT INTO `vanx_growth_value_weight` VALUES (1, 1.00, 1.00, 1.00, 432173494952820736, '成长值等级', '一级', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_growth_value_weight` VALUES (2, 1.10, 1.10, 1.10, 432173842845171712, '成长值等级', '二级', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_growth_value_weight` VALUES (3, 1.20, 1.20, 1.20, 432173842845171714, '成长值等级', '三级', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_paid_memb_rights_rules
-- ----------------------------
DROP TABLE IF EXISTS `vanx_paid_memb_rights_rules`;
CREATE TABLE `vanx_paid_memb_rights_rules`  (
  `paid_memb_rule_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户的id号',
  `order_id` bigint(0) NULL DEFAULT NULL COMMENT '用户付款的订单id号',
  `member_card_id` bigint(0) NULL DEFAULT NULL COMMENT '系统用户购买的会员卡的id号',
  `rule_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '系统用户购买的会员卡包含的规则名称',
  `rule_category_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设置的规则所属分类的名称',
  `rule_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设置的规则编码',
  `rule_icon_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设置的规则对应的图标url',
  `user_behavior_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的行为类型： 1：登录 ；2：签到；3：观看视频；5：视频点赞；6：视频评论；7：视频打赏；8：视频转发；9：视频收藏；10：发布视频；',
  `user_consume_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户消费类型：1、消费2、非消费',
  `rule_object_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '该规则、该积分类型、该积分段的允许次数',
  `rule_object_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '积分单位类型：1：行为积分；2：购物积分；3：游戏积分；',
  `rule_object_get_points` double(14, 2) NULL DEFAULT NULL COMMENT '该用户行为所获得的积分',
  `multiples_of_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '该规则所定义的对象的数值比正常数值的倍数，即是4倍积分或者3倍积分等',
  `rule_object_id` bigint(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的id',
  `rule_object_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '该规则所定义的对象如果为图片，则存放图片的url',
  `rule_object_sort` tinyint(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的排序号',
  `pay_amount` decimal(14, 2) NULL DEFAULT NULL COMMENT '用户付款金额',
  `pay_period` tinyint(0) NULL DEFAULT NULL COMMENT '用户缴费期间，即缴1个月，缴3个月。。。。。。',
  `pay_time` datetime(0) NULL DEFAULT NULL COMMENT '用户付款时间',
  `use_rule_start_time` datetime(0) NULL DEFAULT NULL COMMENT '用户享有该功能的开始时间',
  `user_rule_end_time` datetime(0) NULL DEFAULT NULL COMMENT '用户享有该规则功能的结束时间',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户权限的来源：1、用户自行付费2、由保险公司开通该用户权限',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`paid_memb_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_platf_user_pay_rules' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_paid_memb_rights_rules
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_platf_menu_cost
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_menu_cost`;
CREATE TABLE `vanx_platf_menu_cost`  (
  `cost_id` bigint(0) NOT NULL COMMENT '支付的id号',
  `menu_id` bigint(0) NULL DEFAULT NULL COMMENT '菜单的id号',
  `menu_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单的标题',
  `cost_start_time` datetime(0) NULL DEFAULT NULL COMMENT '菜单收费的开始时间',
  `cost_end_time` datetime(0) NULL DEFAULT NULL COMMENT '菜单收费的开始时间',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `menu_cost_model` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户缴费信息',
  `other_info_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除',
  PRIMARY KEY (`cost_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '系统菜单费用表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_menu_cost
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_platf_menu_function
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_menu_function`;
CREATE TABLE `vanx_platf_menu_function`  (
  `menu_id` bigint(0) NOT NULL COMMENT '菜单的id号',
  `menu_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单标题',
  `menu_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单类型 G容量',
  `parent_menu_id` bigint(0) NULL DEFAULT NULL COMMENT '菜单父类id',
  `storage_size` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单对应服务器上文件夹的容量大小',
  `is_charged` tinyint(0) NULL DEFAULT NULL COMMENT '是否收费',
  `menu_location` tinyint(0) NULL DEFAULT 0 COMMENT '菜单项在前端界面的位置（0 上部分 1 下部分，默认0）',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `prompt_message` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单提示信息',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `icon_url` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单对应图片的url',
  `request_url` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单请求url',
  `target_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '打开方式menultem页签 menuBlank 新窗口',
  `menu_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单的状态 0 显示 1 隐藏',
  `is_leaf` tinyint(0) NULL DEFAULT 0 COMMENT '是否叶子菜单项（0 不是 1 是 ，默认0）',
  `module_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单所在模块名称',
  `operation_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在菜单的操作名',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除 默认1',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '菜单功能表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_menu_function
-- ----------------------------
INSERT INTO `vanx_platf_menu_function` VALUES (1, '存储空间', '容量G', 0, '20', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_platf_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_menu_permission`;
CREATE TABLE `vanx_platf_menu_permission`  (
  `permission_id` bigint(0) NOT NULL COMMENT '权限id号',
  `role_id` bigint(0) NULL DEFAULT NULL COMMENT '角色id号',
  `menu_id` bigint(0) NULL DEFAULT NULL COMMENT '菜单id号',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除 默认 1',
  PRIMARY KEY (`permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '菜单权限功能表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_menu_permission
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_platf_poins_rights_rules
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_poins_rights_rules`;
CREATE TABLE `vanx_platf_poins_rights_rules`  (
  `poins_rule_id` bigint(0) NOT NULL COMMENT '用户积分规则id',
  `rule_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置的规则名称',
  `rule_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置的编码规则',
  `user_behavior_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户的行为类型： 1：登录 ；2：签到；3：观看视频；5：视频点赞；6：视频评论；7：视频打赏；8：视频转发；9：视频收藏；10：发布视频；',
  `rule_category_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置的规则所属分类的名称',
  `rule_category_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属类别图标url',
  `rule_category_minio_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属类别图标minio名称，用于删除',
  `user_consume_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户的消费类型： 1：消费 ；2：非消费',
  `level_info_id` bigint(0) NULL DEFAULT NULL COMMENT '该规则对应的用户等级id',
  `user_level_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '级别分类：0、成长值等级1、购物等级2、游戏等级',
  `user_level_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '级别的名称',
  `rule_object_value` bigint(0) NULL DEFAULT NULL COMMENT '该规则、该积分类型、该积分段的允许次数',
  `rule_obj_value_time_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该规则、该积分类型、该积分段的允许次数的时间间隔单位，如：天、周、月等',
  `value_change_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '增加或减少某积分',
  `rule_object_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分单位类型：1：行为积分；2：购物积分；3：游戏积分；',
  `rule_object_get_points` double(14, 2) NULL DEFAULT NULL COMMENT '该用户行为所获得的积分',
  `rule_object_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该规则所定义的对象如果为图片，则存放图片的url',
  `rule_icon_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置的规则对应的图标url',
  `rule_object_sort` int(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的排序号',
  `multiples_of_value` bigint(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的数值比正常数值的倍数，即是4倍积分或者3倍积分等',
  `rule_object_id` bigint(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`poins_rule_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_poins_rights_rules
-- ----------------------------
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (1, '发视频', '00001', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, '', '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 11:55:40', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (2, '发视频', '00002', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, '', '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 13:50:00', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (3, '发视频', '00003', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068801, '', '购物一级', 10, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (4, '发视频', '00004', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068802, '', '购物二级', 20, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (5, '发视频', '00005', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068811, '', '游戏一级', 10, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (6, '发视频', '00006', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068812, '', '游戏二级', 20, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (7, '发照片', '00007', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, '', '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 11:55:44', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (8, '发照片', '00008', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, '', '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 13:49:59', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (9, '发照片', '00009', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068801, '', '购物一级', 10, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (10, '发照片', '00010', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068802, '', '购物二级', 20, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (11, '发照片', '00011', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068811, '', '游戏一级', 10, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (12, '发照片', '00012', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068812, '', '游戏二级', 20, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (13, '加纪念日', '00013', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, '', '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 11:55:46', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (14, '加纪念日', '00014', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, '', '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 13:49:58', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (15, '加纪念日', '00015', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068801, '', '购物一级', 10, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (16, '加纪念日', '00016', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068802, '', '购物二级', 20, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (17, '加纪念日', '00017', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068811, '', '游戏一级', 10, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (18, '加纪念日', '00018', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068812, '', '游戏二级', 20, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (19, '发文章', '00019', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, '', '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 11:55:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (20, '发文章', '00020', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, '', '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-12-22 13:49:57', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (21, '发文章', '00021', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068801, '', '购物一级', 10, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (22, '发文章', '00022', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068802, '', '购物二级', 20, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (23, '发文章', '00023', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068811, '', '游戏一级', 10, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (24, '发文章', '00024', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068812, '', '游戏二级', 20, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-18 13:53:49', '2023-09-18 13:53:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (25, '发视频', '00025', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, '', '行为三级', 30, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:41:32', '2023-09-19 10:41:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (26, '发视频', '00026', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068803, '', '购物三级', 30, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (27, '发视频', '00027', '发视频', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068813, '', '游戏三级', 30, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (28, '发照片', '00028', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, '', '行为三级', 30, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:41:32', '2023-09-19 10:41:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (29, '发照片', '00027', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068803, '', '购物三级', 30, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (30, '发照片', '00028', '发照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068813, '', '游戏三级', 30, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (31, '加纪念日', '00029', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, '', '行为三级', 30, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:41:32', '2023-09-19 10:41:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (32, '加纪念日', '00030', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068803, '', '购物三级', 30, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (33, '加纪念日', '00031', '加纪念日', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068813, '', '游戏三级', 30, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (34, '发文章', '00032', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, '', '行为三级', 30, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:41:32', '2023-09-19 10:41:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (35, '发文章', '00033', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068803, '', '购物三级', 30, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (36, '发文章', '00034', '发文章', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068813, '', '游戏三级', 30, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, '2023-09-19 10:42:17', '2023-09-19 10:42:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (37, '加笔记图片', '00035', '加笔记图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, '', '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 11:55:49', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (38, '加笔记图片', '00036', '加笔记图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, '', '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:55', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (39, '加笔记图片', '00037', '加笔记图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, '', '行为三级', 6, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (43, '加笔记', '00038', '加笔记', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 4, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (44, '加纪念日图片', '00039', '加纪念日图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (45, '加纪念日图片', '00040', '加纪念日图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (46, '加精选照片', '00041', '加精选照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 6, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (47, '加精选照片', '00042', '加精选照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 8, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (48, '加精选照片', '00043', '加精选照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 10, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (49, '加事件', '00044', '加事件', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 10, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (50, '加事件', '00045', '加事件', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 20, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (51, '加事件', '00046', '加事件', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 30, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (52, '加事件图片', '00047', '加事件图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 5, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (53, '加事件图片', '00048', '加事件图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 10, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (54, '加事件图片', '00049', '加事件图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 15, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (55, '加个人封面照片', '00050', '加个人封面照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 6, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (56, '加个人封面照片', '00051', '加个人封面照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 8, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (57, '加个人封面照片', '00052', '加个人封面照片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 10, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (58, '加倒计时', '00053', '加倒计时', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 8, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (59, '加倒计时', '00054', '加倒计时', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 10, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (60, '加倒计时', '00055', '加倒计时', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 12, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (61, '加倒计时图片', '00056', '加倒计时图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 6, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (62, '加倒计时图片', '00057', '加倒计时图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 8, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (63, '加倒计时图片', '00058', '加倒计时图片', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 10, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (64, '个人存储空间', '00059', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 6442450944, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (65, '个人存储空间', '00060', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 8589934592, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (66, '个人存储空间', '00061', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 10737418240, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (67, '个人存储空间', '00062', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068801, NULL, '购物一级', 6442450944, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (68, '个人存储空间', '00063', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068802, NULL, '购物二级', 8589934592, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (69, '个人存储空间', '00064', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068803, NULL, '购物三级', 10737418240, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (70, '个人存储空间', '00065', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068811, NULL, '游戏一级', 6442450944, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (71, '个人存储空间', '00066', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068812, NULL, '游戏二级', 8589934592, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (72, '个人存储空间', '00067', '个人存储空间', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068813, NULL, '游戏三级', 10737418240, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (73, '发表照片的最大图片数', '00068', '发表照片的最大图片数', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096438992470016, NULL, '行为一级', 2, '天', '增加', '行为积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (74, '发表照片的最大图片数', '00069', '发表照片的最大图片数', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096709575409664, NULL, '行为二级', 3, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (75, '发表照片的最大图片数', '00070', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068800, NULL, '行为三级', 4, '天', '增加', '行为积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (76, '发表照片的最大图片数', '00071', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068801, NULL, '购物一级', 2, '天', '增加', '购物积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (77, '发表照片的最大图片数', '00072', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068802, NULL, '购物二级', 3, '天', '增加', '购物积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (78, '发表照片的最大图片数', '00073', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068803, NULL, '购物三级', 4, '天', '增加', '购物积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (79, '发表照片的最大图片数', '00074', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068811, NULL, '游戏一级', 2, '天', '增加', '游戏积分', 3.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (80, '发表照片的最大图片数', '00075', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068812, NULL, '游戏二级', 3, '天', '增加', '游戏积分', 5.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (81, '发表照片的最大图片数', '00076', '发表照片的最大图片数', '云盘容量', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719207406345.png', NULL, '非消费', 418096885669068813, NULL, '游戏三级', 4, '天', '增加', '游戏积分', 7.00, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (82, '购买会员卡', '00077', '购买会员卡', '会员折扣价', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208554021.png', '', '消费', 418096885669068801, NULL, '购物一级', NULL, '月', '增加', '购物积分', 0.20, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (83, '购买会员卡', '00078', '购买会员卡', '会员折扣价', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208554021.png', '', '消费', 418096885669068802, NULL, '购物二级', NULL, '月', '增加', '购物积分', 0.40, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (84, '购买会员卡', '00079', '购买会员卡', '会员折扣价', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208554021.png', '', '消费', 418096885669068803, NULL, '购物三级', NULL, '月', '增加', '购物积分', 0.60, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (85, '加笔记', '00080', '加笔记', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', '', '非消费', 418096885669068800, NULL, '行为三级', 10, '天', '增加', '行为积分', 7.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_poins_rights_rules` VALUES (426930817353154560, '加笔记', '00081', '加笔记', '功能特权', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-24/1719208644915.png', '', '非消费', 418096709575409664, '', '行为二级', 8, '天', '增加', '行为积分', 5.00, '', '', NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-22 11:49:38', '2023-12-22 11:49:38', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_points_category
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_points_category`;
CREATE TABLE `vanx_platf_points_category`  (
  `points_categ_id` bigint(0) NOT NULL COMMENT 'id',
  `internal_point_categ_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分类目名称，即：行为积分、购物积分、游戏积分',
  `point_catg_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户端显示的名称',
  `point_categ_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类类目编码',
  `categ_pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分类目的图片路径url',
  `categ_pic_minio_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '积分类目的图片存放的minio路径名',
  `is_home_show` tinyint(1) NULL DEFAULT NULL COMMENT '是否首页前台显示',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数',
  `category_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目状态: 申请中、通过、驳回、关闭',
  `add_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源：自定义添加、从商品平台库选择',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类认证失败原因',
  `platform_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复核人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`points_categ_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Vanx系统的积分的类别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_points_category
-- ----------------------------
INSERT INTO `vanx_platf_points_category` VALUES (1, '行为积分', NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/热豆.png', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_points_category` VALUES (2, '游戏积分', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_points_category` VALUES (3, '购物积分', NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/金豆.png', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_roles
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_roles`;
CREATE TABLE `vanx_platf_roles`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色id号',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `role_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '角色名称；1 普通代理人用户 2 用户所在系统管理员用户；3 用户所在系统超级管理用户；4平台的管理员用户；5 用户所在平台的超级管理员用户',
  `role_sort` int(0) NULL DEFAULT NULL COMMENT '角色的显示顺序',
  `role_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '角色状态 1 正常 2 禁用 3 其他',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '角色状态 1 用户自定义增加 2 平台增加',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除 0未删除 1 删除 默认 0',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_roles
-- ----------------------------
INSERT INTO `vanx_platf_roles` VALUES (1, '2023-07-07 10:16:21', NULL, NULL, NULL, '普通买家用户', 1, '1', NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_roles` VALUES (2, '2023-08-29 10:23:34', NULL, NULL, NULL, '平台超级管理用户', 2, '1', NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_roles` VALUES (3, NULL, NULL, NULL, NULL, '卖家普通管理用户', 4, '1', NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_roles` VALUES (4, NULL, NULL, NULL, NULL, '卖家区域管理用户', 5, '1', NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_roles` VALUES (5, NULL, NULL, NULL, NULL, '卖家超级管理用户', 6, '1', NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_roles` VALUES (421054829645824001, '2023-08-29 16:21:46', NULL, NULL, NULL, '平台普通管理用户', 3, '1', NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_platf_user_label
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_user_label`;
CREATE TABLE `vanx_platf_user_label`  (
  `user_label_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `label_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签的名称',
  `label_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签的类型',
  `label_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签的编码',
  `icon_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签图标路径的url',
  `backg_image_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签的背景图片路径url',
  `backg_colour` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '标签的背景颜色',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类的类目来源：1、自定义添加2、从平台商品库选择',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_label_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_platf_user_label' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_user_label
-- ----------------------------
INSERT INTO `vanx_platf_user_label` VALUES (1, NULL, '上网', NULL, NULL, NULL, NULL, '#5AD79F', NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_label` VALUES (2, NULL, '游戏', NULL, NULL, NULL, NULL, '#5EC7E7', NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_label` VALUES (3, NULL, '聚餐', NULL, NULL, NULL, NULL, '#F7B885', NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_user_level_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_user_level_info`;
CREATE TABLE `vanx_platf_user_level_info`  (
  `level_info_id` bigint(0) NOT NULL COMMENT 'id',
  `user_level_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的级别名称',
  `user_level_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户的级别编码',
  `required_min_poins` bigint(0) NULL DEFAULT NULL COMMENT '级别对应分值的最小值',
  `required_max_poins` bigint(0) NULL DEFAULT NULL COMMENT '级别对应分值的最大值',
  `user_level_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '等级的类别：0、成长值等级1、购物等级3、游戏等级',
  `user_level_icon_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '等级图标minio文件名，用于删除',
  `user_level_icon` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '等级图标url',
  `add_source` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '等级添加来源：1、平台添加2、自定义添加',
  `reject_reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '未受理的原因',
  `check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `check_user_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '审核人名字',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`level_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_platf_user_level_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_user_level_info
-- ----------------------------
INSERT INTO `vanx_platf_user_level_info` VALUES (418096438992470016, '一级', '001', 0, 20, '行为等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L1.png', '平台添加', NULL, NULL, NULL, NULL, 390284142044020736, NULL, '2023-11-28 16:27:44', '2024-01-05 15:15:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096709575409664, '二级', '002', 20, 40, '行为等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L2.png', '平台添加', NULL, NULL, NULL, NULL, 390284142044020736, NULL, '2023-11-28 16:28:47', '2023-12-21 14:47:27', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068800, '三级', '003', 40, 100000, '行为等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L3.png', '平台添加', NULL, NULL, NULL, NULL, 390284142044020736, NULL, '2023-11-28 16:29:27', '2023-12-22 13:49:34', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068801, '一级', '101', 0, 20, '购物等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L1.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:33', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068802, '二级', '102', 20, 40, '购物等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L2.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:32', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068803, '三级', '103', 40, 100000, '购物等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L3.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:41', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068811, '一级', '201', 0, 20, '游戏等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L1.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068812, '二级', '202', 20, 40, '游戏等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L2.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:38', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (418096885669068813, '三级', '203', 40, 100000, '游戏等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L3.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-12-22 13:49:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (432173494952820736, '一级', '001', 0, 10, '成长值等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L1.png', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2024-01-05 14:53:53', '2024-01-05 15:15:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (432173842845171712, '二级', '002', 10, 20, '成长值等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L2.png', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2024-01-05 14:55:15', '2024-01-05 14:55:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (432173842845171714, '三级', '003', 20, 100000, '成长值等级', NULL, 'http://101.43.154.175:9003/zx-vanx/system-file/L3.png', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_user_level_info` VALUES (495630996801093632, '四级', '0004', 100000, 200000, '行为等级', NULL, 'http://101.43.154.175:9003/zx-vanx//2024-06-24/1719212484790.png', NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-24 15:01:25', '2024-06-24 15:01:25', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_user_roles
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_user_roles`;
CREATE TABLE `vanx_platf_user_roles`  (
  `user_role_id` bigint(0) NOT NULL COMMENT '用户角色id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id，及创建者用户的id号码',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户的id号',
  `role_id` bigint(0) NULL DEFAULT NULL COMMENT '角色id号',
  `is_active` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户是否活跃，及用户的角色状态',
  `user_role_status` tinyint(0) NULL DEFAULT NULL COMMENT '用户角色审核状态，0驳回 1 待审核 2 审核通过',
  `reject_reason` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '审核失败原因',
  `platform_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id号',
  `platform_user_id` bigint(0) NULL DEFAULT NULL COMMENT '批注人id号',
  `pass_time` datetime(0) NULL DEFAULT NULL COMMENT '审核通过时间',
  `other_info_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除，0 未删除 1 删除 默认 0',
  PRIMARY KEY (`user_role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_user_roles
-- ----------------------------
INSERT INTO `vanx_platf_user_roles` VALUES (421054829645824006, '2023-12-06 15:47:48', '2023-12-06 15:47:48', NULL, NULL, 421054829645824000, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (421369491834830854, '2023-12-07 12:08:51', '2023-12-07 12:08:51', NULL, NULL, 421369491834830848, 3, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (421369491834830899, '2023-12-12 11:04:45', '2023-12-12 11:04:51', NULL, NULL, 421369491834830848, 2, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (425450591234326534, '2023-12-18 12:05:36', '2023-12-18 12:05:36', NULL, NULL, 425450591234326528, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (452999623867400198, '2024-03-01 17:49:55', '2024-03-01 17:49:55', NULL, NULL, 452999623867400192, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (459936283813249030, '2024-03-20 10:27:42', '2024-03-20 10:27:42', NULL, NULL, 459936283813249024, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (471083059681198080, '2024-04-19 11:22:53', '2024-04-19 11:22:53', NULL, NULL, 471083055386230784, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_user_roles` VALUES (482646206858690560, '2024-05-20 15:13:47', '2024-05-20 15:13:47', NULL, NULL, 482646202563723264, 1, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_user_attention_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_attention_relat`;
CREATE TABLE `vanx_user_attention_relat`  (
  `user_attention_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '当前用户id',
  `attention_user_id` bigint(0) NULL DEFAULT NULL COMMENT '偶像id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_attention_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_attention_relat
-- ----------------------------
INSERT INTO `vanx_user_attention_relat` VALUES (483038586480918529, 421054829645824000, 425450591234326528, NULL, NULL, '2024-05-21 16:36:26', '2024-05-21 16:36:26', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_attention_relat` VALUES (483038917193400323, 425450591234326528, 421054829645824000, NULL, NULL, '2024-05-21 16:37:42', '2024-05-21 16:37:42', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_follower_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_follower_relat`;
CREATE TABLE `vanx_user_follower_relat`  (
  `follower_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '当前用户id',
  `follower_user_id` bigint(0) NULL DEFAULT NULL COMMENT '粉丝的id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`follower_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_follower_relat
-- ----------------------------
INSERT INTO `vanx_user_follower_relat` VALUES (483038586480918528, 425450591234326528, 421054829645824000, NULL, NULL, '2024-05-21 16:36:26', '2024-05-21 16:36:26', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_follower_relat` VALUES (483038917193400322, 421054829645824000, 425450591234326528, NULL, NULL, '2024-05-21 16:37:42', '2024-05-21 16:37:42', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_friend_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_friend_info`;
CREATE TABLE `vanx_user_friend_info`  (
  `friend_info_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '当前用户id',
  `user_nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户的昵称',
  `friend_user_id` bigint(0) NULL DEFAULT NULL COMMENT '该用户好友的id',
  `friend_user_nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户好友的昵称',
  `friend_user_remark_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户好友的姓名',
  `friend_photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户好友头像url',
  `friend_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户好友状态 1、申请 2、已添加 3、未同意',
  `friend_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户好友的类型 1、普通成员 2、团队组织创建者 3、团队管理协助者',
  `reason_for_add` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请加入该好友的理由',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`friend_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_friend_info
-- ----------------------------
INSERT INTO `vanx_user_friend_info` VALUES (1, 421054829645824000, NULL, 421369491834830848, NULL, NULL, NULL, '已添加', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_friend_info` VALUES (2, 421369491834830848, NULL, 421054829645824000, NULL, NULL, NULL, '已添加', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_friend_info` VALUES (483038917193400320, 425450591234326528, NULL, 421054829645824000, NULL, NULL, NULL, '已添加', NULL, NULL, NULL, NULL, '2024-05-21 16:37:42', '2024-05-21 16:37:42', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_friend_info` VALUES (483038917193400321, 421054829645824000, NULL, 425450591234326528, NULL, NULL, NULL, '已添加', NULL, NULL, NULL, NULL, '2024-05-21 16:37:42', '2024-05-21 16:37:42', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_growth_log
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_growth_log`;
CREATE TABLE `vanx_user_growth_log`  (
  `growth_log_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT 'userId',
  `user_points` double(14, 2) NULL DEFAULT NULL COMMENT '此次获取的成长值的取值',
  `value_change_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '此次获取的成长值的改变类型：增加、减少',
  `user_behavior_type` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户行为类型',
  `user_points_type` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户积分类型',
  `related_media_id` bigint(0) NULL DEFAULT NULL COMMENT '如果积分变动与相对应的视频、照片或者博客相关，可以记录相关元素的id',
  `related_note_id` bigint(0) NULL DEFAULT NULL COMMENT '如果积分变动与相对应的视频、照片或者博客相关，可以记录相关元素的id',
  `related_item_id` bigint(0) NULL DEFAULT NULL COMMENT '如果积分变动与相对应的视频、照片或者博客相关，可以记录相关元素的id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`growth_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_growth_log
-- ----------------------------
INSERT INTO `vanx_user_growth_log` VALUES (431003258098581504, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-02 11:12:46', '2024-01-02 11:12:46', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (431003356882829312, 421054829645824000, 3.00, '增加', '购买会员卡', '购物积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-02 11:13:10', '2024-01-02 11:13:10', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (431070474336763904, 421054829645824000, 3.00, '增加', '购买会员卡', '购物积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-02 15:33:37', '2024-01-02 15:33:37', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (431070774984474624, 421054829645824000, 3.00, '增加', '购买会员卡', '购物积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-02 15:34:46', '2024-01-02 15:34:46', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (431070942488199168, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-02 15:35:25', '2024-01-02 15:35:25', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (432207970655305728, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-05 17:07:41', '2024-01-05 17:07:41', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433214461881384960, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:13:23', '2024-01-08 10:13:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433214560665632768, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:13:45', '2024-01-08 10:13:45', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433214689514651648, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:14:16', '2024-01-08 10:14:16', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433215982299807744, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:19:17', '2024-01-08 10:19:17', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433216149803532288, 421054829645824000, 3.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:19:56', '2024-01-08 10:19:56', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433216347372027904, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:20:42', '2024-01-08 10:20:42', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433216815523463168, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:22:30', '2024-01-08 10:22:30', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433216854178168832, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:22:39', '2024-01-08 10:22:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433220925807165440, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:38:27', '2024-01-08 10:38:27', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433221050361217024, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:38:57', '2024-01-08 10:38:57', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433221093310889984, 421054829645824000, 5.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:39:06', '2024-01-08 10:39:06', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433221170620301312, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 10:39:24', '2024-01-08 10:39:24', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433226878631837696, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:01:34', '2024-01-08 11:01:34', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433228742647644160, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:08:48', '2024-01-08 11:08:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433228815662088192, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:09:04', '2024-01-08 11:09:04', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433232651067883520, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:23:57', '2024-01-08 11:23:57', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433233553011015680, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:27:28', '2024-01-08 11:27:28', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433235837933617152, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:36:20', '2024-01-08 11:36:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433236211595771904, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:37:47', '2024-01-08 11:37:47', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433236933150277632, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:40:35', '2024-01-08 11:40:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433237010459688960, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:40:53', '2024-01-08 11:40:53', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433237083474132992, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 11:41:09', '2024-01-08 11:41:09', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433260332132106240, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:11:23', '2024-01-08 13:11:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433260765923803136, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:13:04', '2024-01-08 13:13:04', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433260890477854720, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:13:33', '2024-01-08 13:13:33', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433261057981579264, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:14:12', '2024-01-08 13:14:12', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433261324269551616, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:15:14', '2024-01-08 13:15:14', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433261959924711424, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:17:41', '2024-01-08 13:17:41', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433262282047258624, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:18:57', '2024-01-08 13:18:57', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433263656436793344, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:24:16', '2024-01-08 13:24:16', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433265215509921792, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:30:20', '2024-01-08 13:30:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433265267049529344, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:30:31', '2024-01-08 13:30:31', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433265374423711744, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:30:56', '2024-01-08 13:30:56', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433266787467952128, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:36:25', '2024-01-08 13:36:25', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433266834712592384, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:36:36', '2024-01-08 13:36:36', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433266877662265344, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:36:47', '2024-01-08 13:36:47', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433272594263736320, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:58:58', '2024-01-08 13:58:58', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (433272620033540096, 421054829645824000, 7.00, '增加', '加笔记获得积分', '行为积分', NULL, NULL, NULL, 421054829645824000, NULL, '2024-01-08 13:59:03', '2024-01-08 13:59:03', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490832642158133248, 421054829645824000, 4.00, '增加', '加笔记', '行为积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 16:41:21', '2024-06-11 16:41:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490845088973357056, 421054829645824000, 3.00, '增加', '加笔记', '行为积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 17:29:38', '2024-06-11 17:29:38', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490847172032495616, 421054829645824000, 4.00, '增加', '购买会员卡', '购物积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 17:37:43', '2024-06-11 17:37:43', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490848658091180032, 421054829645824000, 3.00, '增加', '加笔记', '行为积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 17:43:29', '2024-06-11 17:43:29', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490850011005878272, 421054829645824000, 4.00, '增加', '购买会员卡', '购物积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 17:48:45', '2024-06-11 17:48:45', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_growth_log` VALUES (490850075430387712, 421054829645824000, 10.00, '增加', '购买会员卡', '购物积分', 0, 0, NULL, 421054829645824000, NULL, '2024-06-11 17:49:00', '2024-06-11 17:49:00', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_identity_change
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_identity_change`;
CREATE TABLE `vanx_user_identity_change`  (
  `user_identity_set_id` bigint(0) NOT NULL COMMENT 'id',
  `user_iden_change_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户身份改变或切换名称',
  `change_interval_hours` bigint(0) NULL DEFAULT NULL COMMENT '用户身份改变或切换时间间隔时长：单位：小时',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_identity_set_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户身份改变系统设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_identity_change
-- ----------------------------
INSERT INTO `vanx_user_identity_change` VALUES (1, '新人用户', 72, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_change` VALUES (2, '新升级用户', 3, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_change` VALUES (3, '新付费用户', 4, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_identity_log
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_identity_log`;
CREATE TABLE `vanx_user_identity_log`  (
  `user_identity_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `user_identi_start_time` datetime(0) NULL DEFAULT NULL COMMENT '用户该身份开始时间',
  `user_identi_end_time` datetime(0) NULL DEFAULT NULL COMMENT '用户该身份结束时间',
  `user_categ_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户级别类型',
  `user_categ_level_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户分类中的级别类型：如果是升级用户。该字段取值：成长值等级，默认为用户成长值等级；如果付费用户，该字段取值；会员卡类型：黄金vip、铂金VIP',
  `user_level_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相应的级别名称',
  `level_info_id` bigint(0) NULL DEFAULT NULL COMMENT '等级id',
  `is_valid` tinyint(1) NULL DEFAULT 0 COMMENT '该身份是否有效',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_identity_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户身份记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_identity_log
-- ----------------------------
INSERT INTO `vanx_user_identity_log` VALUES (1, 421054829645824000, NULL, NULL, '升级类用户', '成长值等级', '三级', 432173842845171714, 1, NULL, NULL, '2024-07-01 15:57:38', '2024-07-01 15:57:41', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (2, 421369491834830848, NULL, NULL, '升级类用户', '成长值等级', '一级', 432173494952820736, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (3, 425450591234326528, NULL, NULL, '升级类用户', '成长值等级', '一级', 432173494952820736, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (4, 452999623867400192, NULL, NULL, '升级类用户', '成长值等级', '一级', 432173494952820736, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (5, 471083055386230784, NULL, NULL, '升级类用户', '成长值等级', '一级', 432173494952820736, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (6, 482646202563723264, NULL, NULL, '升级类用户', '成长值等级', '一级', 432173494952820736, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_identity_log` VALUES (7, 421054829645824000, NULL, NULL, '付费类用户', '铂金VIP', NULL, NULL, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_inbox_timeline
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_inbox_timeline`;
CREATE TABLE `vanx_user_inbox_timeline`  (
  `user_inbox_id` bigint(0) NOT NULL COMMENT 'id',
  `receive_user_id` bigint(0) NULL DEFAULT NULL COMMENT '当前接收该发布内容的用户id',
  `publish_user_id` bigint(0) NULL DEFAULT NULL COMMENT '该用户关注的人的id，即该用户偶像的id',
  `publish_content_id` bigint(0) NULL DEFAULT NULL COMMENT '该用户关注的内容id，即是微博、文章、视频、图片id',
  `publish_content_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该用户关注的内容的类型。即微博、文章、视频、照片',
  `is_own` tinyint(0) NULL DEFAULT NULL COMMENT '是否是自己发的，0不是自己发的，1是自己发的，默认0',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_inbox_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_inbox_timeline
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_user_label_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_label_relat`;
CREATE TABLE `vanx_user_label_relat`  (
  `user_label_relat_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户id',
  `label_id` bigint(0) NULL DEFAULT NULL COMMENT '用户对应的标签id',
  `label_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户对应的标签名称',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_label_relat_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_user_label_relat' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_label_relat
-- ----------------------------
INSERT INTO `vanx_user_label_relat` VALUES (454821639188676608, 421054829645824000, 1, '上网', NULL, NULL, NULL, '2024-03-06 15:40:16', '2024-03-06 15:40:16', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (454824753039966208, 421054829645824000, 1, '上网', NULL, NULL, NULL, '2024-03-06 15:52:20', '2024-03-06 15:52:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (454824753039966209, 421054829645824000, 2, '游戏', NULL, NULL, NULL, '2024-03-06 15:52:20', '2024-03-06 15:52:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (454824753039966210, 421054829645824000, 3, '聚餐', NULL, NULL, NULL, '2024-03-06 15:52:21', '2024-03-06 15:52:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (457068254976835584, 425450591234326528, 1, '上网', NULL, NULL, NULL, '2024-03-12 16:58:16', '2024-03-12 16:58:16', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (457068259271802880, 425450591234326528, 2, '游戏', NULL, NULL, NULL, '2024-03-12 16:58:18', '2024-03-12 16:58:18', 0, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (457068263566770176, 425450591234326528, 3, '聚餐', NULL, NULL, NULL, '2024-03-12 16:58:19', '2024-03-12 16:58:19', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (459945651136921602, 459936283813249024, 1, '上网', NULL, NULL, NULL, '2024-03-20 11:04:02', '2024-03-20 11:04:02', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (459945698381561856, 459936283813249024, 1, '上网', NULL, NULL, NULL, '2024-03-20 11:04:13', '2024-03-20 11:04:13', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (459945999029272576, 459936283813249024, 1, '上网', NULL, NULL, NULL, '2024-03-20 11:05:23', '2024-03-20 11:05:23', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (459946011914174464, 459936283813249024, 1, '上网', NULL, NULL, NULL, '2024-03-20 11:05:26', '2024-03-20 11:05:26', 1, NULL, NULL, NULL);
INSERT INTO `vanx_user_label_relat` VALUES (459947519447695360, 459936283813249024, 1, '上网', NULL, NULL, NULL, '2024-03-20 11:11:17', '2024-03-20 11:11:17', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_user_media_view
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_media_view`;
CREATE TABLE `vanx_user_media_view`  (
  `media_view_id` bigint(0) NOT NULL COMMENT 'id',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '观赏该视频或图片的用户id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '该用户商铺的id',
  `duration_of_view` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '观赏该视频或图片的时长。0、很短时间1、短时间2、一般时长3、较长时长4、长时间',
  `user_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户登录的设备ip',
  `media_id` bigint(0) NULL DEFAULT NULL COMMENT '被观赏的视频或图片的id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`media_view_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_user_media_view
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_user_priv_setting
-- ----------------------------
DROP TABLE IF EXISTS `vanx_user_priv_setting`;
CREATE TABLE `vanx_user_priv_setting`  (
  `user_setting_id` bigint(0) NOT NULL COMMENT '主键ID',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户ID',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '最后修改者用户ID',
  `user_id` bigint(0) NULL DEFAULT NULL COMMENT '用户ID',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '用户商铺的ID',
  `rule_id` bigint(0) NULL DEFAULT NULL COMMENT '用户使用的规则ID',
  `rule_value_id` bigint(0) NULL DEFAULT NULL COMMENT '规则值ID',
  `rule_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户使用的规则名称',
  `rule_value_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则值名称',
  `rule_object_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该规则所定义的用户能够得到的对象的类型：笔记、纪念日、事项',
  `rule_object_id` bigint(0) NULL DEFAULT NULL COMMENT '该规则所定义的对象的ID',
  `rule_value_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户使用的规则的取值类型',
  `rule_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户使用的规则的取值',
  `rule_value_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户使用的规则的取值list',
  `sort_number` int(0) NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息one',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息two',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '是否删除（0：未删除、1：已删除、默认：0）',
  PRIMARY KEY (`user_setting_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of vanx_user_priv_setting
-- ----------------------------
INSERT INTO `vanx_user_priv_setting` VALUES (1, NULL, NULL, NULL, NULL, 421054829645824000, NULL, 3, 4, '分享设置', '粉丝', '视频', 387682813496819715, '', '', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_user_priv_setting` VALUES (2, NULL, NULL, NULL, NULL, 421054829645824000, NULL, 2, 2, '隐藏设置', '隐藏', '', NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO `vanx_user_priv_setting` VALUES (3, NULL, NULL, NULL, NULL, 421054829645824000, NULL, 4, 21, '加密设置', '启动时加密', NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_user_priv_setting` VALUES (4, NULL, NULL, NULL, NULL, 421054829645824000, NULL, 9, 24, '密码类型', '手势密码', NULL, NULL, '启动时加密', '123456', NULL, NULL, NULL, NULL, NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;
