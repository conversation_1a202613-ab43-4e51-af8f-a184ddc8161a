/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80031
 Source Host           : *************:3306
 Source Schema         : vanx_shop_item

 Target Server Type    : MySQL
 Target Server Version : 80031
 File Encoding         : 65001

 Date: 02/07/2024 11:22:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for vanx_item_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_item_info`;
CREATE TABLE `vanx_item_info`  (
  `item_id` bigint(0) NOT NULL COMMENT 'id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺的id号',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家(商家)的id号',
  `item_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺所起的 名称(即，平台上的商品标题)',
  `item_sub_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺所起的子名称(即，平台上的商品标题)',
  `shop_internal_item_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商是 的店铺在自己内部系统上所起的名称',
  `shop_simple_item_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的 店铺所起的商品简称名称',
  `shop_simple_item_symbol` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺所起的助记符，一般指拼音首字母',
  `default_sku_id` bigint(0) NULL DEFAULT NULL COMMENT '商品对应的默认SKU的id号',
  `attribute_template_id` bigint(0) NULL DEFAULT NULL COMMENT '商品对应的属性种类模板的ID号',
  `item_advert` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的广告词',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的来源: 1自定义添加2: 从平台商品库选择',
  `is_copied` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品是否: 1未加入商品库 2 已加入平台商品库',
  `platform_item_id` bigint(0) NULL DEFAULT NULL COMMENT '此商品对应于平台商品的ld;平台商品ID，只有add source为2时值才有意义',
  `platform_link_status` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '与平台商品库关联状态: 1: 未符合待入库2:待入库3: 已入库4: 删除',
  `recommend_value` int(0) NULL DEFAULT NULL COMMENT '推荐指数',
  `is_sale` tinyint(1) NULL DEFAULT 0 COMMENT '商品是否上架',
  `keywords` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的关键字，便于检索',
  `on_sale_time` datetime(0) NULL DEFAULT NULL COMMENT '上架时间',
  `off_sale_time` datetime(0) NULL DEFAULT NULL COMMENT '下架时间',
  `operator` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作方，1: 商家2:平台',
  `is_sale_promotion` tinyint(1) NULL DEFAULT NULL COMMENT '商品是否促销',
  `is_points_exchange` tinyint(1) NULL DEFAULT 0 COMMENT '商品是否可以积分换购',
  `is_enable_spec` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用规格',
  `timing_on_sale` datetime(0) NULL DEFAULT NULL COMMENT '定时上架，为空则表示未设置定时上架',
  `timing_off_sale` datetime(0) NULL DEFAULT NULL COMMENT '定时下架，为空则表示未设置定时下架',
  `product_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的货号',
  `product_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的编码',
  `product_unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的单位; 比如:袋，瓶...',
  `product_origin_cn` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的产地(中文)',
  `product_origin_en` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的产地(英文)',
  `has_price` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '是否有报价: 1: 有价格;2: 暂无报价',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `platform_first_cid` bigint(0) NULL DEFAULT NULL COMMENT '平台一级商品类目ID',
  `platform_second_cid` bigint(0) NULL DEFAULT NULL COMMENT '平台二级商品类目ID',
  `platform_third_cid` bigint(0) NULL DEFAULT NULL COMMENT '平台三级商品类目ID',
  `platform_fourth_cid` bigint(0) NULL DEFAULT NULL COMMENT '平台四级商品类目ID',
  `shop_first_cid` bigint(0) NULL DEFAULT NULL COMMENT '商品所属店铺内部的一级类目id',
  `shop_second_cid` bigint(0) NULL DEFAULT NULL COMMENT '商品所属店铺内部的二级类目id',
  `shop_third_cid` bigint(0) NULL DEFAULT NULL COMMENT '商品所属店铺内部的三级类目id',
  `brand_id` bigint(0) NULL DEFAULT NULL COMMENT '品牌id',
  `brand_name_cn` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品牌中文名称',
  `brand_name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品牌外文名称',
  `item_info_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的状态,1: 待审核，2: 审核驳回，3: 待上架，4: 在售，5: 已下架，（6: 锁定， 7: 申请解锁 8删除）',
  `status_change_reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '平台方下架或锁定或审核驳回时给出的理由',
  `platform_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`item_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_item_info' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_item_info
-- ----------------------------
INSERT INTO `vanx_item_info` VALUES (1, NULL, NULL, 'Vanx一号', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 12213, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, NULL, '2024-06-06 16:34:36', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (488962201080594432, NULL, NULL, '赖克宝哇', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 1222, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 15:43:05', '2024-06-06 15:43:05', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (488970769540349952, NULL, NULL, '涛涛', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 250, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 488604752427384832, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 16:16:20', '2024-06-06 16:18:12', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489340497505058816, NULL, NULL, '礼物盒子', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 3000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:11:03', '2024-06-07 16:11:03', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489340961361526784, NULL, NULL, '玫瑰', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 3000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:12:51', '2024-06-07 16:12:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489341146045120512, NULL, NULL, '芜湖火箭', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 2000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:13:35', '2024-06-07 16:13:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489341438102896640, NULL, NULL, '小心心', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 3000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:14:43', '2024-06-07 16:14:43', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489341605606621184, NULL, NULL, '小憨包', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 3000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:15:21', '2024-06-07 16:15:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489351900643229696, NULL, NULL, '棒棒糖', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 2000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:55:18', '2024-06-07 16:55:18', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489352227060744192, NULL, NULL, '大啤酒', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 2000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:56:35', '2024-06-07 16:56:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489352398859436032, NULL, NULL, '大火花', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 2000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:57:15', '2024-06-07 16:57:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (489352647967539200, NULL, NULL, '小黑人', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '从平台商品库选择', NULL, NULL, NULL, 1000, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 423662755262791680, 423663025845731328, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:58:13', '2024-06-07 16:58:13', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491554273973272576, NULL, NULL, '10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-001', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:21:39', '2024-06-13 15:21:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491554454361899008, NULL, NULL, '60', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-002', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:22:21', '2024-06-13 15:22:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491555498038951936, NULL, NULL, '300', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-003', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:23', '2024-06-13 15:26:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491555605413134336, NULL, NULL, '800', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-004', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:48', '2024-06-13 15:26:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491555717082284032, NULL, NULL, '980', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-005', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:14', '2024-06-13 15:27:14', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_info` VALUES (491555863111172096, NULL, NULL, '2980', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '平台添加', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, '平台', NULL, 1, NULL, NULL, NULL, NULL, 'XLSP-006', NULL, NULL, NULL, NULL, NULL, 488608196991156224, 491544339713916928, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '在售', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:48', '2024-06-13 15:27:48', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_item_sku
-- ----------------------------
DROP TABLE IF EXISTS `vanx_item_sku`;
CREATE TABLE `vanx_item_sku`  (
  `item_sku_id` bigint(0) NOT NULL COMMENT 'id',
  `item_id` bigint(0) NULL DEFAULT NULL COMMENT '对应商品的id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '商铺的id号',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家（商家）的id号',
  `item_sku_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品在平台上所起的sku名称（即店铺起的标题），该名称为系统自动生成字段',
  `shop_internal_sku_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺在自己内部系统上所起的sku名称',
  `shop_simple_sku_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺所起的sku简称名称',
  `shop_simple_sku_symbol` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的店铺所起的sku的助记符，一般指拼音首字母',
  `sku_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'sku的编码',
  `sku_barcode` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'sku的条形码',
  `shop_internal_sku_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '本店铺内的sku的编码',
  `platform_item_sku_id` bigint(0) NULL DEFAULT NULL COMMENT '此商品sku对应于平台商品的id；平台商品id，只有add_source为2时有意义',
  `platform_item_sku_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的平台标准化的sku名称（标题）',
  `sell_point` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的卖点',
  `currency_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '货币类型：有人民币CNY；2：虚拟货币；等',
  `sell_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '当前销售价格',
  `inventory` tinyint(0) NULL DEFAULT NULL COMMENT '库存量',
  `rest_inventory` tinyint(0) NULL DEFAULT NULL COMMENT '库存余量预警值',
  `sku_unit` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的sku单位；比如：袋，瓶。。。',
  `is_default` bit(1) NULL DEFAULT NULL COMMENT '该商品sku是否为默认的sku',
  `is_sale` bit(1) NULL DEFAULT b'0' COMMENT '商品sku是否上下架，默认为false',
  `sku_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'sku状态，表示该sku是否启用。1、有效2、无效',
  `attributes` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '销售属性集合，其是一个json字符串，存储格式key:value',
  `first_picture_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品的第一张图片路径url',
  `platform_fourth_cid` bigint(0) NULL DEFAULT NULL COMMENT '平台四级商品类目id',
  `platform_fourth_cname` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '平台六级商品分类类目名称',
  `brand_name_cn` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品牌中文名称',
  `brand_name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '品牌英文名称',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`item_sku_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_item_sku' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_item_sku
-- ----------------------------
INSERT INTO `vanx_item_sku` VALUES (1, 1, NULL, NULL, 'Vanx一号', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (488962205375561728, 488962201080594432, NULL, NULL, '赖克宝哇', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 15:43:05', '2024-06-06 15:43:05', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (488970769540349953, 488970769540349952, NULL, NULL, '涛涛', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 488604752427384832, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 16:16:20', '2024-06-06 16:16:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489340497505058817, 489340497505058816, NULL, NULL, '礼物盒子', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:11:04', '2024-06-07 16:11:04', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489340961361526785, 489340961361526784, NULL, NULL, '玫瑰', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:12:51', '2024-06-07 16:12:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489341146045120513, 489341146045120512, NULL, NULL, '芜湖火箭', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:13:35', '2024-06-07 16:13:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489341438102896641, 489341438102896640, NULL, NULL, '小心心', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:14:43', '2024-06-07 16:14:43', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489341605606621185, 489341605606621184, NULL, NULL, '小憨包', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:15:21', '2024-06-07 16:15:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489351900643229697, 489351900643229696, NULL, NULL, '棒棒糖', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:55:18', '2024-06-07 16:55:18', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489352227060744193, 489352227060744192, NULL, NULL, '大啤酒', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:56:35', '2024-06-07 16:56:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489352398859436033, 489352398859436032, NULL, NULL, '大火花', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:57:15', '2024-06-07 16:57:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (489352647967539201, 489352647967539200, NULL, NULL, '小黑人', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 423663025845731328, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:58:13', '2024-06-07 16:58:13', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491554273973272577, 491554273973272576, NULL, NULL, '10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:21:39', '2024-06-13 15:21:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491554454361899009, 491554454361899008, NULL, NULL, '60', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:22:21', '2024-06-13 15:22:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491555498038951937, 491555498038951936, NULL, NULL, '300', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:23', '2024-06-13 15:26:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491555605413134337, 491555605413134336, NULL, NULL, '800', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:48', '2024-06-13 15:26:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491555717082284033, 491555717082284032, NULL, NULL, '980', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:14', '2024-06-13 15:27:14', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku` VALUES (491555863111172097, 491555863111172096, NULL, NULL, '2980', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, b'0', '有效', NULL, NULL, 491544339713916928, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:48', '2024-06-13 15:27:48', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_item_sku_cartoon
-- ----------------------------
DROP TABLE IF EXISTS `vanx_item_sku_cartoon`;
CREATE TABLE `vanx_item_sku_cartoon`  (
  `item_carto_id` bigint(0) NOT NULL COMMENT 'id',
  `item_id` bigint(0) NULL DEFAULT NULL COMMENT '商品id',
  `sku_id` bigint(0) NULL DEFAULT NULL COMMENT '商品sku的id号',
  `pic_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物minio图片名称',
  `pic_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物图片路径url',
  `pic_local_path` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物图片的本地存储路径',
  `play_json_file_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放的动画对应的json文件名称',
  `play_json_file_url` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放的动画对应的json文件服务器上存储路径',
  `play_json_file_local_path` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放的动画对应的json文件在本地存储路径',
  `play_git_file_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放动画效果的gif文件名称',
  `play_git_file_url` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放的动画效果的gif文件服务器上存储路径',
  `play_git_file_local_path` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物播放的动画效果的gif文件所在本地路径',
  `is_used` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用',
  `carto_play_location` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '礼物动画播放时在手机界面上渲染的位置：中部、底部等',
  `carto_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '图片的认证状态：0、未认证 1、待审核 2、通过 3、驳回 4、已取消',
  `reject_reason` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '图片的认证失败原因',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`item_carto_id`) USING BTREE,
  INDEX `index_pic_name`(`pic_url`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_item_sku_cartoon' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_item_sku_cartoon
-- ----------------------------
INSERT INTO `vanx_item_sku_cartoon` VALUES (1, 1, 1, '421054829645824000/2024-06-06/1717659784455.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784455.png', NULL, '421054829645824000/2024-06-06/1717659784774.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784774.json', NULL, '421054829645824000/2024-06-06/1717659784874.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784874.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (488962205375561730, 488962201080594432, 488962205375561728, '421054829645824000/2024-06-06/1717659784455.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784455.png', NULL, '421054829645824000/2024-06-06/1717659784774.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784774.json', NULL, '421054829645824000/2024-06-06/1717659784874.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717659784874.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 15:43:05', '2024-06-06 15:43:05', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (488970769540349955, 488970769540349952, 488970769540349953, '421054829645824000/2024-06-06/1717661779067.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717661779067.png', NULL, '421054829645824000/2024-06-06/1717661779341.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717661779341.json', NULL, '421054829645824000/2024-06-06/1717661779444.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-06/1717661779444.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 16:16:20', '2024-06-06 16:16:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489340497505058819, 489340497505058816, 489340497505058817, '421054829645824000/2024-06-07/1717747862863.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747862863.png', NULL, '421054829645824000/2024-06-07/1717747863238.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747863238.json', NULL, '421054829645824000/2024-06-07/1717747863340.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747863340.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:11:04', '2024-06-07 16:11:04', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489340961361526787, 489340961361526784, 489340961361526785, '421054829645824000/2024-06-07/1717747970889.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747970889.png', NULL, '421054829645824000/2024-06-07/1717747970985.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747970985.json', NULL, '421054829645824000/2024-06-07/1717747971050.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717747971050.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:12:51', '2024-06-07 16:12:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489341146045120515, 489341146045120512, 489341146045120513, '421054829645824000/2024-06-07/1717748014357.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748014357.png', NULL, '421054829645824000/2024-06-07/1717748014449.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748014449.json', NULL, '421054829645824000/2024-06-07/1717748014670.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748014670.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:13:35', '2024-06-07 16:13:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489341438102896643, 489341438102896640, 489341438102896641, '421054829645824000/2024-06-07/1717748082708.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748082708.png', NULL, '421054829645824000/2024-06-07/1717748082813.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748082813.json', NULL, '421054829645824000/2024-06-07/1717748082889.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748082889.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:14:43', '2024-06-07 16:14:43', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489341605606621187, 489341605606621184, 489341605606621185, '421054829645824000/2024-06-07/1717748120848.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748120848.png', NULL, '421054829645824000/2024-06-07/1717748120954.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748120954.json', NULL, '421054829645824000/2024-06-07/1717748121114.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717748121114.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:15:21', '2024-06-07 16:15:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489351900643229699, 489351900643229696, 489351900643229697, '421054829645824000/2024-06-07/1717750517732.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750517732.png', NULL, '421054829645824000/2024-06-07/1717750517832.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750517832.json', NULL, '421054829645824000/2024-06-07/1717750517939.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750517939.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:55:18', '2024-06-07 16:55:18', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489352227060744195, 489352227060744192, 489352227060744193, '421054829645824000/2024-06-07/1717750594480.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750594480.png', NULL, '421054829645824000/2024-06-07/1717750594619.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750594619.json', NULL, '421054829645824000/2024-06-07/1717750594816.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750594816.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:56:35', '2024-06-07 16:56:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489352398859436035, 489352398859436032, 489352398859436033, '421054829645824000/2024-06-07/1717750634558.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750634558.png', NULL, '421054829645824000/2024-06-07/1717750634661.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750634661.json', NULL, '421054829645824000/2024-06-07/1717750634754.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750634754.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:57:15', '2024-06-07 16:57:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_cartoon` VALUES (489352647967539203, 489352647967539200, 489352647967539201, '421054829645824000/2024-06-07/1717750692277.png', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750692277.png', NULL, '421054829645824000/2024-06-07/1717750692381.json', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750692381.json', NULL, '421054829645824000/2024-06-07/1717750692495.gif', 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-07/1717750692495.gif', NULL, 1, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:58:13', '2024-06-07 16:58:13', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_item_sku_picture
-- ----------------------------
DROP TABLE IF EXISTS `vanx_item_sku_picture`;
CREATE TABLE `vanx_item_sku_picture`  (
  `sku_pic_id` bigint(0) NOT NULL COMMENT 'id',
  `sku_id` bigint(0) NULL DEFAULT NULL COMMENT '商品sku的id号',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺的id号',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家的id号',
  `pic_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku的图片名称',
  `pic_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU的图片路径url',
  `pic_minio_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU的图片存在minio中的路径名称',
  `picture_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU的图片类型',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数',
  `picture_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的认证状态，即：未认证、待审核、通过、驳回、取消',
  `reject_reason` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片认证失败的原因',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`sku_pic_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SKU的图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_item_sku_picture
-- ----------------------------
INSERT INTO `vanx_item_sku_picture` VALUES (491554278268239872, 491554273973272577, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263298797.png', '421054829645824000/2024-06-13/1718263298797.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:21:39', '2024-06-13 15:21:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_picture` VALUES (491554458656866305, 491554454361899009, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263341015.png', '421054829645824000/2024-06-13/1718263341015.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:22:21', '2024-06-13 15:22:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_picture` VALUES (491555498038951939, 491555498038951937, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263583298.png', '421054829645824000/2024-06-13/1718263583298.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:26:23', '2024-06-13 15:26:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_picture` VALUES (491555605413134339, 491555605413134337, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263608201.png', '421054829645824000/2024-06-13/1718263608201.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:26:48', '2024-06-13 15:26:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_picture` VALUES (491555717082284035, 491555717082284033, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263634137.png', '421054829645824000/2024-06-13/1718263634137.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:27:14', '2024-06-13 15:27:14', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_picture` VALUES (491555863111172099, 491555863111172097, NULL, NULL, NULL, 'http://101.43.154.175:9003/zx-vanx/421054829645824000/2024-06-13/1718263668020.png', '421054829645824000/2024-06-13/1718263668020.png', NULL, NULL, NULL, NULL, NULL, NULL, '2024-06-13 15:27:48', '2024-06-13 15:27:48', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_item_sku_price
-- ----------------------------
DROP TABLE IF EXISTS `vanx_item_sku_price`;
CREATE TABLE `vanx_item_sku_price`  (
  `sku_price_id` bigint(0) NOT NULL COMMENT 'id',
  `sku_id` bigint(0) NULL DEFAULT NULL COMMENT '对应商品的sku的id',
  `item_id` bigint(0) NULL DEFAULT NULL COMMENT '对应商品的id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺的id',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家（商家）的id',
  `area_id` bigint(0) NULL DEFAULT NULL COMMENT '所在区域的id',
  `area_name` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '区域名称',
  `currency_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '货币类型：有人民币CNY；2：虚拟货币VC；等',
  `sell_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '当前价格',
  `member_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '会员价格',
  `market_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '市场价格',
  `purchase_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '采购价格',
  `cost_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '成本价格',
  `wholesale_price` decimal(14, 2) NULL DEFAULT NULL COMMENT '批发价格',
  `max_num` bigint(0) NULL DEFAULT NULL COMMENT '销售的最大数量',
  `min_num` bigint(0) NULL DEFAULT NULL COMMENT '销售的最小数量',
  `price_status` tinyint(0) NULL DEFAULT NULL COMMENT '价格的状态：1、有效2、无效',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`sku_price_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_item_sku_price' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_item_sku_price
-- ----------------------------
INSERT INTO `vanx_item_sku_price` VALUES (1, 1, 1, NULL, NULL, NULL, NULL, 'CNY', 80.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (488962205375561729, 488962205375561728, 488962201080594432, NULL, NULL, NULL, NULL, 'CNY', 2222.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 15:43:05', '2024-06-06 15:43:05', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (488970769540349954, 488970769540349953, 488970769540349952, NULL, NULL, NULL, NULL, 'CNY', 250.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-06 16:16:20', '2024-06-06 16:16:20', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489340497505058818, 489340497505058817, 489340497505058816, NULL, NULL, NULL, NULL, 'VC', 100.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:11:04', '2024-06-07 16:11:04', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489340961361526786, 489340961361526785, 489340961361526784, NULL, NULL, NULL, NULL, 'VC', 100.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:12:51', '2024-06-07 16:12:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489341146045120514, 489341146045120513, 489341146045120512, NULL, NULL, NULL, NULL, 'VC', 1001.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:13:35', '2024-06-07 16:13:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489341438102896642, 489341438102896641, 489341438102896640, NULL, NULL, NULL, NULL, 'VC', 200.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:14:43', '2024-06-07 16:14:43', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489341605606621186, 489341605606621185, 489341605606621184, NULL, NULL, NULL, NULL, 'VC', 2000.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:15:21', '2024-06-07 16:15:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489351900643229698, 489351900643229697, 489351900643229696, NULL, NULL, NULL, NULL, 'VC', 500.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:55:18', '2024-06-07 16:55:18', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489352227060744194, 489352227060744193, 489352227060744192, NULL, NULL, NULL, NULL, 'VC', 400.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:56:35', '2024-06-07 16:56:35', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (489352398859436034, 489352398859436033, 489352398859436032, NULL, NULL, NULL, NULL, 'VC', 1000.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-07 16:57:15', '2024-06-07 16:57:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491194866814976002, 491194866814976001, 491194866814976000, NULL, NULL, NULL, NULL, 'VC', 10000.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-12 16:06:57', '2024-06-12 16:06:57', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491554273973272578, 491554273973272577, 491554273973272576, NULL, NULL, NULL, NULL, 'CNY', 1.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:21:39', '2024-06-13 15:21:39', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491554458656866304, 491554454361899009, 491554454361899008, NULL, NULL, NULL, NULL, 'CNY', 6.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:22:21', '2024-06-13 15:22:21', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491555498038951938, 491555498038951937, 491555498038951936, NULL, NULL, NULL, NULL, 'CNY', 30.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:23', '2024-06-13 15:26:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491555605413134338, 491555605413134337, 491555605413134336, NULL, NULL, NULL, NULL, 'CNY', 80.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:26:48', '2024-06-13 15:26:48', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491555717082284034, 491555717082284033, 491555717082284032, NULL, NULL, NULL, NULL, 'CNY', 98.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:14', '2024-06-13 15:27:14', 0, NULL, NULL, NULL);
INSERT INTO `vanx_item_sku_price` VALUES (491555863111172098, 491555863111172097, 491555863111172096, NULL, NULL, NULL, NULL, 'CNY', 298.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 15:27:48', '2024-06-13 15:27:48', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_item_category
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_item_category`;
CREATE TABLE `vanx_platf_item_category`  (
  `item_categ_id` bigint(0) NOT NULL COMMENT 'id',
  `item_categ_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品分类名称',
  `item_categ_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类类目编码',
  `item_categ_level` tinyint(0) NULL DEFAULT NULL COMMENT '类目级别：1、一级类目2、二级类目3、三级类目4、四级类目，目前只支持四级类目',
  `parent_categ_id` bigint(0) NULL DEFAULT NULL COMMENT '商品父级分类id',
  `is_leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否为叶子节点',
  `is_home_show` tinyint(1) NULL DEFAULT NULL COMMENT '是否前台首页展示',
  `attribute_template_id` bigint(0) NULL DEFAULT NULL COMMENT '商品类目对应的属性种类模板id号',
  `is_used` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `category_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '种类状态：1、申请2、通过3、驳回4、关闭',
  `add_source` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品类目来源：1、自定义添加2、从商品平台库选择',
  `reject_reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类认证失败的原因',
  `platform_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`item_categ_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_platf_item_category' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_item_category
-- ----------------------------
INSERT INTO `vanx_platf_item_category` VALUES (1, '商品类别列表', '0001', 0, 0, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423662755262791680, '礼物', 'GF', 1, 1, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:27:52', '2023-12-13 16:29:16', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423663025845731328, '热门礼物', 'GF-001', 2, 423662755262791680, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:28:56', '2023-12-13 16:28:56', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423663201939390464, '精选礼物', 'GF-002', 2, 423662755262791680, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:29:36', '2023-12-13 16:29:36', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423663266363899904, '豪华礼物', 'GF-003', 2, 423662755262791680, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:29:51', '2023-12-13 16:29:51', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423663343673311232, '生日礼物', 'GF-004', 2, 423662755262791680, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:30:09', '2023-12-13 16:30:16', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (423663451047493632, '节日礼物', 'GF-005', 2, 423662755262791680, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421369491834830848, NULL, '2023-12-13 16:30:34', '2023-12-13 16:30:34', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (488608196991156224, '虚拟商品', 'VC-001', 1, 1, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-05 16:49:22', '2024-06-05 16:49:23', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (488619428330635264, '虚拟币', 'VB-001', 2, 488608196991156224, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-05 17:32:56', '2024-06-05 17:32:56', 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_item_category` VALUES (491544339713916928, '虚拟币充值', 'VBCZ-001', 2, 488608196991156224, NULL, NULL, NULL, 1, NULL, '通过', '平台添加', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2024-06-13 14:43:06', '2024-06-13 14:43:19', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_platf_shop_busin_category
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_shop_busin_category`;
CREATE TABLE `vanx_platf_shop_busin_category`  (
  `busin_categ_id` bigint(0) NOT NULL COMMENT '主键id号',
  `business_categ_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台的经营类型名称：1、电影2、酒店3、餐饮 4、家电类 5、服装类....',
  `business_categ_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经营分类类目编码',
  `business_categ_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经营类型标识：1、一级类型2、二级类型3、三级类型4、四级类型',
  `parent_categ_id` bigint(0) NULL DEFAULT NULL COMMENT '经营类型父级id，顶级目录填0',
  `is_leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否为叶子节点',
  `is_home_show` tinyint(1) NULL DEFAULT NULL COMMENT '是否在前台首页展示',
  `attribute_template_id` bigint(0) NULL DEFAULT NULL COMMENT '平台经营类目对应的属性种类模板的id',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `category_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目状态1、申请2、通过3、驳回4关闭',
  `add_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目提交的来源1、平台管理2、商家',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类认证失败的原因',
  `platf_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platf_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '复审时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`busin_categ_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台的店铺经营类型分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_platf_shop_busin_category
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_shop_busin_categ_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_shop_busin_categ_relat`;
CREATE TABLE `vanx_shop_busin_categ_relat`  (
  `shop_busin_relat_id` bigint(0) NOT NULL COMMENT 'id',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家id',
  `shop_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺id',
  `busin_categ_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺的经营类目id',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`shop_busin_relat_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺经营类型关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_shop_busin_categ_relat
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_shop_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_shop_info`;
CREATE TABLE `vanx_shop_info`  (
  `shop_id` bigint(0) NOT NULL COMMENT 'id',
  `seller_id` bigint(0) NULL DEFAULT NULL COMMENT '卖家id',
  `shop_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺类别：1、个人店铺2、企业店铺',
  `shop_business_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺经营类型：1、品牌商2、经销商3、生产商',
  `sold_goods_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺销售的产品类型：1、新产品2、二手产品3、新品、二手混合销售',
  `shop_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺名称',
  `shop_introduce` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺介绍',
  `main_shell` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺主营介绍',
  `advert` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺的广告词',
  `shop_url` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺域名',
  `logo_url` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺logo_url',
  `business_time` tinyint(0) NULL DEFAULT NULL COMMENT '开店时长，单位月',
  `is_entity_shop` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺是否为实体店：0、非实体店，即仅仅虚拟店铺1、实体店',
  `is_direct_sale` tinyint(1) NULL DEFAULT NULL COMMENT '是否直营',
  `recommend_value` int(0) NULL DEFAULT NULL COMMENT '推荐指数',
  `keyword` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺的关键字，各关键字之间用分号隔开',
  `shop_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺建新状态：0、未申请1、申请2、通过3、驳回4、平台关闭5、开通6停业整改',
  `pass_time` datetime(0) NULL DEFAULT NULL COMMENT '开通时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '终止时间',
  `run_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺运营状态（只允许卖家操作，默认不开启），1、表示卖家开启店铺2、表示卖家关闭店铺',
  `modifiy_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺信息修改状态1、待审核2、驳回3、修改通过',
  `add_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺提交的来源1、平台管理2、商家',
  `reject_reasons` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺申请未通过原因',
  `platf_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platf_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统店铺基本信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_shop_info
-- ----------------------------
INSERT INTO `vanx_shop_info` VALUES (1, NULL, NULL, NULL, NULL, '系统一号店', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_shop_picture
-- ----------------------------
DROP TABLE IF EXISTS `vanx_shop_picture`;
CREATE TABLE `vanx_shop_picture`  (
  `shop_picture_id` bigint(0) NOT NULL COMMENT 'id',
  `shop_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '店铺的id',
  `picture_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的标题',
  `picture_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的类型',
  `picture_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片的路径',
  `picture_minio_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片minio名称',
  `sort_number` tinyint(0) NULL DEFAULT NULL COMMENT '排序指数',
  `picture_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片状态：1、未认证2、待审核3、通过4、驳回5取消',
  `reject_reasion` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证失败的原因',
  `platf_check_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人id',
  `platf_recheck_user_id` bigint(0) NULL DEFAULT NULL COMMENT '复审人id',
  `chech_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint(0) NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(0) NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`shop_picture_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_shop_picture
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
