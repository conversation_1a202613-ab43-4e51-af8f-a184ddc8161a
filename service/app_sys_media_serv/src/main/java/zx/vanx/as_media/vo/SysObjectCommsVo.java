package zx.vanx.as_media.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统对象评论VO
 */
@Data
public class SysObjectCommsVo {

    @ApiModelProperty(value = "评论id")
    @TableId(value = "object_comms_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objectCommsId;

    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户昵称")
    private String userNickName;

    @ApiModelProperty(value = "用户头像")
    private String userPhoto;

    @ApiModelProperty(value = "被评论的对象的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commsedObjectId;

    @ApiModelProperty(value = "评论的内容")
    private String content;

    @ApiModelProperty(value = "评论的顶级评论id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long topCommsId;

    @ApiModelProperty(value = "评论的父级评论id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommsId;

    @ApiModelProperty(value = "评论的父级评论用户id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommsUserId;

    @ApiModelProperty(value = "评论的父级评论用户名称")
    @JsonSerialize(using = ToStringSerializer.class)
    private String parentCommsUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @ApiModelProperty("二级评论列表")
    @TableField(exist = false)
    private List<SysObjectCommsVo> secondCommsList;

    // @ApiModelProperty("二级评论列表")
    // @TableField(exist = false)
    // private boolean isHasChildren;
}
