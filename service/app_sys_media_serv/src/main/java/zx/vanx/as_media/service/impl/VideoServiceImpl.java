package zx.vanx.as_media.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zx.vanx.as_media.service.VideoService;
import zx.vanx.as_media.vo.VideoVO;
import zx.vanx.common.redis.utils.RedisUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 视频服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoServiceImpl implements VideoService {

    // 用户观看记录Redis键前缀
    private static final String USER_VIEWED_KEY_PREFIX = "user:viewed:";
    // 用户兴趣模型Redis键前缀
    private static final String USER_INTEREST_KEY_PREFIX = "user:interest:";
    // 推荐结果记录Redis键前缀
    private static final String RECOMMEND_RECORD_KEY_PREFIX = "recommend:record:";
    // 兴趣模型过期时间（天）
    private static final int INTEREST_EXPIRE_DAYS = 30;
    // 观看记录过期时间（天）
    private static final int VIEWED_EXPIRE_DAYS = 15;

    /**
     * 获取用户初始推荐视频
     * 
     * @param userId 用户ID
     * @return 推荐视频列表
     */
    @Override
    public List<VideoVO> getInitialRecommendations(Long userId) {
        log.info("获取用户初始推荐视频, userId: {}", userId);
        // 这里实现实际的推荐算法逻辑
        // 1. 从用户兴趣模型中获取用户偏好
        Map<String, Object> userInterests = getUserInterests(userId);

        // 2. 根据用户兴趣从数据库获取推荐视频
        // 实际项目中，这里应该调用mapper查询数据库
        // 为演示，这里模拟返回测试数据
        return generateMockRecommendations(200);
    }

    /**
     * 获取用户下一批推荐视频
     * 
     * @param userId 用户ID
     * @return 推荐视频列表
     */
    @Override
    public List<VideoVO> getNextBatchRecommendations(Long userId) {
        log.info("获取用户下一批推荐视频, userId: {}", userId);
        // 获取用户兴趣模型
        Map<String, Object> userInterests = getUserInterests(userId);

        // 根据用户兴趣获取推荐视频
        // 实际项目中，这里应该调用mapper查询数据库
        // 为演示，这里模拟返回测试数据
        return generateMockRecommendations(50);
    }

    /**
     * 获取推荐视频列表 - 基于推荐算法
     *
     * @param userId 用户ID
     * @param count  需要获取的视频数量
     * @return 推荐视频列表
     */
    @Override
    public List<VideoVO> getRecommendVideos(Long userId, int count) {
        log.info("获取用户{}的推荐视频, count: {}", userId, count);

        // 获取用户兴趣模型
        Map<String, Object> userInterests = getUserInterests(userId);

        // 基于用户兴趣生成推荐视频
        // 实际项目中应该调用推荐算法服务
        return generateMockRecommendations(count);
    }

    /**
     * 获取热门视频列表
     *
     * @param count 需要获取的视频数量
     * @return 热门视频列表
     */
    @Override
    public List<VideoVO> getPopularVideos(int count) {
        log.info("获取热门视频, count: {}", count);

        // 实际项目中应该从数据库查询热门视频
        // 这里模拟返回热门视频数据
        return generateMockRecommendations(count);
    }

    /**
     * 获取随机视频列表
     * 
     * @param count 需要获取的视频数量
     * @return 随机视频列表
     */
    @Override
    public List<VideoVO> getRandomVideos(int count) {
        log.info("获取随机视频, count: {}", count);

        // 实际项目中应该从数据库随机查询视频
        // 这里模拟返回随机视频数据
        return generateMockRecommendations(count);
    }

    /**
     * 获取用户已观看的视频ID列表
     * 
     * @param userId 用户ID
     * @return 已观看视频ID列表
     */
    @Override
    public List<Long> getUserViewedVideoIds(Long userId) {
        String key = USER_VIEWED_KEY_PREFIX + userId;
        Set<String> viewedSet = RedisUtils.getCacheSet(key);

        if (viewedSet == null || viewedSet.isEmpty()) {
            return new ArrayList<>();
        }

        return viewedSet.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 生成模拟推荐数据（仅用于演示） 实际项目中应该从数据库查询
     * 
     * 使用时间戳和随机数生成唯一ID，避免重复
     */
    private List<VideoVO> generateMockRecommendations(int count) {
        List<VideoVO> mockVideos = new ArrayList<>();

        // 使用时间戳作为基础ID，避免重复
        long baseId = System.currentTimeMillis() / 1000; // 秒级时间戳
        Random random = new Random();

        for (int i = 0; i < count; i++) {
            // 生成唯一ID：时间戳 + 序号 + 随机数
            long videoId = baseId * 1000 + i * 10 + random.nextInt(10);

            VideoVO video = new VideoVO().setId(videoId).setTitle("推荐视频 #" + videoId)
                    .setCoverUrl("https://example.com/cover/" + videoId + ".jpg")
                    .setVideoUrl("https://example.com/video/" + videoId + ".mp4").setDuration(random.nextInt(300) + 60) // 60-360秒
                    .setCategoryId(random.nextInt(10) + 1L) // 1-10分类
                    .setCategoryName("分类" + (random.nextInt(10) + 1)).setCreatorId(random.nextInt(1000) + 1L)
                    .setCreatorName("创作者" + (random.nextInt(1000) + 1))
                    .setCreatorAvatar("https://example.com/avatar/" + (random.nextInt(100) + 1) + ".jpg")
                    .setLikeCount((long) random.nextInt(10000)).setCommentCount((long) random.nextInt(5000))
                    .setShareCount((long) random.nextInt(3000)).setPlayCount((long) random.nextInt(50000))
                    .setIsLiked(false).setIsCollected(false).setIsFollowed(false).setTags("标签1,标签2,标签3")
                    .setCreateTime(new Date()).setUpdateTime(new Date()).setRecommendWeight(random.nextInt(100))
                    .setStatus(1);

            mockVideos.add(video);
        }

        return mockVideos;
    }

    /**
     * 生成模拟推荐视频
     * 
     * @param userId     用户ID
     * @param categoryId 视频分类ID
     * @param tags       视频标签
     */
    @Override
    public void updateUserInterestModel(Long userId, Long categoryId, String tags) {
        if (categoryId == null) {
            return;
        }

        String interestKey = USER_INTEREST_KEY_PREFIX + userId;

        // 更新分类兴趣度
        String categoryKey = "category:" + categoryId;
        Integer currentWeight = RedisUtils.getCacheMapValue(interestKey, categoryKey);
        int newWeight = (currentWeight == null ? 0 : currentWeight) + 1;
        RedisUtils.setCacheMapValue(interestKey, categoryKey, newWeight);

        // 更新标签兴趣度
        if (tags != null && !tags.isEmpty()) {
            String[] tagArray = tags.split(",");
            for (String tag : tagArray) {
                if (tag.trim().isEmpty()) {
                    continue;
                }
                String tagKey = "tag:" + tag.trim();
                Integer tagWeight = RedisUtils.getCacheMapValue(interestKey, tagKey);
                int newTagWeight = (tagWeight == null ? 0 : tagWeight) + 1;
                RedisUtils.setCacheMapValue(interestKey, tagKey, newTagWeight);
            }
        }

        // 设置过期时间
        RedisUtils.expire(interestKey, TimeUnit.DAYS.toSeconds(INTEREST_EXPIRE_DAYS));
    }

    /**
     * 记录推荐结果（基于位置）
     * 
     * @param userId         用户ID
     * @param globalPosition 全局位置
     * @param videos         推荐的视频列表
     */
    @Override
    public void recordRecommendationResultsByPosition(Long userId, int globalPosition, List<VideoVO> videos) {
        // 记录本次推荐的视频ID（基于位置）
        String recordKey = RECOMMEND_RECORD_KEY_PREFIX + userId + ":pos:" + globalPosition + ":"
                + System.currentTimeMillis();

        // 将视频ID列表存入Redis
        List<String> videoIds = videos.stream().map(v -> String.valueOf(v.getId())).collect(Collectors.toList());

        RedisUtils.setCacheList(recordKey, videoIds);

        // 设置过期时间（1天后自动过期）
        RedisUtils.expire(recordKey, TimeUnit.DAYS.toSeconds(1));

        log.debug("记录推荐结果成功(基于位置), userId: {}, globalPosition: {}, count: {}", userId, globalPosition, videos.size());

    }

    /**
     * 记录推荐结果（基于页码）
     * 
     * @param userId 用户ID
     * @param page   页码
     * @param videos 推荐的视频列表
     */
    @Override
    public void recordRecommendationResults(Long userId, int page, List<VideoVO> videos) {
        // 记录本次推荐的视频ID
        String recordKey = RECOMMEND_RECORD_KEY_PREFIX + userId + ":" + System.currentTimeMillis();

        // 将视频ID列表存入Redis
        List<String> videoIds = videos.stream().map(v -> String.valueOf(v.getId())).collect(Collectors.toList());

        RedisUtils.setCacheList(recordKey, videoIds);

        // 设置过期时间（1天后自动过期）
        RedisUtils.expire(recordKey, TimeUnit.DAYS.toSeconds(1));

        log.debug("记录推荐结果成功, userId: {}, page: {}, count: {}", userId, page, videos.size());
    }

    /**
     * 记录用户观看记录
     *
     * @param userId  用户ID
     * @param videoId 视频ID
     */
    @Override
    public void recordUserViewed(Long userId, Long videoId) {

        if (userId == null || videoId == null) {
            return;
        }

        String key = USER_VIEWED_KEY_PREFIX + userId;
        RedisUtils.setCacheSet(key, Collections.singleton(String.valueOf(videoId)));
        RedisUtils.expire(key, TimeUnit.DAYS.toSeconds(VIEWED_EXPIRE_DAYS));
    }

    /**
     * 获取用户兴趣模型
     * 
     * @param userId 用户ID
     * @return 用户兴趣模型
     */
    private Map<String, Object> getUserInterests(Long userId) {
        String interestKey = USER_INTEREST_KEY_PREFIX + userId;
        Map<String, Object> interests = RedisUtils.getCacheMap(interestKey);
        return interests != null ? interests : new HashMap<>();
    }
}