package zx.vanx.as_media.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.as_media.entity.MediaPictures;
import zx.vanx.as_media.entity.SysMediaSetting;
import zx.vanx.public_creation.entity.CreationCateg;
import zx.vanx.public_creation.entity.CreationTopic;
import zx.vanx.public_creation.vo.CreationCategVo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 媒体和快速信息VO
 */
@Data
public class MediaAndFast implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "media_id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mediaId;

    @ApiModelProperty(value = "该用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户昵称")
    private String userNickName;

    @ApiModelProperty(value = "条件:=>用户角色:1、平台用户 2、普通用户")
    private String roleName;

    @ApiModelProperty(value = "该用户商铺的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty(value = "该用户商铺的昵称")
    private String shopName;

    @ApiModelProperty(value = "条件:=>视频或图片的名称")
    private String mediaTitle;

    @ApiModelProperty(value = "填写的事项描述")
    private String mediaDescription;

    @ApiModelProperty(value = "视频或图片的用户对应的序号")
    private String mediaUserSort;

    @ApiModelProperty(value = "条件:=>视频或图片的路径url")
    private String mediaFileUrl;

    @ApiModelProperty(value = "条件:=>视频或图片的名称")
    private String mediaFileName;

    @ApiModelProperty(value = "条件:=>视频或图片的Minio名称")
    private String mediaFileMinioName;

    @ApiModelProperty(value = "条件:=>视频或图片的封面的路径url")
    private String mediaCoverUrl;

    @ApiModelProperty(value = "条件:=>视频或图片的封面的名称")
    private String mediaCoverName;

    @ApiModelProperty(value = "视频或图片的封面的Minio名称")
    private String mediaCoverMinioName;

    @ApiModelProperty(value = "条件:=>视频或图片的类型：1、视频2、照片")
    private String mediaFileType;

    @ApiModelProperty(value = "条件:=>拍摄时间：来自相册")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date mediaBirthDay;

    @ApiModelProperty(value = "条件:=>视频或图片大小,单位M")
    private Double mediaSize;

    @ApiModelProperty(value = "分享类型：1、公开2、粉丝3、好友圈4、好友可见5、仅自己可见")
    private String sharedType;

    @ApiModelProperty(value = "视频或图片展示的类型；1、单栏上下简单式；2、单栏上下完整式；3、单栏左图右文简单式；4、单栏左图右文完整式；5、单栏左文右图简单式；6、单栏左文右图完整式")
    private String mediaShownType;

    @ApiModelProperty(value = "是否允许评论")
    private Boolean isAllowedComments;

    @ApiModelProperty(value = "视频或图片的创作类型")
    private String mediaCreationType;

    @ApiModelProperty(value = "条件:=>视频或图片状态1、下架；2、上架3、删除")
    private String mediaStatus;

    @ApiModelProperty(value = "视频或图片来源，1-自定义，2-平台添加")
    private String addSource;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long mediaFastId;

    @ApiModelProperty(value = "图片列表")
    private String images;

    @ApiModelProperty(value = "指定好友分享的好友ids，为json形式", required = true)
    @TableField(exist = false)
    private Long[] friendIds;

    @ApiModelProperty(value = "创作形式分类")
    @TableField(exist = false)
    private CreationCateg creativeFormCategory;

    @ApiModelProperty(value = "视频的一级分类对象")
    @TableField(exist = false)
    private CreationCateg creationCategOne;

    @ApiModelProperty(value = "视频的二级分类对象")
    @TableField(exist = false)
    private CreationCateg creationCategTwo;

    @ApiModelProperty(value = "视频的三级分类对象")
    @TableField(exist = false)
    private CreationCateg creationCategThree;

    @ApiModelProperty(value = "视频的分类对象列表")
    @TableField(exist = false)
    private List<CreationCategVo> creationCategList;

    @ApiModelProperty(value = "视频或图片的分享次数")
    private Integer numOfShared;

    @ApiModelProperty(value = "视频或图片的点赞数量")
    private Integer numOfLikes;

    @ApiModelProperty(value = "视频或图片的收藏数量")
    private Integer numOfCollect;

    @ApiModelProperty(value = "所在省（省）")
    private String provinceName;

    @ApiModelProperty(value = "所在地（市）")
    private String cityName;

    @ApiModelProperty(value = "所在地（区）")
    private String districtName;

    @ApiModelProperty(value = "所在镇、街道名称")
    private String townName;

    @ApiModelProperty(value = "照片列表")
    @TableField(exist = false)
    private List<MediaPictures> mediaPicturesList = new ArrayList<>();

    @ApiModelProperty(value = "用户选择的已有的话题列表")
    @TableField(exist = false)
    private List<CreationTopic> selectTopicList;

    @ApiModelProperty(value = "用户自定义添加的话题列表")
    @TableField(exist = false)
    private List<CreationTopic> newTopicList;

    /**
     * 设置表相关信息
     */
    private SysMediaSetting sysMediaSetting;

    /**
     * 设置视频或图片展示的类型
     */
    public void setRandomMediaShownType() {
        Random random = new Random();
        int randomType = random.nextInt(6) + 1;
        String typeDescription;
        switch (randomType) {
            case 1:
                typeDescription = "单栏上下简单式";
                break;
            case 2:
                typeDescription = "单栏上下完整式";
                break;
            case 3:
                typeDescription = "单栏左图右文简单式";
                break;
            case 4:
                typeDescription = "单栏左图右文完整式";
                break;
            case 5:
                typeDescription = "单栏左文右图简单式";
                break;
            case 6:
                typeDescription = "单栏左文右图完整式";
                break;
            default:
                typeDescription = "";
        }
        this.mediaShownType = typeDescription;
    }

}
