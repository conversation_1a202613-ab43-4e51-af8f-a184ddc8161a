package zx.vanx.as_media.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户分类VO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("vanx_categ_media")
@ApiModel(value = "Category对象", description = "vanx_categ_media")
public class UserCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mediaCategId;

    @ApiModelProperty(value = "该用户的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "该用户商铺的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty(value = "分类类目名称")
    private String categName;

    @ApiModelProperty(value = "分类的类型，如：视频分类；照片分类")
    private String categType;

    @ApiModelProperty(value = "分类的图标路径url")
    private String iconUrl;

    @ApiModelProperty(value = "分类的背景图片路径url")
    private String backgImageUrl;

    @ApiModelProperty(value = "分类的背景颜色")
    private String backgColour;

    @ApiModelProperty(value = "类目级别，1、一级类目2、二级类目3、三级类目4、四级类目")
    private Integer categLevel;

    @ApiModelProperty(value = "商品分类类目编码")
    private String categCode;

    @ApiModelProperty(value = "父级分类，顶级目录填0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCategId;

    @ApiModelProperty(value = "创建者用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creatorId;

    @ApiModelProperty(value = "修改者用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long editorId;

}
