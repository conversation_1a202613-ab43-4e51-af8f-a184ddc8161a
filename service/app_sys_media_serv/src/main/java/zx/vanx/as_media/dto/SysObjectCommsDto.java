package zx.vanx.as_media.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用户系统对象的评论表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@ApiModel(value="SysObjectComms对象", description="用户系统对象的评论表")
public class SysObjectCommsDto {

    @ApiModelProperty(value = "被评论的视频的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commsedObjectId;

    @ApiModelProperty(value = "被评论的对象类型")
    private String commsedObjectType;

    @ApiModelProperty(value = "评论的内容")
    private String content;

    @ApiModelProperty(value = "评论的顶级评论id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long topCommsId;

    @ApiModelProperty(value = "评论的父级评论id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommsId;

    @ApiModelProperty(value = "评论的父级评论用户id，第一级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommsUserId;
}
