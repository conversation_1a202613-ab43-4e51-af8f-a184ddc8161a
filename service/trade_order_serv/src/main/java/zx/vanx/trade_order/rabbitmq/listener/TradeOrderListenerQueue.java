package zx.vanx.trade_order.rabbitmq.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import zx.vanx.aspect.Idempotent;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.common_util.utils.SnowflakeIdWorker;
import zx.vanx.constant.MqConst;
import zx.vanx.ma_manage.client.ManageClient;
import zx.vanx.ma_manage.vo.AddVanxUserPointsExchaRec;
import zx.vanx.trade_order.entity.TradeOrderInfo;
import zx.vanx.trade_order.entity.TradeOrderItems;
import zx.vanx.trade_order.entity.TradeOrderItemsDiscount;
import zx.vanx.trade_order.request.OrderInfoRequest;
import zx.vanx.trade_order.request.OrderItemRequest;
import zx.vanx.trade_order.service.TradeOrderInfoService;
import zx.vanx.trade_order.service.TradeOrderItemsDiscountService;
import zx.vanx.trade_order.service.TradeOrderItemsService;

import java.math.BigDecimal;
import java.util.List;


@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class TradeOrderListenerQueue {

    private final TradeOrderInfoService tradeOrderInfoService;

    private final TradeOrderItemsService tradeOrderItemsService;

    private final ManageClient manageClient;

    private final TradeOrderItemsDiscountService tradeOrderItemsDiscountService;


    /**
     * 添加广告订单
     * @param orderInfoRequest 订单对象
     * @param message 消息
     * @param channel 通道
     */
    @SneakyThrows
    @RabbitListener(bindings =
    @QueueBinding(
            exchange = @Exchange(value = MqConst.EXCHANGE_DIRECT_ADD_ORDER,autoDelete = "false"),
            key = {MqConst.ROUTING_ADD_ORDER},
            value = @Queue(
                    value = MqConst.QUEUE_ADD_ORDER,durable = "true",autoDelete = "false",
                    arguments = {@Argument(name = "x-queue-mode", value = "lazy")

                    })
    ))
    @Idempotent
    @Transactional
    public void addOrder(OrderInfoRequest orderInfoRequest, Message message, Channel channel) {

        // 将dto转换为实体
        TradeOrderInfo tradeOrderInfo = BeanUtil.copyProperties(orderInfoRequest, TradeOrderInfo::new);
        // 生成订单编号
        tradeOrderInfo.setOrderCode(SnowflakeIdWorker.getNextId());


        BigDecimal couponReducePrice = orderInfoRequest.getCouponReducePrice(); // 优惠卷的总金额
        BigDecimal scoreDiscount = orderInfoRequest.getScoreDiscount(); // 积分优惠总金额
        BigDecimal promotionDiscountAmount = orderInfoRequest.getPromotionDiscountAmount(); // 促销折扣总金额
        BigDecimal paymentPrice = orderInfoRequest.getPaymentPrice(); // 实际支付金额
        Long scoreNum = orderInfoRequest.getScoreNum(); // 积分总数

        List<OrderItemRequest> orderItems = orderInfoRequest.getOrderItems();

        // 保存订单项信息
        for (OrderItemRequest orderItem : orderItems) {
            // 订单信息填充
            couponReducePrice = couponReducePrice.add(orderItem.getCouponDiscount());
            scoreDiscount = scoreDiscount.add(orderItem.getScoreDiscount());
            promotionDiscountAmount = promotionDiscountAmount.add(orderItem.getPromotionDiscount());
            scoreNum = scoreNum + orderItem.getScoreNum();


            paymentPrice = paymentPrice.add(orderItem.getPayPriceTotal());
        }

        tradeOrderInfo.setCouponReducePrice(couponReducePrice);
        tradeOrderInfo.setScoreDiscount(scoreDiscount);
        tradeOrderInfo.setPromotionDiscountAmount(promotionDiscountAmount);
        tradeOrderInfo.setPaymentPrice(paymentPrice);
        tradeOrderInfo.setScoreNum(scoreNum);

        // 保存订单信息
        tradeOrderInfoService.save(tradeOrderInfo);

        // 保存订单项信息
        for (OrderItemRequest orderItem : orderItems) {

            TradeOrderItems tradeOrderItems = BeanUtil.copyProperties(orderItem, TradeOrderItems::new);
            tradeOrderItems.setOrderId(tradeOrderInfo.getOrderId());
            tradeOrderItems.setOrderCode(tradeOrderInfo.getOrderCode());

            tradeOrderItemsService.save(tradeOrderItems);
        }

    }

    /**
     * 订单生成延时消息队列
     * @param addVanxUserPointsExchaRec
     * @param message
     * @param channel
     */
    @SneakyThrows
    @RabbitListener(bindings =
    @QueueBinding(
            exchange = @Exchange(value = MqConst.EXCHANGE_DIRECT_CANCEL_ORDER, autoDelete = "false"),
            key = {MqConst.ROUTING_CANCEL_ORDER},
            value = @Queue(
                    value = MqConst.QUEUE_CANCEL_ORDER, durable = "true", autoDelete = "false",
                    arguments = {
                            @Argument(name = "x-queue-mode", value = "lazy"),
                            @Argument(name = "x-dead-letter-exchange", value = MqConst.DLX_EXCHANGE_DIRECT_CANCEL_ORDER),
                            @Argument(name = "x-dead-letter-routing-key", value = MqConst.DLX_ROUTING_CANCEL_ORDER),
                            @Argument(name = "x-message-ttl", value = "30000", type = "java.lang.Integer")
                    }
            )
    ), autoStartup = "false")
    @Transactional
    public void orderRollback(AddVanxUserPointsExchaRec addVanxUserPointsExchaRec, Message message, Channel channel) {
    }
    /**
     * 订单生成延时消息死信队列
     * @param addVanxUserPointsExchaRec
     * @param message
     * @param channel
     */
    @SneakyThrows
    @RabbitListener(bindings =
    @QueueBinding(
            exchange = @Exchange(value = MqConst.DLX_EXCHANGE_DIRECT_CANCEL_ORDER, autoDelete = "false"),
            key = {MqConst.DLX_ROUTING_CANCEL_ORDER},
            value = @Queue(value = MqConst.DLX_QUEUE_CANCEL_ORDER, durable = "true", autoDelete = "false")
    ))
    @Idempotent
    @Transactional
    public void processExpiredOrder(AddVanxUserPointsExchaRec addVanxUserPointsExchaRec, Message message, Channel channel) {
        // 处理过期订单的逻辑，例如删除订单和回滚库存
        System.out.println( " 延迟消息被执行" + addVanxUserPointsExchaRec );

        Long orderId = addVanxUserPointsExchaRec.getOrderId();// 订单id
        TradeOrderInfo tradeOrderInfo = tradeOrderInfoService.getBaseMapper().selectById(orderId);

        if ("待付款".equals(tradeOrderInfo.getPayStatus())) {

            // 恢复用户未支付而扣除的积分并增加商品库存
            manageClient.recoverDeductItemStock(addVanxUserPointsExchaRec);

            Long redPacketId = null;
            Long CouponId = null;

            TradeOrderItemsDiscount redPacket = tradeOrderItemsDiscountService.getBaseMapper().selectOne(new QueryWrapper<TradeOrderItemsDiscount>().eq("order_id", orderId).eq("order_items_id", addVanxUserPointsExchaRec.getOrderItemsId()).eq("discount_object_type", "红包"));
            TradeOrderItemsDiscount Coupon = tradeOrderItemsDiscountService.getBaseMapper().selectOne(new QueryWrapper<TradeOrderItemsDiscount>().eq("order_id", orderId).eq("order_items_id", addVanxUserPointsExchaRec.getOrderItemsId()).eq("discount_object_type", "优惠券"));

            if (!ObjectUtils.isEmpty(redPacket)) {
                redPacketId = redPacket.getDiscountObjectId();
            }
            if (!ObjectUtils.isEmpty(Coupon)) {
                CouponId = Coupon.getDiscountObjectId();
            }

            // 取消订单-释放优惠券and红包
            manageClient.cancelOrder(CouponId,redPacketId,addVanxUserPointsExchaRec.getUserId());

            // 订单状态修改为已取消
            tradeOrderInfo.setPayStatus("已取消");
            tradeOrderInfo.setOrderStatus("已取消");
            tradeOrderInfo.setOrderTradeStatus("已取消");
            tradeOrderInfoService.getBaseMapper().updateById(tradeOrderInfo);

        }
    }


}
