<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.PlatfPostCategMapper">

    <!-- 递归查询所有分类信息（支持多级分类树形结构） -->
    <select id="selectAllCategoriesRecursive" resultType="zx.vanx.social_circle.entity.PlatfPostCateg">
        WITH RECURSIVE category_tree AS (
            -- 第一步：查询顶级分类（parent_categ_id = 0）
            SELECT 
                post_categ_id,
                user_id,
                shop_id,
                categ_name,
                categ_type,
                icon_url,
                backg_image_url,
                backg_colour,
                categ_level,
                categ_crit_id,
                categ_code,
                parent_categ_id,
                is_leaf,
                sort_number,
                add_source,
                creator_id,
                editor_id,
                created_time,
                modified_time,
                is_deleted,
                other_info_one,
                other_info_two,
                remark
            FROM vanx_platf_post_categ
            WHERE parent_categ_id = 0
            AND is_deleted = 0
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="categType != null and categType.trim() != ''">
                AND categ_type = #{categType}
            </if>
            
            UNION ALL
            
            -- 递归步骤：查询子分类
            SELECT 
                child.post_categ_id,
                child.user_id,
                child.shop_id,
                child.categ_name,
                child.categ_type,
                child.icon_url,
                child.backg_image_url,
                child.backg_colour,
                child.categ_level,
                child.categ_crit_id,
                child.categ_code,
                child.parent_categ_id,
                child.is_leaf,
                child.sort_number,
                child.add_source,
                child.creator_id,
                child.editor_id,
                child.created_time,
                child.modified_time,
                child.is_deleted,
                child.other_info_one,
                child.other_info_two,
                child.remark
            FROM vanx_platf_post_categ child
            INNER JOIN category_tree parent ON child.parent_categ_id = parent.post_categ_id
            WHERE child.is_deleted = 0
            <if test="userId != null">
                AND child.user_id = #{userId}
            </if>
            <if test="categType != null and categType.trim() != ''">
                AND child.categ_type = #{categType}
            </if>
        )
        SELECT * FROM category_tree
        ORDER BY categ_level ASC, sort_number ASC, created_time ASC
    </select>

    <!-- 递归查询指定父级下的所有子分类 -->
    <select id="selectChildCategoriesRecursive" resultType="zx.vanx.social_circle.entity.PlatfPostCateg">
        WITH RECURSIVE child_category_tree AS (
            -- 第一步：查询直接子分类
            SELECT 
                post_categ_id,
                user_id,
                shop_id,
                categ_name,
                categ_type,
                icon_url,
                backg_image_url,
                backg_colour,
                categ_level,
                categ_crit_id,
                categ_code,
                parent_categ_id,
                is_leaf,
                sort_number,
                add_source,
                creator_id,
                editor_id,
                created_time,
                modified_time,
                is_deleted,
                other_info_one,
                other_info_two,
                remark
            FROM vanx_platf_post_categ
            WHERE parent_categ_id = #{parentCategId}
            AND is_deleted = 0
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="categType != null and categType.trim() != ''">
                AND categ_type = #{categType}
            </if>
            
            UNION ALL
            
            -- 递归步骤：查询更深层的子分类
            SELECT 
                child.post_categ_id,
                child.user_id,
                child.shop_id,
                child.categ_name,
                child.categ_type,
                child.icon_url,
                child.backg_image_url,
                child.backg_colour,
                child.categ_level,
                child.categ_crit_id,
                child.categ_code,
                child.parent_categ_id,
                child.is_leaf,
                child.sort_number,
                child.add_source,
                child.creator_id,
                child.editor_id,
                child.created_time,
                child.modified_time,
                child.is_deleted,
                child.other_info_one,
                child.other_info_two,
                child.remark
            FROM vanx_platf_post_categ child
            INNER JOIN child_category_tree parent ON child.parent_categ_id = parent.post_categ_id
            WHERE child.is_deleted = 0
            <if test="userId != null">
                AND child.user_id = #{userId}
            </if>
            <if test="categType != null and categType.trim() != ''">
                AND child.categ_type = #{categType}
            </if>
        )
        SELECT * FROM child_category_tree
        ORDER BY categ_level ASC, sort_number ASC, created_time ASC
    </select>

    <!-- 查询顶级分类列表（parent_categ_id = 0） -->
    <select id="selectTopLevelCategories" resultType="zx.vanx.social_circle.entity.PlatfPostCateg">
        SELECT 
            post_categ_id,
            user_id,
            shop_id,
            categ_name,
            categ_type,
            icon_url,
            backg_image_url,
            backg_colour,
            categ_level,
            categ_crit_id,
            categ_code,
            parent_categ_id,
            is_leaf,
            sort_number,
            add_source,
            creator_id,
            editor_id,
            created_time,
            modified_time,
            is_deleted,
            other_info_one,
            other_info_two,
            remark
        FROM vanx_platf_post_categ
        WHERE parent_categ_id = 0
        AND is_deleted = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="categType != null and categType.trim() != ''">
            AND categ_type = #{categType}
        </if>
        ORDER BY sort_number ASC, created_time ASC
    </select>

</mapper>
