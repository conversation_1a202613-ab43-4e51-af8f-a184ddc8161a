package zx.vanx.social_circle.service;

import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.social_circle.entity.SocialCirclePost;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo;
import zx.vanx.social_circle.request.DefaultCirclePostsPageRequest;
import zx.vanx.social_circle.request.RecentPostsPageRequest;
import zx.vanx.social_circle.request.SocialCirclePostAddRequest;
import zx.vanx.social_circle.vo.SocialCirclePostVO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;

/**
 * <p>
 * 用户圈子帖子表 服务类
 * 提供圈子帖子的创建、查询、管理等核心业务功能
 * 支持文件上传、媒体处理等企业级功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface SocialCirclePostService extends IService<SocialCirclePost> {

    /**
     * 添加社交圈子帖子
     * 
     * @param request 帖子添加请求
     * @param userId 当前用户ID
     * @return 帖子信息VO
     */
    SocialCirclePostVO addPost(SocialCirclePostAddRequest request, Long userId);

    /**
     * 分页查询最近帖子
     * 根据categoryId参数决定查询方式：
     * - 如果传入categoryId：查询指定分类的流量池数据
     * - 如果不传categoryId：查询Redis列表中的未登录用户最近帖子
     *
     * @param request 分页查询请求（包含可选的categoryId参数）
     * @return 分页结果
     */
    PageObj<RedisCirclePostVo> getRecentPosts(RecentPostsPageRequest request);

    /**
     * 获取帖子详情
     * 优先从流量池获取，流量池不存在则从数据库获取
     *
     * @param postId 帖子ID
     * @return 帖子详情信息
     */
    RedisCirclePostVo getPostDetail(Long postId);

    /**
     * 获取某圈子的今日发帖数量
     * 统计指定圈子今天（当前日期）发布的帖子总数
     *
     * @param socialCircleId 圈子ID
     * @return 今日发帖数量
     */
    Long getTodayPostCountByCircleId(Long socialCircleId);

    /**
     * 删除用户帖子
     * @param userId 用户id
     * @param postId 帖子id
     * @return 删除是否成功
     */
    boolean deletePost(Long userId,Long postId);

    /**
     * 获取用户默认圈子的帖子总数
     * 统计用户默认圈子中的帖子数量
     *
     * @param userId 用户ID
     * @return 默认圈子帖子总数
     */
    Long getDefaultCirclePostCount(Long userId);

    /**
     * 获取用户发布的帖子总数
     * 统计用户在所有圈子中发布的帖子数量
     *
     * @param userId 用户ID
     * @return 用户发布的帖子总数
     */
    Long getUserPostCount(Long userId);

    /**
     * 分页查询用户默认圈子下的帖子列表
     * 查询当前用户设置为默认的圈子中的帖子，支持分页和排序
     *
     * @param request 分页查询请求参数
     * @param userId 当前用户ID
     * @return 分页结果
     */
    PageObj<RedisCirclePostVo> getMyDefaultCirclePosts(DefaultCirclePostsPageRequest request, Long userId);

    /**
     * 查询关注用户的最近帖子
     * @param request 分页查询请求参数
     * @return 分页结果
     */
    PageObj<RedisCirclePostVo> getAttentionUserRecentPosts(@Valid RecentPostsPageRequest request);

}
