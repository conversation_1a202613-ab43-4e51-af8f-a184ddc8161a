package zx.vanx.social_circle.redis.key_manager;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.social_circle.entity.SocialCirclePostUsersRelat;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 圈子帖子Redis键管理器
 * 1. 管理帖子与分类的映射关系
 * 2. 管理未登录用户最近帖子缓存（永久保存，无过期时间）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Component
@RequiredArgsConstructor
public class CirclePostKeyManager {

    // 使用RedisCache处理帖子相关操作，避免序列化问题
    private final RedisCache redisCache;

    // 帖子分类
    private static final String POST_CATEGORY_PREFIX = "circle:post:category:";
    // 最近游客帖子
    private static final String RECENT_POSTS_KEY = "circle:post:recent:guest";
    
    // ==================== Sorted Set 设计 ====================
    // 帖子维度前缀 - 存储用户ID和时间戳 {userId: timestamp} 
    private static final String CIRCLE_POST_PREFIX = "circle:post:";
    
    // 用户维度前缀 - 存储帖子ID和时间戳 {postId: timestamp}
    private static final String CIRCLE_USER_PREFIX = "circle:user:";
    
    // 行为类型常量
    private static final String BEHAVIOR_LIKES = "likes";
    private static final String BEHAVIOR_FORWARDS = "forwards";
    private static final String BEHAVIOR_COLLECTS = "collects";
    private static final String BEHAVIOR_COMMENTS = "comments";
    private static final String BEHAVIOR_GIFTS = "gifts";

    /**
     * 获取帖子分类的Redis Key
     * 格式: circle:post:category:{postId}
     *
     * @param postId 帖子ID
     * @return Redis键
     */
    public String getPostCategoryKey(Long postId) {
        return POST_CATEGORY_PREFIX + postId;
    }

    /**
     * 设置帖子的分类ID
     * Key格式: circle:post:category:{postId}
     * Value: categoryId
     *
     * @param postId     帖子ID
     * @param categoryId 分类ID
     */
    public void setPostCategory(Long postId, Long categoryId) {
        if (postId == null || categoryId == null) {
            return;
        }
        String key = getPostCategoryKey(postId);
        redisCache.setCacheObject(key, categoryId);
    }

    /**
     * 获取帖子的分类ID
     * 通过Key获取对应的分类ID
     *
     * @param postId 帖子ID
     * @return 分类ID
     */
    public Long getPostCategory(Long postId) {
        if (postId == null) {
            return null;
        }
        String key = getPostCategoryKey(postId);
        return redisCache.getCacheObject(key);
    }

    /**
     * 删除帖子的分类映射
     *
     * @param postId 帖子ID
     */
    public void deletePostCategory(Long postId) {
        if (postId == null) {
            return;
        }
        String key = getPostCategoryKey(postId);
        redisCache.deleteObject(key);
    }

    /**
     * 判断帖子是否存在分类映射
     *
     * @param postId 帖子ID
     * @return 是否存在
     */
    public boolean hasPostCategory(Long postId) {
        if (postId == null) {
            return false;
        }
        String key = getPostCategoryKey(postId);
        return redisCache.hasKey(key);
    }

    // ==================== 未登录用户最近帖子缓存管理 ====================

    /**
     * 设置未登录用户的最近帖子列表
     * 新帖子会被添加到列表头部，保证最新的在前面
     * 数据永久保存，无过期时间
     *
     * @param posts 帖子列表（已按创建时间降序排序）
     */
    public void setRecentPosts(List<RedisCirclePostVo> posts) {
        if (posts == null || posts.isEmpty()) {
            return;
        }
        // 使用redisCache保存帖子列表，不设置过期时间
        redisCache.setCacheObject(RECENT_POSTS_KEY, posts);
    }

    /**
     * 添加单个帖子到最近帖子列表头部
     * 新帖子添加到列表开头，保证最新的在前面
     *
     * @param post 新帖子
     */
    public void addRecentPost(RedisCirclePostVo post) {
        if (post == null) {
            return;
        }
        // 获取现有列表
        List<RedisCirclePostVo> posts = getAllRecentPosts();
        if (posts == null) {
            posts = new ArrayList<>();
        }
        // 将新帖子添加到列表开头
        posts.add(0, post);
        // 重新设置整个列表（不设置过期时间）
        setRecentPosts(posts);
    }

    /**
     * 获取未登录用户的最近帖子列表（分页）
     * 返回的列表已经按时间排序（最新的在前面）
     *
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 帖子列表
     */
    public List<RedisCirclePostVo> getRecentPosts(Long pageNum, Long pageSize) {
        if (pageNum == null || pageNum <= 0 || pageSize == null || pageSize <= 0) {
            return new ArrayList<>();
        }
        
        List<RedisCirclePostVo> allPosts = getAllRecentPosts();
        if (allPosts == null || allPosts.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 计算起始索引和结束索引
        int start = (int) ((pageNum - 1) * pageSize);
        int end = (int) (start + pageSize);
        
        // 确保索引在有效范围内
        int size = allPosts.size();
        if (start >= size) {
            return new ArrayList<>();
        }
        
        end = Math.min(end, size);
        return new ArrayList<>(allPosts.subList(start, end));
    }

    /**
     * 获取未登录用户的所有最近帖子
     *
     * @return 帖子列表
     */
    public List<RedisCirclePostVo> getAllRecentPosts() {
        List<RedisCirclePostVo> posts = redisCache.getCacheObject(RECENT_POSTS_KEY);
        return posts != null ? posts : new ArrayList<>();
    }

    /**
     * 获取最近帖子列表的总数
     *
     * @return 列表长度
     */
    public Long getRecentPostsCount() {
        List<RedisCirclePostVo> posts = getAllRecentPosts();
        return posts == null ? 0L : (long) posts.size();
    }

    /**
     * 删除未登录用户的最近帖子缓存
     */
    public void deleteRecentPosts() {
        redisCache.deleteObject(RECENT_POSTS_KEY);
    }

    /**
     * 判断是否存在未登录用户的最近帖子缓存
     *
     * @return 是否存在
     */
    public boolean hasRecentPosts() {
        return redisCache.hasKey(RECENT_POSTS_KEY);
    }

    /**
     * 从最近帖子列表中删除指定的帖子
     *
     * @param postId 要删除的帖子ID
     * @return 删除是否成功
     */
    public boolean removeRecentPost(Long postId) {
        if (postId == null) {
            return false;
        }

        try {
            // 获取当前最近帖子列表
            List<RedisCirclePostVo> posts = getAllRecentPosts();
            if (posts == null || posts.isEmpty()) {
                return true; // 列表为空，认为删除成功
            }

            // 查找并删除指定帖子
            boolean removed = posts.removeIf(post -> 
                post != null && postId.equals(post.getCirclePostId())
            );

            if (removed) {
                // 如果删除了帖子，更新Redis中的列表
                if (posts.isEmpty()) {
                    // 如果列表为空，删除整个键
                    redisCache.deleteObject(RECENT_POSTS_KEY);
                } else {
                    // 否则更新列表
                    redisCache.setCacheObject(RECENT_POSTS_KEY, posts);
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // ==================== 用户帖子行为缓存管理 ====================

    // ==================== Redis Key 生成方法 ====================
    
    /**
     * 获取帖子行为的Redis Key
     * 格式: circle:post:{postId}:{behavior}
     */
    private String getPostBehaviorKey(Long postId, String behavior) {
        return CIRCLE_POST_PREFIX + postId + ":" + behavior;
    }
    
    /**
     * 获取用户行为的Redis Key  
     * 格式: circle:user:{userId}:{behavior}
     */
    private String getUserBehaviorKey(Long userId, String behavior) {
        return CIRCLE_USER_PREFIX + userId + ":" + behavior;
    }
    

    /**
     * 添加用户点赞帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void addUserLikePost(Long postId, Long userId) {
        addUserBehavior(postId, userId, BEHAVIOR_LIKES);
    }

    /**
     * 移除用户点赞帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void removeUserLikePost(Long postId, Long userId) {
        removeUserBehavior(postId, userId, BEHAVIOR_LIKES);
    }

    /**
     * 检查用户是否点赞了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否点赞
     */
    public boolean isUserLikedPost(Long postId, Long userId) {
        return hasUserBehavior(postId, userId, BEHAVIOR_LIKES);
    }

    /**
     * 获取帖子的点赞用户数量
     *
     * @param postId 帖子ID
     * @return 点赞数量
     */
    public Long getPostLikesCount(Long postId) {
        return getBehaviorCount(postId, BEHAVIOR_LIKES);
    }

    /**
     * 添加用户转发帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void addUserForwardPost(Long postId, Long userId) {
        addUserBehavior(postId, userId, BEHAVIOR_FORWARDS);
    }

    /**
     * 移除用户转发帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void removeUserForwardPost(Long postId, Long userId) {
        removeUserBehavior(postId, userId, BEHAVIOR_FORWARDS);
    }

    /**
     * 检查用户是否转发了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否转发
     */
    public boolean isUserForwardedPost(Long postId, Long userId) {
        return hasUserBehavior(postId, userId, BEHAVIOR_FORWARDS);
    }

    /**
     * 获取帖子的转发用户数量
     *
     * @param postId 帖子ID
     * @return 转发数量
     */
    public Long getPostForwardsCount(Long postId) {
        return getBehaviorCount(postId, BEHAVIOR_FORWARDS);
    }

    /**
     * 添加用户收藏帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void addUserCollectPost(Long postId, Long userId) {
        addUserBehavior(postId, userId, BEHAVIOR_COLLECTS);
    }

    /**
     * 移除用户收藏帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void removeUserCollectPost(Long postId, Long userId) {
        removeUserBehavior(postId, userId, BEHAVIOR_COLLECTS);
    }

    /**
     * 检查用户是否收藏了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否收藏
     */
    public boolean isUserCollectedPost(Long postId, Long userId) {
        return hasUserBehavior(postId, userId, BEHAVIOR_COLLECTS);
    }

    /**
     * 获取帖子的收藏用户数量
     *
     * @param postId 帖子ID
     * @return 收藏数量
     */
    public Long getPostCollectsCount(Long postId) {
        return getBehaviorCount(postId, BEHAVIOR_COLLECTS);
    }

    /**
     * 添加用户评论帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void addUserCommentPost(Long postId, Long userId) {
        addUserBehavior(postId, userId, BEHAVIOR_COMMENTS);
    }

    /**
     * 检查用户是否评论了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否评论
     */
    public boolean isUserCommentedPost(Long postId, Long userId) {
        return hasUserBehavior(postId, userId, BEHAVIOR_COMMENTS);
    }

    /**
     * 获取帖子的评论用户数量
     *
     * @param postId 帖子ID
     * @return 评论数量
     */
    public Long getPostCommentsCount(Long postId) {
        return getBehaviorCount(postId, BEHAVIOR_COMMENTS);
    }

    /**
     * 添加用户送礼帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    public void addUserGiftPost(Long postId, Long userId) {
        addUserBehavior(postId, userId, BEHAVIOR_GIFTS);
    }

    /**
     * 检查用户是否给帖子送礼
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否送礼
     */
    public boolean isUserGiftedPost(Long postId, Long userId) {
        return hasUserBehavior(postId, userId, BEHAVIOR_GIFTS);
    }

    /**
     * 获取帖子的送礼用户数量
     *
     * @param postId 帖子ID
     * @return 送礼数量
     */
    public Long getPostGiftsCount(Long postId) {
        return getBehaviorCount(postId, BEHAVIOR_GIFTS);
    }

    /**
     * 批量更新用户帖子行为
     * 根据SocialCirclePostUsersRelat实体批量更新Redis中的用户行为数据
     *
     * @param postUsersRelat 用户帖子关系实体
     */
    public void updateUserPostBehavior(SocialCirclePostUsersRelat postUsersRelat) {
        if (postUsersRelat == null || postUsersRelat.getPostId() == null || postUsersRelat.getFromUserId() == null) {
            return;
        }

        Long postId = postUsersRelat.getPostId();
        Long userId = postUsersRelat.getFromUserId();

        // 处理点赞
        if (Boolean.TRUE.equals(postUsersRelat.getIsLike())) {
            addUserLikePost(postId, userId);
        } else {
            removeUserLikePost(postId, userId);
        }

        // 处理转发
        if (Boolean.TRUE.equals(postUsersRelat.getIsForwarding())) {
            addUserForwardPost(postId, userId);
        } else {
            removeUserForwardPost(postId, userId);
        }

        // 处理收藏
        if (Boolean.TRUE.equals(postUsersRelat.getIsCollect())) {
            addUserCollectPost(postId, userId);
        } else {
            removeUserCollectPost(postId, userId);
        }

        // 处理评论
        if (Boolean.TRUE.equals(postUsersRelat.getIsComment())) {
            addUserCommentPost(postId, userId);
        }

        // 处理送礼
        if (Boolean.TRUE.equals(postUsersRelat.getIsGift())) {
            addUserGiftPost(postId, userId);
        }
    }

    /**
     * 清理帖子相关的所有用户行为缓存
     * 当帖子被删除时调用
     *
     * @param postId 帖子ID
     */
    public void clearPostBehaviorCache(Long postId) {
        if (postId == null) {
            return;
        }

        // 删除帖子的所有行为记录
        redisCache.deleteObject(getPostBehaviorKey(postId, BEHAVIOR_LIKES));
        redisCache.deleteObject(getPostBehaviorKey(postId, BEHAVIOR_FORWARDS));
        redisCache.deleteObject(getPostBehaviorKey(postId, BEHAVIOR_COLLECTS));
        redisCache.deleteObject(getPostBehaviorKey(postId, BEHAVIOR_COMMENTS));
        redisCache.deleteObject(getPostBehaviorKey(postId, BEHAVIOR_GIFTS));
    }

    /**
     * 清理用户相关的所有帖子行为缓存
     * 当用户被删除时调用
     *
     * @param userId 用户ID
     */
    public void clearUserBehaviorCache(Long userId) {
        if (userId == null) {
            return;
        }

        // 删除用户的所有行为记录
        redisCache.deleteObject(getUserBehaviorKey(userId, BEHAVIOR_LIKES));
        redisCache.deleteObject(getUserBehaviorKey(userId, BEHAVIOR_FORWARDS));
        redisCache.deleteObject(getUserBehaviorKey(userId, BEHAVIOR_COLLECTS));
        redisCache.deleteObject(getUserBehaviorKey(userId, BEHAVIOR_COMMENTS));
        redisCache.deleteObject(getUserBehaviorKey(userId, BEHAVIOR_GIFTS));
    }


    // ==================== 通用行为操作方法 ====================
    
    /**
     * 通用添加用户行为方法
     *
     * @param postId   帖子ID
     * @param userId   用户ID
     * @param behavior 行为类型
     */
    private void addUserBehavior(Long postId, Long userId, String behavior) {

        if (postId == null || userId == null || behavior == null) {
            return;
        }

        long timestamp = System.currentTimeMillis();
        String postBehaviorKey = getPostBehaviorKey(postId, behavior);
        String userBehaviorKey = getUserBehaviorKey(userId, behavior);

        // 使用redisCache的有序集合操作
        redisCache.addToSortedSet(postBehaviorKey, userId.toString(), timestamp);
        redisCache.addToSortedSet(userBehaviorKey, postId.toString(), timestamp);

    }
    
    /**
     * 通用移除用户行为方法
     *
     * @param postId   帖子ID
     * @param userId   用户ID
     * @param behavior 行为类型
     */
    private void removeUserBehavior(Long postId, Long userId, String behavior) {
        if (postId == null || userId == null || behavior == null) {
            return;
        }

        String postBehaviorKey = getPostBehaviorKey(postId, behavior);
        String userBehaviorKey = getUserBehaviorKey(userId, behavior);

        // 使用redisCache的有序集合操作
        redisCache.removeFromSortedSet(postBehaviorKey, userId.toString());
        redisCache.removeFromSortedSet(userBehaviorKey, postId.toString());

    }
    
    /**
     * 通用检查用户行为方法
     *
     * @param postId   帖子ID
     * @param userId   用户ID
     * @param behavior 行为类型
     * @return 是否存在该行为
     */
    private boolean hasUserBehavior(Long postId, Long userId, String behavior) {

        if (postId == null || userId == null || behavior == null) {
            return false;
        }

        String postBehaviorKey = getPostBehaviorKey(postId, behavior);

        // 使用redisCache检查有序集合中是否包含指定成员
        return redisCache.containsInSortedSet(postBehaviorKey, userId.toString());

    }
    
    /**
     * 通用获取行为数量方法
     *
     * @param postId   帖子ID
     * @param behavior 行为类型
     * @return 行为数量
     */
    private long getBehaviorCount(Long postId, String behavior) {

        if (postId == null || behavior == null) {
            return 0L;
        }

        String postBehaviorKey = getPostBehaviorKey(postId, behavior);
        // 使用redisCache获取有序集合大小
        Long size = redisCache.getSortedSetSize(postBehaviorKey);
        return size != null ? size : 0L;
    }
    
}
