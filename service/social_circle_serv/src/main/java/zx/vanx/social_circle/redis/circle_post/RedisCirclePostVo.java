package zx.vanx.social_circle.redis.circle_post;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class RedisCirclePostVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long socialCircleId;

    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long circlePostId;

    @ApiModelProperty(value = "创建该圈子的用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "帖子分类ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long postCategId;

    @ApiModelProperty(value = "用户昵称")
    private String userNickName;

    @ApiModelProperty(value = "用户头像")
    private String userAvatar;

    @ApiModelProperty(value = "帖子名称、标题")
    private String circlePostTitle;

    @ApiModelProperty(value = "帖子的状态1、下架2、上架3、删除")
    private String postStatus;

    @ApiModelProperty(value = "填写的帖子描述")
    private String circlePostContent;

    @ApiModelProperty(value = "帖子的点赞数量")
    private Integer numOfLikes;

    @ApiModelProperty(value = "帖子的收藏数量")
    private Integer numOfCollect;

    @ApiModelProperty(value = "帖子的评论数量")
    private Integer numOfComments;

    @ApiModelProperty(value = "帖子的阅读数量")
    private Integer numOfPlayed;

    @ApiModelProperty(value = "帖子的被分享数量")
    private Integer numOfShared;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    // 帖子媒体列表对象
    private List<RedisCirclePostMediaVo> postMediaList;

    @ApiModelProperty(value = "是否关注字段：0-未关注，1-已关注，2-本人")
    private Integer isFollowed;

    @ApiModelProperty(value = "是否登录状态：true-已登录，false-未登录")
    private Boolean isLoggedIn;

}
