package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.request.SocialCircleCreateRequest;
import zx.vanx.social_circle.service.SocialCircleInfoService;
import zx.vanx.social_circle.vo.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 圈子信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Api(tags = "社交圈子")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-info")
public class SocialCircleInfoController {

    private final SocialCircleInfoService socialCircleInfoService;

    @ApiOperation("查询指定圈子的完整详情信息")
    @GetMapping("/current-user")
    public ResultData<SocialCircleDetailVO> getCurrentUserCircleInfo(@RequestParam Long circleId) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 查询指定圈子的完整详情信息（包含地址、媒体、成员信息）
        SocialCircleDetailVO circleDetail = socialCircleInfoService.getCircleDetailById(circleId, userId);
        
        // 如果圈子不存在或用户无权限查看，返回提示信息
        if (circleDetail == null) {
            return ResultData.info("圈子不存在或您无权限查看该圈子", null);
        }
        
        // 返回完整的圈子详情信息
        return ResultData.ok(circleDetail).Message("圈子详情查询成功");
    }

    @ApiOperation("查询我的默认圈子")
    @GetMapping("/default-circle")
    public ResultData<DefaultCircleVO> getMyDefaultCircle() {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 查询用户设置的默认圈子信息（包含圈子基本信息、成员数、活跃数等）
        DefaultCircleVO defaultCircle = socialCircleInfoService.getMyDefaultCircle(userId);
        
        // 如果用户没有设置默认圈子，返回提示信息
        if (defaultCircle == null) {
            return ResultData.info("您尚未设置默认圈子或还未加入任何圈子", null);
        }
        
        // 返回默认圈子信息
        return ResultData.ok(defaultCircle).Message("默认圈子查询成功");
    }

    @ApiOperation("创建新的社交圈子")
    @PostMapping("/create")
    public ResultData<SocialCircleCreateResultVO> createSocialCircle(@Valid @RequestBody SocialCircleCreateRequest request) {
        
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 调用Service层创建圈子（包含实名认证检查）
        SocialCircleCreateResultVO result = socialCircleInfoService.createSocialCircle(request, userId);
        
        // 根据认证和创建结果返回不同响应
        if (result.getSuccess()) {
            return ResultData.ok(result).Message(result.getMessage());
        } else {
            return ResultData.info(result.getMessage(), result);
        }
    }

    @ApiOperation("查询我的圈子列表")
    @GetMapping("/list")
    public ResultData<List<SocialCircleInfoVO>> getMtSocialCircleList() {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层查询用户的圈子列表（包含创建的和加入的圈子）
        List<SocialCircleInfoVO> socialCircleList = socialCircleInfoService.getMtSocialCircleList(userId);

        // 如果用户没有任何圈子，返回提示信息
        if (socialCircleList == null || socialCircleList.isEmpty()) {
            return ResultData.info("您还没有创建或加入任何圈子", socialCircleList);
        }

        // 返回圈子列表信息
        return ResultData.ok(socialCircleList).Message("圈子列表查询成功");
    }

    @ApiOperation("查询圈子添加时的按钮权限")
    @GetMapping("/member-button-permissions")
    public ResultData<List<CircleFunctionMenuVO>> getMemberButtonPermissions(
            @RequestParam 
            @ApiParam(value = "圈子ID", required = true, example = "1001") 
            Long socialCircleId) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层查询普通成员的按钮权限
        List<CircleFunctionMenuVO> buttonPermissions = socialCircleInfoService.getMemberButtonPermissions(socialCircleId, userId);

        // 如果没有找到权限或用户无权限，返回提示信息
        if (buttonPermissions == null || buttonPermissions.isEmpty()) {
            return ResultData.info("暂无可用的圈子按钮权限或您无权限查看", buttonPermissions);
        }

        // 返回按钮权限列表
        return ResultData.ok(buttonPermissions).Message("圈子添加时的按钮权限查询成功");
    }

    /*@ApiOperation("条件搜索圈子列表")
    @PostMapping("/search")
    public ResultData<PageObj<SocialCircleSearchVO>> searchCircles(@Valid @RequestBody SocialCircleSearchRequest request) {
        
        // 调用Service层进行条件搜索
        PageObj<SocialCircleSearchVO> pageResult = socialCircleInfoService.searchCircles(request);
        
        // 如果查询结果为空，返回提示信息
        if (pageResult == null || pageResult.getRecords() == null || pageResult.getRecords().isEmpty()) {
            return ResultData.info("没有找到符合条件的圈子", pageResult);
        }
        
        // 返回搜索结果
        return ResultData.ok(pageResult).Message("圈子搜索成功，共找到" + pageResult.getTotal() + "个圈子");
    }
*/

}

