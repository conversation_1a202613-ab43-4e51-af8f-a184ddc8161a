package zx.vanx.social_circle.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import zx.vanx.social_circle.entity.SocialCircleAddress;
import zx.vanx.social_circle.entity.SocialCircleInfo;
import zx.vanx.social_circle.entity.SocialCircleMedia;
import zx.vanx.social_circle.entity.SocialCircleMembers;

import java.util.List;

/**
 * <p>
 * 社交圈子详情信息VO
 * 包含圈子基本信息、地址信息、媒体信息、成员信息的完整详情对象
 * 用于前端展示圈子的全部相关信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "SocialCircleDetailVO对象", description = "社交圈子详情信息")
public class SocialCircleDetailVO {

    // ==================== 圈子基本信息 ====================

    @ApiModelProperty(value = "圈子基本信息", notes = "包含圈子的基础信息：ID、名称、描述、状态等")
    private SocialCircleInfo circleInfo;

    // ==================== 圈子地址信息 ====================

    @ApiModelProperty(value = "圈子地址信息", notes = "圈子的地理位置信息，可能为空")
    private SocialCircleAddress addressInfo;

    // ==================== 圈子媒体信息列表 ====================

    @ApiModelProperty(value = "圈子媒体信息列表", notes = "圈子的展示图片、视频等媒体资源列表")
    private List<SocialCircleMedia> mediaList;

    // ==================== 圈子成员信息列表 ====================

    @ApiModelProperty(value = "圈子成员信息列表", notes = "圈子的所有成员信息，包含圈主、管理员、普通成员")
    private List<SocialCircleMembers> memberList;

    // ==================== 统计信息 ====================

    @ApiModelProperty(value = "成员总数", notes = "圈子当前的成员总数量")
    private Integer totalMemberCount;

    @ApiModelProperty(value = "媒体总数", notes = "圈子当前的媒体资源总数量")
    private Integer totalMediaCount;

    @ApiModelProperty(value = "是否有地址信息", notes = "标识圈子是否设置了地址信息")
    private Boolean hasAddressInfo;

}