package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo;
import zx.vanx.social_circle.request.DefaultCirclePostsPageRequest;
import zx.vanx.social_circle.request.RecentPostsPageRequest;
import zx.vanx.social_circle.request.SocialCirclePostAddRequest;
import zx.vanx.social_circle.service.SocialCirclePostService;
import zx.vanx.social_circle.vo.SocialCirclePostVO;

import javax.validation.Valid;

/**
 * <p>
 * 用户圈子帖子表 前端控制器
 * 提供圈子帖子的创建、查询、管理等RESTful API接口
 * 支持文件上传、媒体处理等功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Api(tags = "社交圈子帖子")
@RestController
@RequestMapping("/social_circle/social-circle-post")
public class SocialCirclePostController {

    @Autowired
    private SocialCirclePostService socialCirclePostService;

    @ApiOperation("查询最近帖子")
    @PostMapping("/recent-posts")
    public ResultData<PageObj<RedisCirclePostVo>> getRecentPosts(@Valid @RequestBody RecentPostsPageRequest request) {

        PageObj<RedisCirclePostVo> pageObj = socialCirclePostService.getRecentPosts(request);

        return ResultData.ok(pageObj).Message("查询成功");

    }

    @ApiOperation("查询关注用户的最近帖子")
    @PostMapping("/attention-user-recent-posts")
    public ResultData<PageObj<RedisCirclePostVo>> getAttentionUserRecentPosts(@Valid @RequestBody RecentPostsPageRequest request) {

        PageObj<RedisCirclePostVo> pageObj = socialCirclePostService.getAttentionUserRecentPosts(request);

        return ResultData.ok(pageObj).Message("查询成功");

    }

    @ApiOperation("添加社交圈子帖子")
    @PostMapping("/add")
    public ResultData<SocialCirclePostVO> addPost(@Valid @RequestBody SocialCirclePostAddRequest request) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用服务层添加帖子
        SocialCirclePostVO postVO = socialCirclePostService.addPost(request, userId);

        return ResultData.ok(postVO).Message("帖子发布成功");
    }

    @ApiOperation("获取帖子详情")
    @GetMapping("/detail/{postId}")
    public ResultData<RedisCirclePostVo> getPostDetail(@PathVariable Long postId) {

        // 调用服务层获取帖子详情
        RedisCirclePostVo postDetail = socialCirclePostService.getPostDetail(postId);

        return ResultData.ok(postDetail).Message("帖子详情查询成功");
    }

    @ApiOperation("删除用户的帖子")
    @DeleteMapping("/delete/{postId}")
    public ResultData<Object> deletePost(@PathVariable Long postId) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用服务层删除用户帖子
        boolean result = socialCirclePostService.deletePost(userId, postId);

        if (result) {
            return ResultData.ok().Message("帖子删除成功");
        } else {
            return ResultData.error("帖子删除失败");
        }
    }

    @ApiOperation("默认圈子帖子列表")
    @PostMapping("/my-default-circle-posts")
    public ResultData<PageObj<RedisCirclePostVo>> getMyDefaultCirclePosts(@Valid @RequestBody DefaultCirclePostsPageRequest request) {
        
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 调用服务层查询默认圈子帖子列表
        PageObj<RedisCirclePostVo> pageObj = socialCirclePostService.getMyDefaultCirclePosts(request, userId);
        
        return ResultData.ok(pageObj).Message("查询成功");
    }

    @ApiOperation("默认圈子帖子列表（userId）")
    @PostMapping("/user-default-circle-posts/{userId}")
    public ResultData<PageObj<RedisCirclePostVo>> getUserDefaultCirclePosts(
            @PathVariable Long userId,
            @Valid @RequestBody DefaultCirclePostsPageRequest request) {
        
        // 调用服务层查询指定用户的默认圈子帖子列表
        PageObj<RedisCirclePostVo> pageObj = socialCirclePostService.getMyDefaultCirclePosts(request, userId);
        
        return ResultData.ok(pageObj).Message("查询成功");
    }

    @ApiOperation("获取今日发帖数量")
    @GetMapping("/today-count")
    public ResultData<Long> getTodayPostCount() {

        // 调用服务层获取今日发帖数量
        Long todayCount = socialCirclePostService.getTodayPostCount();

        return ResultData.ok(todayCount).Message("今日发帖数量查询成功");
    }

}

