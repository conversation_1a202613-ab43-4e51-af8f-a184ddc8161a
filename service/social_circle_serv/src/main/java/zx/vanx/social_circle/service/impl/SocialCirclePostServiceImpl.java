package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.common_util.utils.ObjectUtil;
import zx.vanx.common_util.utils.PageHelper;
import zx.vanx.common_util.utils.StringUtil;
import zx.vanx.service.MinioService;
import zx.vanx.social_circle.entity.*;
import zx.vanx.social_circle.mapper.SocialCirclePostMapper;
import zx.vanx.social_circle.mapper.SocialCirclePostMediaMapper;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostMediaVo;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo;
import zx.vanx.social_circle.redis.key_manager.CirclePostKeyManager;
import zx.vanx.social_circle.redis.key_manager.PostTrafficPoolKeyManager;
import zx.vanx.social_circle.request.DefaultCirclePostsPageRequest;
import zx.vanx.social_circle.request.RecentPostsPageRequest;
import zx.vanx.social_circle.request.SocialCirclePostAddRequest;
import zx.vanx.social_circle.request.SocialCirclePostMediaRequest;
import zx.vanx.social_circle.service.*;
import zx.vanx.social_circle.vo.DefaultCircleVO;
import zx.vanx.social_circle.vo.SocialCirclePostVO;
import zx.vanx.user_permiss.client.IdolFansRelatClient;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserAttentionPersonVo;
import zx.vanx.user_permiss.vo.UserDetail;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户圈子帖子表 服务实现类
 * 提供圈子帖子的创建、查询、管理等核心业务功能
 * 支持帖子分类关联、快表维护、权限验证等企业级功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class SocialCirclePostServiceImpl extends ServiceImpl<SocialCirclePostMapper, SocialCirclePost> implements SocialCirclePostService {

    private final SocialCircleInfoService socialCircleInfoService;
    private final SocialCircleMembersService socialCircleMembersService;
    private final SocialCirclePostFastService socialCirclePostFastService;
    private final SocialCirclePostCommsService socialCirclePostCommsService;
    private final SocialCirclePostMediaService socialCirclePostMediaService;
    private final MinioService minioService;
    private final RedisCache redisCache;
    private final PostTrafficPoolKeyManager postTrafficPoolKeyManager;
    private final CirclePostKeyManager circlePostKeyManager;
    private final SocialCirclePostMediaMapper socialCirclePostMediaMapper;
    private final IdolFansRelatClient idolFansRelatClient;

    /**
     * 添加社交圈子帖子
     *
     * @param request 帖子添加请求
     * @param userId  当前用户ID
     * @return 帖子信息VO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCirclePostVO addPost(SocialCirclePostAddRequest request, Long userId) {

        // 1. 基础参数验证
        validateAddPostRequest(request, userId);

        // 2. 业务规则验证
        validateAddPostBusinessRules(request, userId);

        // 3. 创建帖子基本信息
        SocialCirclePost post = createPostInfo(request, userId);

        // 4. 创建帖子快表记录
        SocialCirclePostFast postFast = createPostFastInfo(post, userId);

        // 5. 创建帖子媒体信息表
        List<RedisCirclePostMediaVo> postMedias = createPostMedias(post.getCirclePostId(), request.getPostMediaList());

        // 6. 将帖子添加到初始流量池,以及对应的redis中
        addPostToTrafficPool(post, postFast, postMedias);

        // 7. 转换为VO返回
        return convertToPostVO(post);
    }

    /**
     * 创建帖子媒体信息
     *
     * @param circlePostId  帖子id
     * @param postMediaList 帖子媒体信息请求列表
     */
    private List<RedisCirclePostMediaVo> createPostMedias(Long circlePostId, List<SocialCirclePostMediaRequest> postMediaList) {

        if (!CollectionUtils.isEmpty(postMediaList)) {
            return postMediaList.stream().map(postMediaRequest -> {

                SocialCirclePostMedia postMedia = BeanUtil.copyProperties(postMediaRequest, SocialCirclePostMedia::new);
                postMedia.setCirclePostId(circlePostId);
                postMedia.setSortNumber("0");
                socialCirclePostMediaMapper.insert(postMedia);
                RedisCirclePostMediaVo redisCirclePostMediaVo = new RedisCirclePostMediaVo();
                BeanUtils.copyProperties(postMediaRequest, redisCirclePostMediaVo);
                redisCirclePostMediaVo.setPostMediaId(postMedia.getCirclePostId());
                return redisCirclePostMediaVo;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 将帖子添加到流量池
     *
     * @param post     帖子实体
     * @param postFast 帖子快表记录
     */
    private void addPostToTrafficPool(SocialCirclePost post, SocialCirclePostFast postFast, List<RedisCirclePostMediaVo> postMedias) {

        UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + post.getUserId());


        // 构建 Redis帖子信息
        RedisCirclePostVo redisPostVo = new RedisCirclePostVo();
        redisPostVo.setCirclePostId(post.getCirclePostId());
        redisPostVo.setSocialCircleId(post.getSocialCircleId());
        redisPostVo.setUserId(post.getUserId());
        redisPostVo.setUserNickName(post.getUserName());
        redisPostVo.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
        redisPostVo.setCirclePostTitle(post.getCirclePostTitle());
        redisPostVo.setPostStatus(post.getPostStatus());
        redisPostVo.setCirclePostContent(post.getCirclePostContent());
        redisPostVo.setNumOfLikes(postFast.getNumOfLikes());
        redisPostVo.setNumOfCollect(postFast.getNumOfCollect());
        redisPostVo.setNumOfComments(postFast.getNumOfComments());
        redisPostVo.setNumOfShared(postFast.getNumOfShared());
        redisPostVo.setNumOfPlayed(postFast.getNumOfPlayed());
        redisPostVo.setCreatedTime(post.getCreatedTime());
        redisPostVo.setPostMediaList(postMedias);

        // 1. 添加到分类流量池
        postTrafficPoolKeyManager.addPostToTrafficPool(post.getPostCategId(), post.getCirclePostId(), redisPostVo);

        // 2. 添加到未登录用户最近帖子列表
        circlePostKeyManager.addRecentPost(redisPostVo);

        // 3. 设置帖子分类映射
        circlePostKeyManager.setPostCategory(post.getCirclePostId(), post.getPostCategId());
    }

    /**
     * 基础参数验证
     *
     * @param request 帖子添加请求
     * @param userId  用户ID
     */
    private void validateAddPostRequest(SocialCirclePostAddRequest request, Long userId) {
        if (request == null) {
            throw new ZxException(400, "帖子添加请求不能为空");
        }

        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }

        if (request.getSocialCircleId() == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }

        if (request.getPostCategId() == null) {
            throw new ZxException(400, "帖子分类ID不能为空");
        }

        List<SocialCirclePostMediaRequest> postMediaList = request.getPostMediaList();

        if (!CollectionUtils.isEmpty(postMediaList)) {
            for (SocialCirclePostMediaRequest socialCirclePostMediaRequest : postMediaList) {
                if (socialCirclePostMediaRequest.getMediaType() == null) {
                    throw new ZxException(400, "帖子媒体类型不能为空");
                }
                if (socialCirclePostMediaRequest.getMediaUrl() == null) {
                    throw new ZxException(400, "帖子媒体URL不能为空");
                }
            }
        }

    }

    /**
     * 业务规则验证
     *
     * @param request 帖子添加请求
     * @param userId  用户ID
     */
    private void validateAddPostBusinessRules(SocialCirclePostAddRequest request, Long userId) {
        // 验证圈子是否存在
        SocialCircleInfo circleInfo = socialCircleInfoService.getById(request.getSocialCircleId());
        if (circleInfo == null) {
            throw new ZxException(404, "圈子不存在");
        }

        // 验证用户是否为圈子成员
        SocialCircleMembers member = getCircleMember(request.getSocialCircleId(), userId);
        if (member == null) {
            throw new ZxException(403, "您不是该圈子的成员，无法发布帖子");
        }
    }

    /**
     * 获取圈子成员信息
     *
     * @param socialCircleId 圈子ID
     * @param userId         用户ID
     * @return 圈子成员信息
     */
    private SocialCircleMembers getCircleMember(Long socialCircleId, Long userId) {
        LambdaQueryWrapper<SocialCircleMembers> memberWrapper = new LambdaQueryWrapper<>();
        memberWrapper.eq(SocialCircleMembers::getSocialCircleId, socialCircleId)
                .eq(SocialCircleMembers::getUserId, userId)
                .eq(SocialCircleMembers::getUserStatus, "已入圈")
                .eq(SocialCircleMembers::getIsDeleted, 0);

        return socialCircleMembersService.getOne(memberWrapper);
    }

    /**
     * 创建帖子基本信息
     *
     * @param request 帖子添加请求
     * @param userId  用户ID
     * @return 创建的帖子实体
     */
    private SocialCirclePost createPostInfo(SocialCirclePostAddRequest request, Long userId) {

        UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + userId);

        // 创建帖子实体
        SocialCirclePost post = new SocialCirclePost();
        BeanUtils.copyProperties(request, post);

        if (!ObjectUtil.isEmpty(userDetail)) {
            post.setUserName(userDetail.getUserInfo().getUserNickname());
        }
        // 设置帖子基本信息
        post.setUserId(userId);
        post.setPostStatus("上架"); // 默认上架状态
        post.setAddSource("自定义添加");
        post.setCreatorId(userId);
        post.setCreatedTime(LocalDateTime.now());
        post.setModifiedTime(LocalDateTime.now());
        post.setIsDeleted(0);

        // 保存帖子
        boolean saveResult = this.save(post);
        if (!saveResult) {
            throw new ZxException(500, "帖子保存失败，请稍后重试");
        }

        return post;
    }

    /**
     * 创建帖子快表记录
     *
     * @param post   帖子实体
     * @param userId 用户ID
     */
    private SocialCirclePostFast createPostFastInfo(SocialCirclePost post, Long userId) {

        // 创建帖子快表记录
        SocialCirclePostFast postFast = new SocialCirclePostFast();
        postFast.setUserId(userId);
        postFast.setUserNickName(post.getUserName());
        postFast.setCirclePostId(post.getCirclePostId());
        postFast.setCirclePostTitle(post.getCirclePostTitle());
        postFast.setPostCoverUrl(post.getPostCoverUrl());
        postFast.setRecommendValue("0"); // 初始推荐值
        postFast.setNumOfLikes(0);
        postFast.setNumOfCollect(0);
        postFast.setNumOfComments(0);
        postFast.setNumOfPlayed(0);
        postFast.setNumOfShared(0);
        postFast.setCreatorId(userId);
        postFast.setCreatedTime(LocalDateTime.now());
        postFast.setModifiedTime(LocalDateTime.now());
        postFast.setIsDeleted(0);

        boolean saveResult = socialCirclePostFastService.save(postFast);
        if (!saveResult) {
            throw new ZxException(500, "帖子快表记录创建失败，请稍后重试");
        }

        return postFast;
    }

    /**
     * 转换为VO对象
     *
     * @param post 帖子实体
     * @return 帖子VO对象
     */
    private SocialCirclePostVO convertToPostVO(SocialCirclePost post) {
        SocialCirclePostVO postVO = new SocialCirclePostVO();
        BeanUtils.copyProperties(post, postVO);
        return postVO;
    }

    /**
     * 分页查询最近帖子
     * 根据categoryId参数决定查询方式：
     * - 如果传入categoryId：查询指定分类的流量池数据
     * - 如果不传categoryId：查询Redis列表中的未登录用户最近帖子
     *
     * @param request 分页查询请求（包含可选的categoryId参数）
     * @return 分页结果
     */
    @Override
    public PageObj<RedisCirclePostVo> getRecentPosts(RecentPostsPageRequest request) {

        // 根据是否传入categoryId决定查询方式
        if (!ObjectUtil.isEmpty(request.getCategoryId())) {
            // 有分类ID：查询指定分类的流量池数据
            return getRecentPostsByCategory(request);
        } else {
            // 无分类ID：查询Redis列表中的未登录用户最近帖子
            return getRecentPostsFromRedis(request);
        }
    }

    /**
     * 查询指定分类的流量池数据
     *
     * @param request 包含分类ID的查询请求
     * @return 分页结果
     */
    private PageObj<RedisCirclePostVo> getRecentPostsByCategory(RecentPostsPageRequest request) {

        // 获取当前用户信息
        Long currentUserId = null;
        boolean isLoggedIn = false;
        try {
            currentUserId = LoginHelper.getUserId();
            isLoggedIn = true;
        } catch (Exception e) {
            // 未登录用户，继续处理
        }

        // 获取所有初始流量池数据
        Map<String, RedisCirclePostVo> allPosts = postTrafficPoolKeyManager.getPostsFromInitialPool(request.getCategoryId());

        // 过滤并转换为列表
        List<RedisCirclePostVo> sortedPosts = allPosts.values().stream()
                .filter(post -> post != null && post.getCreatedTime() != null)
                .sorted(Comparator.comparing(RedisCirclePostVo::getCreatedTime, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        
        // 填充用户状态信息（登录状态和关注状态）
        fillUserStatusForPosts(sortedPosts, currentUserId, isLoggedIn);

        // 分页
        List<RedisCirclePostVo> paginatedPosts = PageHelper.getPagedList(sortedPosts, request.getPage(), request.getLimit());
        PageObj<RedisCirclePostVo> pageObj = new PageObj<>();
        pageObj.setTotal((long) sortedPosts.size());
        pageObj.setRecords(paginatedPosts);

        return pageObj;
    }

    /**
     * 查询Redis列表中的未登录用户最近帖子
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    private PageObj<RedisCirclePostVo> getRecentPostsFromRedis(RecentPostsPageRequest request) {

        // 获取当前用户信息
        Long currentUserId = null;
        boolean isLoggedIn = false;
        try {
            currentUserId = LoginHelper.getUserId();
            isLoggedIn = true;
        } catch (Exception e) {
            // 未登录用户，继续处理
        }

        // 获取分页数据
        List<RedisCirclePostVo> posts = circlePostKeyManager.getRecentPosts(request.getPage(), request.getLimit());
        
        // 如果Redis中没有数据，从数据库加载并同步到Redis
        if (CollectionUtils.isEmpty(posts)) {

            syncRecentPostsToRedis();
            // 重新从Redis获取数据
            posts = circlePostKeyManager.getRecentPosts(request.getPage(), request.getLimit());
        }

        // 填充用户状态信息（登录状态和关注状态）
        fillUserStatusForPosts(posts, currentUserId, isLoggedIn);

        // 获取总数
        Long total = circlePostKeyManager.getRecentPostsCount();

        // 构建分页对象
        PageObj<RedisCirclePostVo> pageObj = new PageObj<>();
        pageObj.setTotal(total);
        pageObj.setRecords(posts);

        return pageObj;
    }

    /**
     * 获取帖子详情
     * 优先从流量池获取，流量池不存在则从数据库获取
     *
     * @param postId 帖子ID
     * @return 帖子详情信息
     */
    @Override
    public RedisCirclePostVo getPostDetail(Long postId) {

        // 1. 参数验证
        if (postId == null) {
            throw new ZxException(400, "帖子ID不能为空");
        }

        // 2. 获取当前用户信息
        Long currentUserId = null;
        boolean isLoggedIn = false;
        try {
            currentUserId = LoginHelper.getUserId();
            isLoggedIn = true;
        } catch (Exception e) {
            // 未登录用户，继续处理
        }

        // 3. 优先从流量池获取帖子详情
        RedisCirclePostVo postDetail = getPostFromTrafficPool(postId);
        if (postDetail == null) {
            // 4. 流量池中没有，从数据库获取
            postDetail = getPostFromDatabase(postId);
        }

        if (postDetail == null) {
            throw new ZxException(404, "帖子不存在或已被删除");
        }

        // 5. 设置用户状态信息
        postDetail.setIsLoggedIn(isLoggedIn);

        // 6. 设置关注状态
        if (!isLoggedIn) {
            postDetail.setIsFollowed(0); // 未登录，显示未关注
        } else if (currentUserId != null && currentUserId.equals(postDetail.getUserId())) {
            postDetail.setIsFollowed(2); // 本人发布的帖子
        } else if (currentUserId != null) {
            // 调用远程服务查询关注状态
            try {
                boolean isFollowing = idolFansRelatClient.queryUserIsFollower(currentUserId, postDetail.getUserId());
                postDetail.setIsFollowed(isFollowing ? 1 : 0); // 1-已关注, 0-未关注
            } catch (Exception e) {
                log.warn("查询关注状态失败，帖子用户ID: {}, 当前用户ID: {}", postDetail.getUserId(), currentUserId);
                postDetail.setIsFollowed(0); // 异常情况下默认未关注
            }
        } else {
            postDetail.setIsFollowed(0); // 默认未关注
        }

        return postDetail;
    }

    /**
     * 删除用户帖子
     * @param userId 用户id
     * @param postId 帖子id
     * @return 删除是否成功
     */
    @Override
    public boolean deletePost(Long userId,Long postId) {

        // 1. 参数验证
        if (postId == null) {
            throw new ZxException(400, "帖子ID不能为空");
        }

        // 1.验证用户身份是否为圈主、管理员或帖子发起者
        boolean hasPermission = validateDeletePermission(userId, postId);
        if (!hasPermission) {
            throw new ZxException(403, "您没有权限删除该帖子");
        }

        // 2.从流量池中删除对应帖子
        boolean poolDeleteResult = removePostFromTrafficPools(postId);
        if (!poolDeleteResult) {
            log.warn("从流量池删除帖子失败，帖子ID: {}", postId);
        }

        // 3.从redis中删除对应帖子分类
        boolean categoryDeleteResult = deletePostCategoryMapping(postId);
        if (!categoryDeleteResult) {
            log.warn("删除帖子分类映射失败，帖子ID: {}", postId);
        }

        // 4.删除帖子media表中对应的minio文件
        boolean minioDeleteResult = deletePostMinioFiles(postId);
        if (!minioDeleteResult) {
            log.warn("删除帖子MinIO文件失败，帖子ID: {}", postId);
        }

        // 5.删除帖子media表记录
        boolean mediaDeleteResult = deletePostMediaRecords(postId);
        if (!mediaDeleteResult) {
            throw new ZxException(500, "删除帖子媒体记录失败");
        }

        // 6.删除帖子评论表
        boolean commsDeleteResult = deletePostComms(postId);
        if (!commsDeleteResult) {
            throw new ZxException(500, "删除帖子评论表失败");
        }

        // 7.删除帖子fast表记录
        boolean fastDeleteResult = deletePostFastRecord(postId);
        if (!fastDeleteResult) {
            throw new ZxException(500, "删除帖子快表记录失败");
        }

        // 8.删除帖子表记录
        boolean postDeleteResult = deletePostRecord(postId);
        if (!postDeleteResult) {
            throw new ZxException(500, "删除帖子记录失败");
        }

        return true;
    }

    /**
     * 验证用户删除帖子的权限
     * @param userId 当前用户ID
     * @param postId 帖子ID
     * @return 是否有权限删除
     */
    private boolean validateDeletePermission(Long userId, Long postId) {
        // 1. 获取帖子信息
        SocialCirclePost post = this.getById(postId);
        if (post == null) {
            throw new ZxException(404, "帖子不存在或已被删除");
        }
        
        // 2. 如果是帖子发起者，直接允许删除
        if (userId.equals(post.getUserId())) {
            return true;
        }
        
        // 3. 检查是否为圈主或管理员
        LambdaQueryWrapper<SocialCircleMembers> memberWrapper = new LambdaQueryWrapper<>();
        memberWrapper.eq(SocialCircleMembers::getSocialCircleId, post.getSocialCircleId())
                .eq(SocialCircleMembers::getUserId, userId)
                .eq(SocialCircleMembers::getUserStatus, "已入圈")
                .in(SocialCircleMembers::getUserRole, "圈主", "管理员");
        
        SocialCircleMembers member = socialCircleMembersService.getOne(memberWrapper);

        return member != null;
    }

    /**
     * 从流量池中删除帖子
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean removePostFromTrafficPools(Long postId) {
        try {
            // 1. 获取帖子的分类ID
            Long categoryId = circlePostKeyManager.getPostCategory(postId);
            if (categoryId == null) {

                SocialCirclePost socialCirclePost = this.getBaseMapper().selectById(postId);
                categoryId = socialCirclePost.getPostCategId();
                log.warn("获取帖子分类成功，帖子ID: {}", postId);
            }

            boolean anySuccess = false;

            // 2. 从所有流量池中删除帖子
            boolean poolResult = postTrafficPoolKeyManager.removePostFromAllPools(categoryId, postId);
            if (poolResult) {
                anySuccess = true;
                log.info("成功从流量池删除帖子，帖子ID: {}, 分类ID: {}", postId, categoryId);
            }

            // 3. 删除帖子的预推荐用户列表
            boolean usersResult = postTrafficPoolKeyManager.removeRecommendUsers(postId);
            if (usersResult) {
                anySuccess = true;
                log.info("成功删除帖子预推荐用户列表，帖子ID: {}", postId);
            }

            // 4. 从未登录用户最近帖子列表中删除
            boolean recentResult = circlePostKeyManager.removeRecentPost(postId);
            if (recentResult) {
                anySuccess = true;
                log.info("成功从最近帖子列表删除帖子，帖子ID: {}", postId);
            }

            return anySuccess;
        } catch (Exception e) {
            log.error("从流量池删除帖子失败，帖子ID: {}, 错误信息: {}", postId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除帖子分类映射
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean deletePostCategoryMapping(Long postId) {
        try {
            circlePostKeyManager.deletePostCategory(postId);
            log.info("成功删除帖子分类映射，帖子ID: {}", postId);
            return true;
        } catch (Exception e) {
            log.error("删除帖子分类映射失败，帖子ID: {}, 错误信息: {}", postId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除帖子在MinIO中的媒体文件
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean deletePostMinioFiles(Long postId) {

        try {
            // 1. 查询帖子的所有media记录
            LambdaQueryWrapper<SocialCirclePostMedia> mediaWrapper = new LambdaQueryWrapper<>();
            mediaWrapper.eq(SocialCirclePostMedia::getCirclePostId, postId);
            
            List<SocialCirclePostMedia> mediaList = socialCirclePostMediaService.list(mediaWrapper);
            if (CollectionUtils.isEmpty(mediaList)) {
                log.info("帖子没有媒体文件，帖子ID: {}", postId);
                return true;
            }

            // 2. 收集所有需要删除的文件路径
            List<String> filePathsToDelete = new ArrayList<>();
            for (SocialCirclePostMedia media : mediaList) {
                // 添加主媒体文件路径
                if (media.getMediaUrl() != null && !media.getMediaUrl().trim().isEmpty()) {
                    String filePath = StringUtil.extractFilePath(media.getMediaUrl());
                    if (!filePath.trim().isEmpty()) {
                        filePathsToDelete.add(filePath);
                    }
                }
                
                // 添加封面文件路径（如果是视频）
                if (media.getMediaCoverUrl() != null && !media.getMediaCoverUrl().trim().isEmpty()) {
                    String coverPath = StringUtil.extractFilePath(media.getMediaCoverUrl());
                    if (!coverPath.trim().isEmpty()) {
                        filePathsToDelete.add(coverPath);
                    }
                }
            }

            // 3. 批量删除MinIO文件
            if (!filePathsToDelete.isEmpty()) {
                minioService.removeObjects(filePathsToDelete);
                log.info("成功删除帖子MinIO文件，帖子ID: {}, 删除文件数: {}", postId, filePathsToDelete.size());
            }

            return true;
        } catch (Exception e) {
            log.error("删除帖子MinIO文件失败，帖子ID: {}, 错误信息: {}", postId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除帖子媒体记录
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean deletePostMediaRecords(Long postId) {

            LambdaQueryWrapper<SocialCirclePostMedia> mediaWrapper = new LambdaQueryWrapper<>();
            mediaWrapper.eq(SocialCirclePostMedia::getCirclePostId, postId);

            socialCirclePostMediaService.remove(mediaWrapper);
            
            return true;
    }

    /**
     * 删除帖子评论表
     * @param postId 帖子id
     * @return 是否删除成功
     */
    private boolean deletePostComms(Long postId) {

            LambdaQueryWrapper<SocialCirclePostComms> commsWrapper = new LambdaQueryWrapper<>();
            commsWrapper.eq(SocialCirclePostComms::getCommsedObjectId, postId);

            socialCirclePostCommsService.remove(commsWrapper);

            return true;
    }

    /**
     * 从流量池获取帖子详情
     * 按优先级依次查询：初始池 -> 初筛池 -> 放大池 -> 沉底池
     *
     * @param postId 帖子ID
     * @return 帖子详情，如果流量池中不存在则返回null
     */
    private RedisCirclePostVo getPostFromTrafficPool(Long postId) {

        // 1. 获取帖子的分类ID
        Long categoryId = circlePostKeyManager.getPostCategory(postId);
        if (categoryId == null) {
            return null; // 没有分类映射，可能不在流量池中
        }

        // 2. 按优先级依次查询各个流量池
        String[] poolTypes = {
                PostTrafficPoolKeyManager.POOL_INITIAL,
                PostTrafficPoolKeyManager.POOL_FILTERED,
                PostTrafficPoolKeyManager.POOL_AMPLIFIED,
                PostTrafficPoolKeyManager.POOL_BOTTOM
        };

        for (String poolType : poolTypes) {
            Map<String, RedisCirclePostVo> poolData = postTrafficPoolKeyManager.getPostsFromPool(categoryId, poolType);
            if (poolData != null && poolData.containsKey(postId.toString())) {
                return poolData.get(postId.toString());
            }
        }

        return null; // 所有流量池都没有找到
    }

    /**
     * 从数据库获取帖子详情
     * 查询帖子主表、快表、媒体表，组装成RedisCirclePostVo
     *
     * @param postId 帖子ID
     * @return 帖子详情，如果不存在则返回null
     */
    private RedisCirclePostVo getPostFromDatabase(Long postId) {

        // 1. 查询帖子基本信息
        SocialCirclePost post = this.getById(postId);
        if (post == null || post.getIsDeleted() == 1) {
            return null; // 帖子不存在或已删除
        }

        // 2. 查询帖子快表信息（统计数据）
        LambdaQueryWrapper<SocialCirclePostFast> fastWrapper = new LambdaQueryWrapper<>();
        fastWrapper.eq(SocialCirclePostFast::getCirclePostId, postId);
        SocialCirclePostFast postFast = socialCirclePostFastService.getOne(fastWrapper);

        // 3. 查询帖子媒体信息
        List<RedisCirclePostMediaVo> postMedias = getPostMediasFromDatabase(postId);

        // 4. 获取用户详情
        UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + post.getUserId());

        // 5. 组装RedisCirclePostVo
        return convertDatabaseToRedisVo(post, postFast, postMedias, userDetail);
    }

    /**
     * 从数据库获取帖子媒体信息
     *
     * @param postId 帖子ID
     * @return 媒体信息列表
     */
    private List<RedisCirclePostMediaVo> getPostMediasFromDatabase(Long postId) {

        LambdaQueryWrapper<SocialCirclePostMedia> mediaWrapper = new LambdaQueryWrapper<>();
        mediaWrapper.eq(SocialCirclePostMedia::getCirclePostId, postId)
                .orderByAsc(SocialCirclePostMedia::getSortNumber);

        List<SocialCirclePostMedia> mediaList = socialCirclePostMediaMapper.selectList(mediaWrapper);

        if (CollectionUtils.isEmpty(mediaList)) {
            return new ArrayList<>();
        }

        // 转换为RedisCirclePostMediaVo
        return mediaList.stream().map(media -> {
            RedisCirclePostMediaVo mediaVo = new RedisCirclePostMediaVo();
            mediaVo.setPostMediaId(media.getPostMediaId());
            mediaVo.setMediaUrl(media.getMediaUrl());
            mediaVo.setMediaName(media.getMediaName());
            mediaVo.setMediaCoverUrl(media.getMediaCoverUrl());
            mediaVo.setMediaType(media.getMediaType());
            mediaVo.setSortNumber(media.getSortNumber());
            return mediaVo;
        }).collect(Collectors.toList());
    }

    /**
     * 将数据库实体转换为RedisCirclePostVo
     *
     * @param post       帖子实体
     * @param postFast   帖子快表实体
     * @param postMedias 媒体信息列表
     * @param userDetail 用户详情
     * @return RedisCirclePostVo
     */
    private RedisCirclePostVo convertDatabaseToRedisVo(SocialCirclePost post,
                                                       SocialCirclePostFast postFast,
                                                       List<RedisCirclePostMediaVo> postMedias,
                                                       UserDetail userDetail) {

        RedisCirclePostVo redisPostVo = new RedisCirclePostVo();

        // 基本信息
        redisPostVo.setCirclePostId(post.getCirclePostId());
        redisPostVo.setSocialCircleId(post.getSocialCircleId());
        redisPostVo.setUserId(post.getUserId());
        redisPostVo.setCirclePostTitle(post.getCirclePostTitle());
        redisPostVo.setPostStatus(post.getPostStatus());
        redisPostVo.setCirclePostContent(post.getCirclePostContent());
        redisPostVo.setCreatedTime(post.getCreatedTime());

        // 用户信息
        if (userDetail != null) {
            redisPostVo.setUserNickName(userDetail.getUserInfo().getUserNickname());
            redisPostVo.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
        } else {
            // 如果Redis中没有用户信息，使用帖子中的用户名
            redisPostVo.setUserNickName(post.getUserName());
            redisPostVo.setUserAvatar(""); // 默认空头像
        }

        // 统计信息（如果快表存在）
        if (postFast != null) {
            redisPostVo.setNumOfLikes(postFast.getNumOfLikes());
            redisPostVo.setNumOfCollect(postFast.getNumOfCollect());
            redisPostVo.setNumOfComments(postFast.getNumOfComments());
            redisPostVo.setNumOfPlayed(postFast.getNumOfPlayed());
            redisPostVo.setNumOfShared(postFast.getNumOfShared());
        } else {
            // 默认统计数据
            redisPostVo.setNumOfLikes(0);
            redisPostVo.setNumOfCollect(0);
            redisPostVo.setNumOfComments(0);
            redisPostVo.setNumOfPlayed(0);
            redisPostVo.setNumOfShared(0);
        }

        // 媒体信息
        redisPostVo.setPostMediaList(postMedias);

        return redisPostVo;
    }

    /**
     * 删除帖子快表记录
     * 
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean deletePostFastRecord(Long postId) {

            LambdaQueryWrapper<SocialCirclePostFast> fastWrapper = new LambdaQueryWrapper<>();
            fastWrapper.eq(SocialCirclePostFast::getCirclePostId, postId);
            socialCirclePostFastService.remove(fastWrapper);

            return true;
    }

    /**
     * 删除帖子表记录
     * 
     * @param postId 帖子ID
     * @return 删除是否成功
     */
    private boolean deletePostRecord(Long postId) {

            this.removeById(postId);

            return true;
    }

    /**
     * 获取用户默认圈子的帖子总数
     * 统计用户默认圈子中的帖子数量
     *
     * @param userId 用户ID
     * @return 默认圈子帖子总数
     */
    @Override
    public Long getDefaultCirclePostCount(Long userId) {
        if (userId == null) {
            return 0L;
        }
        
        try {
            // 1. 先查询用户的默认圈子ID
            LambdaQueryWrapper<SocialCircleMembers> defaultCircleQuery = new LambdaQueryWrapper<>();
            defaultCircleQuery.eq(SocialCircleMembers::getUserId, userId)
                    .eq(SocialCircleMembers::getIsDefault, true)
                    .eq(SocialCircleMembers::getUserStatus, "已入圈");
            
            SocialCircleMembers defaultMember = socialCircleMembersService.getOne(defaultCircleQuery);
            if (defaultMember == null) {
                return 0L;
            }
            
            // 2. 统计该默认圈子的帖子数量
            LambdaQueryWrapper<SocialCirclePost> countQuery = new LambdaQueryWrapper<>();
            countQuery.eq(SocialCirclePost::getSocialCircleId, defaultMember.getSocialCircleId());
            
            return this.count(countQuery);
            
        } catch (Exception e) {
            log.error("查询用户默认圈子帖子总数失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 获取用户发布的帖子总数
     * 统计用户在所有圈子中发布的帖子数量
     *
     * @param userId 用户ID
     * @return 用户发布的帖子总数
     */
    @Override
    public Long getUserPostCount(Long userId) {
        if (userId == null) {
            return 0L;
        }
        
        try {
            // 统计用户发布的所有帖子数量
            LambdaQueryWrapper<SocialCirclePost> countQuery = new LambdaQueryWrapper<>();
            countQuery.eq(SocialCirclePost::getUserId, userId);
            
            return this.count(countQuery);
            
        } catch (Exception e) {
            log.error("查询用户发布帖子总数失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 分页查询用户默认圈子下的帖子列表
     *
     * @param request 分页查询请求参数
     * @param userId 当前用户ID
     * @return 分页结果
     */
    @Override
    public PageObj<RedisCirclePostVo> getMyDefaultCirclePosts(DefaultCirclePostsPageRequest request, Long userId) {

        try {
            // 1. 基础参数验证
            if (userId == null) {
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            // 2. 查询用户的默认圈子信息
            DefaultCircleVO defaultCircle = socialCircleInfoService.getMyDefaultCircle(userId);
            if (defaultCircle == null || defaultCircle.getCircleInfo() == null) {
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            Long socialCircleId = defaultCircle.getCircleInfo().getSocialCircleId();

            // 3. 构建查询条件
            LambdaQueryWrapper<SocialCirclePost> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SocialCirclePost::getSocialCircleId, socialCircleId);
            queryWrapper.eq(SocialCirclePost::getUserId, userId);

            // 4. 按时间排序（最新的在前面）
            queryWrapper.orderByDesc(SocialCirclePost::getCreatedTime);

            // 5. 执行分页查询
            Page<SocialCirclePost> page = new Page<>(request.getPage(), request.getLimit());
            IPage<SocialCirclePost> pageResult = this.page(page, queryWrapper);


            // 6. 转换为RedisCirclePostVo对象
            List<RedisCirclePostVo> postVoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
                for (SocialCirclePost post : pageResult.getRecords()) {
                    RedisCirclePostVo postVo = convertToRedisCirclePostVo(post);
                    if (postVo != null) {
                        postVoList.add(postVo);
                    }
                }
            }

            // 7. 构建分页结果
            PageObj<RedisCirclePostVo> result = new PageObj<>();
            result.setRecords(postVoList);
            result.setTotal(pageResult.getTotal());

            return result;

        } catch (Exception e) {
            PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
            emptyResult.setTotal(0L);
            emptyResult.setRecords(new ArrayList<>());
            return emptyResult;
        }
    }

    /**
     * 查询关注用户的最近帖子
     * @param request 分页查询请求参数
     * @return 分页结果
     */
    @Override
    public PageObj<RedisCirclePostVo> getAttentionUserRecentPosts(RecentPostsPageRequest request) {
        
        try {
            // 1. 获取当前登录用户ID
            Long currentUserId;
            boolean isLoggedIn;
            try {
                currentUserId = LoginHelper.getUserId();
                isLoggedIn = true;
            } catch (Exception e) {
                // 未登录用户，返回空结果
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            // 2. 通过远程调用获取用户关注的用户列表
            List<UserAttentionPersonVo> attentionUsers = idolFansRelatClient.getAttentionUserList(currentUserId);
            if (CollectionUtils.isEmpty(attentionUsers)) {
                // 用户没有关注任何人，返回空结果
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            // 3. 提取关注用户ID集合
            Set<Long> attentionUserIds = attentionUsers.stream()
                    .map(UserAttentionPersonVo::getUserIdolId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (attentionUserIds.isEmpty()) {
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            // 4. 通过CirclePostKeyManager查询所有最近帖子
            List<RedisCirclePostVo> allRecentPosts = circlePostKeyManager.getAllRecentPosts();
            if (CollectionUtils.isEmpty(allRecentPosts)) {
                PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
                emptyResult.setTotal(0L);
                emptyResult.setRecords(new ArrayList<>());
                return emptyResult;
            }

            // 5. 过滤出关注用户的帖子
            List<RedisCirclePostVo> filteredPosts = allRecentPosts.stream()
                    .filter(post -> post != null && post.getUserId() != null)
                    .filter(post -> attentionUserIds.contains(post.getUserId()))
                    .collect(Collectors.toList());
            
            // 填充用户状态信息（这里的用户都是已关注的，特殊处理）
            for (RedisCirclePostVo post : filteredPosts) {
                post.setIsLoggedIn(isLoggedIn);
                if (currentUserId.equals(post.getUserId())) {
                    post.setIsFollowed(2); // 本人发布的帖子
                } else {
                    post.setIsFollowed(1); // 已关注用户的帖子
                }
            }

            // 6. 按创建时间排序（最新的在前面）
            filteredPosts.sort((p1, p2) -> {
                if (p1.getCreatedTime() == null && p2.getCreatedTime() == null) {
                    return 0;
                }
                if (p1.getCreatedTime() == null) {
                    return 1;
                }
                if (p2.getCreatedTime() == null) {
                    return -1;
                }
                return p2.getCreatedTime().compareTo(p1.getCreatedTime());
            });

            // 7. 分页处理
            List<RedisCirclePostVo> paginatedPosts = PageHelper.getPagedList(filteredPosts, request.getPage(), request.getLimit());

            // 8. 构建分页结果
            PageObj<RedisCirclePostVo> result = new PageObj<>();
            result.setRecords(paginatedPosts);
            result.setTotal((long) filteredPosts.size());

            return result;

        } catch (Exception e) {
            log.error("查询关注用户最近帖子失败", e);
            PageObj<RedisCirclePostVo> emptyResult = new PageObj<>();
            emptyResult.setTotal(0L);
            emptyResult.setRecords(new ArrayList<>());
            return emptyResult;
        }
    }

    /**
     * 将SocialCirclePost实体转换为RedisCirclePostVo
     *
     * @param post 帖子实体
     * @return RedisCirclePostVo对象
     */
    private RedisCirclePostVo convertToRedisCirclePostVo(SocialCirclePost post) {
        if (post == null) {
            return null;
        }

        try {
            RedisCirclePostVo postVo = new RedisCirclePostVo();
            
            // 复制基本属性
            BeanUtils.copyProperties(post, postVo);
            
            // 设置帖子ID
            postVo.setCirclePostId(post.getCirclePostId());
            
            // 查询并设置帖子媒体信息
            List<SocialCirclePostMedia> mediaList = socialCirclePostMediaService.list(
                new LambdaQueryWrapper<SocialCirclePostMedia>()
                    .eq(SocialCirclePostMedia::getCirclePostId, post.getCirclePostId())
                    .orderByAsc(SocialCirclePostMedia::getSortNumber)
            );
            
            if (!CollectionUtils.isEmpty(mediaList)) {
                List<RedisCirclePostMediaVo> mediaVoList = mediaList.stream()
                    .map(media -> {
                        RedisCirclePostMediaVo mediaVo = new RedisCirclePostMediaVo();
                        BeanUtils.copyProperties(media, mediaVo);
                        return mediaVo;
                    })
                    .collect(Collectors.toList());
                postVo.setPostMediaList(mediaVoList);
            }

            // 查询用户信息
            String userDetailKey = PermissKey.USER_DETAIL + post.getUserId();
            UserDetail userDetail = redisCache.getCacheObject(userDetailKey);
            if (userDetail != null) {
                postVo.setUserNickName(userDetail.getUserInfo().getUserNickname());
                if (userDetail.getUserAvatar() != null) {
                    postVo.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
                }
            }

            // 设置默认的关注状态和登录状态
            postVo.setIsFollowed(0); // 默认未关注
            postVo.setIsLoggedIn(false); // 默认未登录

            return postVo;
            
        } catch (Exception e) {
            log.error("转换帖子实体为VO失败，帖子ID: {}, 错误信息: {}", post.getCirclePostId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将SocialCirclePost实体转换为RedisCirclePostVo（带用户关注状态）
     *
     * @param post 帖子实体
     * @param currentUserId 当前用户ID（用于判断关注状态）
     * @param isLoggedIn 是否登录
     * @return RedisCirclePostVo对象
     */
    private RedisCirclePostVo convertToRedisCirclePostVo(SocialCirclePost post, Long currentUserId, boolean isLoggedIn) {
        RedisCirclePostVo postVo = convertToRedisCirclePostVo(post);
        if (postVo == null) {
            return null;
        }

        try {
            // 设置登录状态
            postVo.setIsLoggedIn(isLoggedIn);

            // 设置关注状态
            if (currentUserId == null || !isLoggedIn) {
                postVo.setIsFollowed(0); // 未登录，显示未关注
            } else if (currentUserId.equals(post.getUserId())) {
                postVo.setIsFollowed(2); // 本人发布的帖子
            } else {
                // 调用远程服务查询关注状态
                boolean isFollowing = idolFansRelatClient.queryUserIsFollower(currentUserId, post.getUserId());
                postVo.setIsFollowed(isFollowing ? 1 : 0); // 1-已关注, 0-未关注
            }

            return postVo;
        } catch (Exception e) {
            log.error("设置帖子关注状态失败，帖子ID: {}, 当前用户ID: {}", post.getCirclePostId(), currentUserId, e);
            // 异常情况下设置默认值
            postVo.setIsFollowed(0);
            postVo.setIsLoggedIn(isLoggedIn);
            return postVo;
        }
    }

    /**
     * 填充帖子的用户状态信息（登录状态和关注状态）
     * 
     * @param posts 帖子列表
     * @param currentUserId 当前登录用户ID（未登录为null）
     * @param isLoggedIn 是否已登录
     */
    private void fillUserStatusForPosts(List<RedisCirclePostVo> posts, Long currentUserId, boolean isLoggedIn) {
        if (CollectionUtils.isEmpty(posts)) {
            return;
        }
        
        for (RedisCirclePostVo post : posts) {
            if (post == null) {
                continue;
            }
            
            // 设置登录状态
            post.setIsLoggedIn(isLoggedIn);
            
            // 设置关注状态
            if (!isLoggedIn) {
                post.setIsFollowed(0); // 未登录，显示未关注
            } else if (currentUserId != null && currentUserId.equals(post.getUserId())) {
                post.setIsFollowed(2); // 本人发布的帖子
            } else if (currentUserId != null) {
                // 调用远程服务查询关注状态
                try {
                    boolean isFollowing = idolFansRelatClient.queryUserIsFollower(currentUserId, post.getUserId());
                    post.setIsFollowed(isFollowing ? 1 : 0); // 1-已关注, 0-未关注
                } catch (Exception e) {
                    log.warn("查询关注状态失败，帖子用户ID: {}, 当前用户ID: {}", post.getUserId(), currentUserId);
                    post.setIsFollowed(0); // 异常情况下默认未关注
                }
            } else {
                post.setIsFollowed(0); // 默认未关注
            }
        }
    }

    /**
     * 从数据库查询最近帖子并同步到Redis
     * 通过一条SQL查询所有数据，包括媒体信息
     */
    private void syncRecentPostsToRedis() {

        // 1. 使用自定义SQL一次性查询所有帖子数据（包括媒体信息），限制10000条
        List<RedisCirclePostVo> posts = this.baseMapper.selectRecentPostsForRedis(10000);
        
        if (CollectionUtils.isEmpty(posts)) {
            return;
        }

        // 2. 循环处理：设置用户头像和帖子分类映射
        for (RedisCirclePostVo post : posts) {
            // 尝试从Redis获取用户头像（如果数据库中没有）
            if (ObjectUtil.isEmpty(post.getUserAvatar())) {
                UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + post.getUserId());
                if (userDetail != null && userDetail.getUserAvatar() != null) {
                    post.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
                    post.setUserNickName(userDetail.getUserInfo().getUserNickname());
                }
            }
            
            // 设置帖子分类映射到Redis
            if (post.getPostCategId() != null) {
                circlePostKeyManager.setPostCategory(post.getCirclePostId(), post.getPostCategId());
            }
        }
        
        // 3. 保存到Redis最近帖子列表
        circlePostKeyManager.setRecentPosts(posts);

    }
}
