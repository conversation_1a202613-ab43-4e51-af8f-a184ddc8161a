package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import zx.vanx.social_circle.entity.PlatfCircleFunction;
import zx.vanx.social_circle.mapper.PlatfCircleFunctionMapper;
import zx.vanx.social_circle.mapper.SocialCircleFuncRelatMapper;
import zx.vanx.social_circle.service.PlatfCircleFunctionService;
import zx.vanx.social_circle.vo.CircleFunctionMenuVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单功能表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
@RequiredArgsConstructor
public class PlatfCircleFunctionServiceImpl extends ServiceImpl<PlatfCircleFunctionMapper, PlatfCircleFunction> implements PlatfCircleFunctionService {

    private final SocialCircleFuncRelatMapper socialCircleFuncRelatMapper;

    /**
     * 查询指定功能菜单下的子菜单树
     * @param functionTitle 父级功能菜单标题
     * @return 子菜单功能树
     */
    @Override
    public List<CircleFunctionMenuVO> getSubMenuTree(String functionTitle) {
        // 查询指定功能菜单相关的菜单数据
        List<PlatfCircleFunction> functions = baseMapper.selectCircleDetailMenus(functionTitle);
        
        // 转换为VO对象
        List<CircleFunctionMenuVO> menuVOs = functions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        // 构建树形结构
        return buildMenuTree(menuVOs, functionTitle);
    }

    /**
     * 根据圈子ID查询圈主菜单树
     * @param socialCircleId 圈子ID
     * @return 圈主菜单树
     */
    @Override
    public List<CircleFunctionMenuVO> getOwnerMenuTree(Long socialCircleId) {
        // 1. 根据圈子ID和圈主角色查询菜单数据
        List<PlatfCircleFunction> ownerFunctions = socialCircleFuncRelatMapper.selectUserFunctionMenus(socialCircleId, "圈主");
        
        // 2. 转换为VO对象
        List<CircleFunctionMenuVO> menuVOs = ownerFunctions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        // 3. 构建完整的菜单树结构
        return buildCompleteMenuTree(menuVOs);
    }
    
    /**
     * 实体转VO
     */
    private CircleFunctionMenuVO convertToVO(PlatfCircleFunction function) {
        CircleFunctionMenuVO vo = new CircleFunctionMenuVO();
        
        // 基本信息
        vo.setFunctionId(function.getFunctionId());
        vo.setFunctionTitle(function.getFunctionTitle());
        vo.setParentFunctionId(function.getParentFunctionId());
        vo.setAssociateFunctionId(function.getAssociateFunctionId());
        vo.setSortNumber(function.getSortNumber());
        
        // 路由和组件信息
        vo.setPath(function.getPath());
        vo.setComponent(function.getComponent());
        vo.setFunctionType(function.getFunctionType());
        vo.setPerms(function.getPerms());
        
        // 显示相关信息
        vo.setIcon(function.getIcon());
        vo.setIconUrl(function.getIconUrl());
        vo.setPromptMessage(function.getPromptMessage());
        
        // 功能配置信息
        vo.setStorageSize(function.getStorageSize());
        vo.setIsCharged(function.getIsCharged());
        vo.setIsFrame(function.getIsFrame());
        vo.setIsVisible(function.getIsVisible());
        vo.setStatus(function.getStatus());
        vo.setIsDefault(function.getIsDefault() != null && function.getIsDefault() ? 1 : 0);
        
        // 分类和位置信息
        vo.setFunctionCateg(function.getFunctionCateg());
        vo.setFunctionLocation(function.getFunctionLocation());
        vo.setFunctionStatus(function.getFunctionStatus());
        vo.setIsLeaf(function.getIsLeaf());
        
        // 模块和操作信息
        vo.setModuleName(function.getModuleName());
        vo.setOperationName(function.getOperationName());
        
        // 请求相关信息
        vo.setRequestUrl(function.getRequestUrl());
        vo.setTargetType(function.getTargetType());
        
        return vo;
    }
    
    /**
     * 构建菜单树
     */
    private List<CircleFunctionMenuVO> buildMenuTree(List<CircleFunctionMenuVO> menuVOs, String targetFunctionTitle) {

        // 按父ID分组
        Map<Long, List<CircleFunctionMenuVO>> menuMap = menuVOs.stream()
                .collect(Collectors.groupingBy(
                        vo -> vo.getParentFunctionId() == null ? 0L : vo.getParentFunctionId()
                ));
        
        // 构建树结构
        for (CircleFunctionMenuVO vo : menuVOs) {
            List<CircleFunctionMenuVO> children = menuMap.get(vo.getFunctionId());
            vo.setChildren(children != null ? children : new ArrayList<>());
        }
        
        // 查找指定功能标题的节点，返回其子菜单列表
        for (CircleFunctionMenuVO vo : menuVOs) {
            if (targetFunctionTitle.equals(vo.getFunctionTitle())) {
                return vo.getChildren() != null ? vo.getChildren() : new ArrayList<>();
            }
        }
        
        // 如果没找到指定功能节点，返回空列表
        return new ArrayList<>();
    }

    /**
     * 构建完整的菜单树结构（返回根节点列表）
     */
    private List<CircleFunctionMenuVO> buildCompleteMenuTree(List<CircleFunctionMenuVO> menuVOs) {
        if (menuVOs == null || menuVOs.isEmpty()) {
            return new ArrayList<>();
        }

        // 按父ID分组
        Map<Long, List<CircleFunctionMenuVO>> menuMap = menuVOs.stream()
                .collect(Collectors.groupingBy(
                        vo -> vo.getParentFunctionId() == null ? 0L : vo.getParentFunctionId()
                ));
        
        // 构建树结构
        for (CircleFunctionMenuVO vo : menuVOs) {
            List<CircleFunctionMenuVO> children = menuMap.get(vo.getFunctionId());
            vo.setChildren(children != null ? children : new ArrayList<>());
        }
        
        // 找出所有菜单中没有父节点在当前查询结果中的菜单，这些就是根节点
        // 也就是parent_function_id指向的菜单不在当前查询结果中的菜单
        Set<Long> allFunctionIds = menuVOs.stream()
                .map(CircleFunctionMenuVO::getFunctionId)
                .collect(Collectors.toSet());
        
        List<CircleFunctionMenuVO> rootNodes = new ArrayList<>();
        for (CircleFunctionMenuVO vo : menuVOs) {
            Long parentId = vo.getParentFunctionId();
            // 如果父ID为null/0，或者父ID不在当前查询结果中，则认为是根节点
            if (parentId == null || parentId == 0L || !allFunctionIds.contains(parentId)) {
                rootNodes.add(vo);
            }
        }
        
        return rootNodes;
    }
}
