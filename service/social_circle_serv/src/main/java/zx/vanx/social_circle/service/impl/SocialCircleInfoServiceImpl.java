package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.common_util.utils.SensitiveWordsUtil;
import zx.vanx.social_circle.entity.*;
import zx.vanx.social_circle.mapper.*;
import zx.vanx.social_circle.request.SocialCircleAddressRequest;
import zx.vanx.social_circle.request.SocialCircleCreateRequest;
import zx.vanx.social_circle.request.SocialCircleMediaRequest;
import zx.vanx.social_circle.request.SocialCircleSearchRequest;
import zx.vanx.social_circle.service.PlatfPostCategService;
import zx.vanx.social_circle.service.SocialCircleFuncRelatService;
import zx.vanx.social_circle.service.SocialCircleInfoService;
import zx.vanx.social_circle.service.SocialCirclePostCategRelatService;
import zx.vanx.social_circle.vo.*;
import zx.vanx.user_permiss.client.UserCertificationClient;
import zx.vanx.user_permiss.vo.PersonalAuthInfoVo;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 社交圈子信息表 服务实现类
 * 企业级规范实现，支持完整的圈子创建业务流程
 * 包含圈子基本信息、地址信息、媒体信息、成员信息的综合管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class SocialCircleInfoServiceImpl extends ServiceImpl<SocialCircleInfoMapper, SocialCircleInfo> implements SocialCircleInfoService {

    /**
     * 圈子地址信息表 Mapper 接口
     */
    private final SocialCircleAddressMapper socialCircleAddressMapper;

    /**
     * 圈子媒体信息表 Mapper 接口
     */
    private final SocialCircleMediaMapper socialCircleMediaMapper;

    /**
     * 圈子成员表 Mapper 接口
     */
    private final SocialCircleMembersMapper socialCircleMembersMapper;

    /**
     * 圈子分类关联表 Mapper 接口
     */
    private final SocialCircleCategoryRelatMapper socialCircleCategoryRelatMapper;

    /**
     * 社交圈子信息表 Mapper 接口
     */
    private final SocialCircleInfoMapper socialCircleInfoMapper;

    /**
     * 用户认证服务客户端
     */
    private final UserCertificationClient userCertificationClient;

    /**
     * 圈子菜单权限功能表 服务接口
     */
    private final SocialCircleFuncRelatService socialCircleFuncRelatService;
    /**
     * 圈子菜单权限功能表 Mapper 接口
     */
    private final SocialCircleFuncRelatMapper socialCircleFuncRelatMapper;

    /**
     * 菜单功能表 Mapper 接口
     */
    private final PlatfCircleFunctionMapper platfCircleFunctionMapper;

    /**
     * 圈子帖子分类关联 服务接口
     */
    private final SocialCirclePostCategRelatService socialCirclePostCategRelatService;

    /**
     * 平台帖子分类 服务接口
     */
    private final PlatfPostCategService platfPostCategService;

    /**
     * 圈子帖子表 Mapper 接口
     */
    private final SocialCirclePostMapper socialCirclePostMapper;

    /**
     * 根据用户ID查询该用户创建的圈子信息
     *
     * @param userId 用户ID
     * @return 圈子信息，如果没有则返回null
     */
    @Override
    public SocialCircleInfo getCircleByUserId(Long userId) {

        // 参数校验：如果用户ID为null，直接返回null
        if (userId == null) {
            return null;
        }
        
        // 使用LambdaQueryWrapper构建类型安全的查询条件
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleInfo::getUserId, userId);
        
        return this.getOne(queryWrapper);
    }

    /**
     * 查询当前用户圈子的完整详情信息
     * 包含圈子基本信息、地址信息、媒体信息、成员信息等全部相关数据
     * 采用模块化设计，分步查询后组装成完整的详情对象
     *
     * @param userId 用户ID
     * @return 圈子详情信息，如果用户没有创建圈子则返回null
     */
    @Override
    public SocialCircleDetailVO getCurrentUserCircleDetail(Long userId) {
        
        // 1. 基础参数验证
        if (userId == null) {
            return null;
        }
        
        // 2. 查询圈子基本信息
        SocialCircleInfo circleInfo = queryCircleBasicInfo(userId);
        if (circleInfo == null) {
            return null;  // 用户尚未创建圈子
        }
        
        // 3. 查询圈子地址信息（可选）
        SocialCircleAddress addressInfo = queryCircleAddress(circleInfo.getSocialCircleId());
        
        // 4. 查询圈子媒体信息列表
        List<SocialCircleMedia> mediaList = queryCircleMediaList(circleInfo.getSocialCircleId());
        
        // 5. 查询圈子成员信息列表
        List<SocialCircleMembers> memberList = queryCircleMemberList(circleInfo.getSocialCircleId());
        
        // 6. 组装详情VO对象并返回
        return buildCircleDetailVO(circleInfo, addressInfo, mediaList, memberList);
    }

    /**
     * 根据圈子ID查询圈子完整详情信息
     * 需要验证当前用户是否有权限查看该圈子
     * 包含圈子基本信息、地址信息、媒体信息、成员信息等全部相关数据
     *
     * @param circleId 圈子ID
     * @param userId   当前用户ID
     * @return 圈子详情信息，如果圈子不存在或用户无权限则返回null
     */
    @Override
    public SocialCircleDetailVO getCircleDetailById(Long circleId, Long userId) {
        
        // 1. 基础参数验证
        if (circleId == null || userId == null) {
            return null;
        }
        
        // 2. 验证用户是否有权限查看该圈子（用户必须是该圈子的成员）
        boolean hasPermission = checkUserCirclePermission(circleId, userId);
        if (!hasPermission) {
            return null;  // 用户无权限查看该圈子
        }
        
        // 3. 查询圈子基本信息
        SocialCircleInfo circleInfo = queryCircleBasicInfoById(circleId);
        if (circleInfo == null) {
            return null;  // 圈子不存在
        }
        
        // 4. 查询圈子地址信息（可选）
        SocialCircleAddress addressInfo = queryCircleAddress(circleId);
        
        // 5. 查询圈子媒体信息列表
        List<SocialCircleMedia> mediaList = queryCircleMediaList(circleId);
        
        // 6. 查询圈子成员信息列表
        List<SocialCircleMembers> memberList = queryCircleMemberList(circleId);
        
        // 7. 组装详情VO对象并返回
        return buildCircleDetailVO(circleInfo, addressInfo, mediaList, memberList);
    }

    /**
     * 查询用户的默认圈子信息
     * 通过用户在圈子成员表中设置的默认圈子标识查询
     * 返回圈子基本信息、成员数、活跃数等统计数据
     *
     * @param userId 用户ID
     * @return 默认圈子信息，如果用户没有设置默认圈子则返回null
     */
    @Override
    public DefaultCircleVO getMyDefaultCircle(Long userId) {
        
        // 1. 基础参数验证
        if (userId == null) {
            return null;
        }
        
        // 2. 查询用户的默认圈子成员记录
        SocialCircleMembers defaultMember = queryDefaultCircleMember(userId);
        if (defaultMember == null) {
            return null;  // 用户没有设置默认圈子
        }
        
        // 3. 根据圈子ID查询圈子基本信息
        SocialCircleInfo circleInfo = queryCircleBasicInfoById(defaultMember.getSocialCircleId());
        if (circleInfo == null) {
            return null;  // 默认圈子不存在或已被删除
        }
        
        // 4. 统计圈子成员总数
        Integer memberCount = countCircleMembers(defaultMember.getSocialCircleId());

        // 5. 统计圈子帖子总数
        Integer totalPostCount = countCirclePosts(defaultMember.getSocialCircleId());

        // 6. TODO: 后期通过用户行为数据统计填充日活跃数
        Integer dailyActiveCount = 0;  // 暂时设置为0，后续可通过行为数据统计填充

        // 7. 查询普通用户在该圈子的菜单列表
        List<CircleFunctionMenuVO> userMenuList = queryUserFunctionMenus(
            defaultMember.getSocialCircleId(),
            "普通成员"
        );

        // 8. 查询圈子支持的帖子分类列表
        List<PlatfPostCategVO> postCategList = queryCirclePostCategories(defaultMember.getSocialCircleId());

        // 9. 组装默认圈子VO对象并返回
        return buildDefaultCircleVO(circleInfo, defaultMember, memberCount, dailyActiveCount, totalPostCount, userMenuList, postCategList);
    }

    /**
     * 圈子创建主方法
     * 支持圈子基本信息、地址信息、媒体信息、成员信息的完整创建
     * 包含个人实名认证检查
     * 采用事务控制确保数据一致性
     *
     * @param request 圈子创建请求参数（包含基本信息、地址信息、媒体信息）
     * @param userId  创建者用户ID
     * @return 圈子创建结果（包含认证状态和创建信息）
     * @throws ZxException 业务异常（参数验证失败、敏感词检测失败、数据库操作失败等）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCircleCreateResultVO createSocialCircle(SocialCircleCreateRequest request, Long userId) {
        
        // 1. 基础参数验证
        validateCreateRequest(request, userId);
        
        // 2. 检查用户个人实名认证状态
        SocialCircleCreateResultVO authResult = checkUserPersonalAuth(userId);
        if (!authResult.getIsPersonRealNameAuth()) {
            // 未认证，直接返回认证失败结果
            return authResult;
        }
        
        // 3. 业务规则验证
        validateBusinessRules(request, userId);
        
        // 4. 敏感词检测
        validateSensitiveWords(request);
        
        // 5. 创建圈子基本信息
        SocialCircleInfo socialCircleInfo = createCircleInfo(request, userId);
        
        // 6. 创建圈子地址信息（可选）
        if (request.getAddressInfo() != null) {
            createCircleAddress(socialCircleInfo.getSocialCircleId(), request.getAddressInfo(), userId);
        }
        
        // 7. 批量创建圈子媒体信息（可选）
        if (!CollectionUtils.isEmpty(request.getMediaList())) {
            createCircleMedias(socialCircleInfo.getSocialCircleId(), request.getMediaList(), userId);
        }

        // 8. 关联圈子分类
        if (!CollectionUtils.isEmpty(request.getCategoryIds())) {
            createCircleCategories(socialCircleInfo.getSocialCircleId(), request.getCategoryIds());
        }
        
        // 9. 创建圈主成员信息
        SocialCircleMembers circleOwnerMember = createCircleOwnerMember(socialCircleInfo.getSocialCircleId(), userId);

        // 10. 创建圈主菜单权限关联
        socialCircleFuncRelatService.createOwnerFunctionRelations(
            socialCircleInfo.getSocialCircleId(), 
            request.getSelectedFunctionIds()
        );

        // 封装普通成员需要关联的菜单id列表
        List<Long> memberFubctionList = getMemberFunctionList(request.getSelectedFunctionIds());

        // 11. 创建普通成员菜单权限关联
        socialCircleFuncRelatService.createMemberFunctionRelations(
                socialCircleInfo.getSocialCircleId(),
                memberFubctionList
        );

        // 12. 创建圈子帖子分类关联（可选）
        if (!CollectionUtils.isEmpty(request.getPostCategIds())) {
            createCirclePostCategRelations(socialCircleInfo.getSocialCircleId(), request.getPostCategIds());
        }
        
        // 13. 返回创建成功结果
        return SocialCircleCreateResultVO.success(socialCircleInfo);
    }

    /**
     * 创建圈子分类关联
     * 目前仅支持单个圈子关联多个分类
     * 注意：实际业务中可能需要根据具体需求调整实现方式
     *
     * @param socialCircleId 圈子ID
     * @param categoryIds    圈子分类ID列表
     * @throws ZxException 业务异常（参数验证失败、数据库操作失败等）
     */
    private void createCircleCategories(Long socialCircleId, @NotEmpty @Size(min = 1, message = "至少选择一个圈子分类") List<Long> categoryIds) {

        // 参数验证：圈子ID不能为空，分类ID列表不能为空且至少包含一个分类
        if (socialCircleId == null || CollectionUtils.isEmpty(categoryIds)) {
            throw new ZxException(400, "圈子ID和分类ID列表不能为空");
        }

        // 批量插入圈子分类关联记录
        List<SocialCircleCategoryRelat> socialCircleCategoryRelats = categoryIds.stream()
                .map(categoryId -> {
                    SocialCircleCategoryRelat socialCircleCategoryRelat = new SocialCircleCategoryRelat();
                    socialCircleCategoryRelat.setSocialCircleId(socialCircleId);
                    socialCircleCategoryRelat.setSocialCircleCategId(categoryId);
                    return socialCircleCategoryRelat;
                })
                .collect(Collectors.toList());

        // 批量插入分类关联记录
        int totalInserted = 0;
        for (SocialCircleCategoryRelat relat : socialCircleCategoryRelats) {
            int insertResult = socialCircleCategoryRelatMapper.insert(relat);
            if (insertResult > 0) {
                totalInserted++;
            }
        }
        boolean saveResult = totalInserted == socialCircleCategoryRelats.size();
        if (!saveResult) {
            throw new ZxException(500, "圈子分类关联创建失败，请稍后重试");
        }
    }

    /**
     * 查询当前用户的社交圈子列表
     * 包含用户创建的圈子（圈主）和加入的圈子（管理员、普通成员）
     * 圈主创建的圈子排在前面，加入的圈子排在后面
     *
     * @param userId 用户ID
     * @return 社交圈子列表，按角色优先级和加入时间排序
     */
    @Override
    public List<SocialCircleInfoVO> getMtSocialCircleList(Long userId) {

        // 1. 基础参数验证
        if (userId == null) {
            return Collections.emptyList();
        }

        // 2. 查询用户的所有圈子成员记录
        List<SocialCircleMembers> memberList = queryUserCircleMemberList(userId);
        if (CollectionUtils.isEmpty(memberList)) {
            return Collections.emptyList();  // 用户没有加入任何圈子
        }

        // 3. 提取圈子ID列表
        List<Long> circleIds = memberList.stream()
                .map(SocialCircleMembers::getSocialCircleId)
                .distinct()
                .collect(Collectors.toList());

        // 4. 批量查询圈子基本信息
        List<SocialCircleInfo> circleInfoList = queryCircleInfoByIds(circleIds);
        if (CollectionUtils.isEmpty(circleInfoList)) {
            return Collections.emptyList();  // 相关圈子不存在或已被删除
        }

        // 5. 组装VO列表并排序
        return buildSocialCircleInfoVOList(memberList, circleInfoList);
    }



    // ==================== 私有方法 - 业务逻辑拆分 ====================

    /**
     * 验证创建请求的基础参数
     *
     * @param request 创建请求
     * @param userId  用户ID
     * @throws ZxException 参数验证失败时抛出异常
     */
    private void validateCreateRequest(SocialCircleCreateRequest request, Long userId) {
        if (request == null) {
            throw new ZxException(400, "圈子创建请求参数不能为空");
        }
        
        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }
    }

    /**
     * 验证业务规则
     * 检查用户的圈子名称是否重复等业务约束
     *
     * @param request 创建请求
     * @param userId  用户ID
     * @throws ZxException 业务规则验证失败时抛出异常
     */
    private void validateBusinessRules(SocialCircleCreateRequest request, Long userId) {

        // 检查用户是否已创建过同名圈子（同一用户的圈子名称不能重复）
        boolean hasExistingCircleWithSameName = checkCircleNameExists(userId, request.getSocialCircleName());
        if (hasExistingCircleWithSameName) {
            throw new ZxException(400, "您已经创建过名称为「" + request.getSocialCircleName() + "」的圈子，请使用不同的圈子名称");
        }

    }

    /**
     * 敏感词检测
     * 对圈子名称、描述、广告语进行敏感词过滤
     *
     * @param request 创建请求
     * @throws ZxException 敏感词检测失败时抛出异常
     */
    private void validateSensitiveWords(SocialCircleCreateRequest request) {
        // 验证圈子名称是否包含敏感词
        if (StringUtils.hasText(request.getSocialCircleName())) {
            if (SensitiveWordsUtil.containsSensitiveWords(request.getSocialCircleName())) {
                throw new ZxException(400, "圈子名称包含敏感词，请修改后重试");
            }
        }

        // 验证圈子描述是否包含敏感词
        if (StringUtils.hasText(request.getSocialCircleDescription())) {
            if (SensitiveWordsUtil.containsSensitiveWords(request.getSocialCircleDescription())) {
                throw new ZxException(400, "圈子描述包含敏感词，请修改后重试");
            }
        }

        // 验证圈子广告语是否包含敏感词
        if (StringUtils.hasText(request.getSocialCircleAdvertise())) {
            if (SensitiveWordsUtil.containsSensitiveWords(request.getSocialCircleAdvertise())) {
                throw new ZxException(400, "圈子广告语包含敏感词，请修改后重试");
            }
        }
    }

    /**
     * 创建圈子基本信息
     * 使用BeanUtil进行对象拷贝，设置默认状态和系统字段
     *
     * @param request 创建请求
     * @param userId  创建者用户ID
     * @return 创建成功的圈子信息
     * @throws ZxException 数据库操作失败时抛出异常
     */
    private SocialCircleInfo createCircleInfo(SocialCircleCreateRequest request, Long userId) {
        // 使用BeanUtil进行对象拷贝，避免逐个属性设置
        SocialCircleInfo socialCircleInfo = BeanUtil.copyProperties(request, SocialCircleInfo::new);
        
        // 设置创建者相关信息
        socialCircleInfo.setUserId(userId);
        socialCircleInfo.setCreatorId(userId);
        socialCircleInfo.setEditorId(userId);

        // 生成socialCircleCode号
        String socialCircleCode = "circle_code_" + RandomStringUtils.randomNumeric(9);
        socialCircleInfo.setSocialCircleCode(socialCircleCode);
        
        // 设置默认状态 - 圈子需要审核
        socialCircleInfo.setSocialCircleStatus("申请");  // 默认为申请状态，需要平台审核
        socialCircleInfo.setSocialCircleSource("个人申请");  // 来源为个人申请
        
        // 设置系统字段
        LocalDateTime now = LocalDateTime.now();
        socialCircleInfo.setCreatedTime(now);
        socialCircleInfo.setModifiedTime(now);

        // 保存到数据库
        boolean saveResult = this.save(socialCircleInfo);
        if (!saveResult) {
            throw new ZxException(500, "圈子基本信息创建失败，请稍后重试");
        }

        return socialCircleInfo;
    }

    /**
     * 创建圈子地址信息
     * 将地址请求DTO转换为地址实体并保存
     *
     * @param circleId       圈子ID
     * @param addressRequest 地址信息请求
     * @param userId         创建者用户ID
     * @throws ZxException 数据库操作失败时抛出异常
     */
    private void createCircleAddress(Long circleId, SocialCircleAddressRequest addressRequest, Long userId) {
        // 使用BeanUtil进行对象拷贝
        SocialCircleAddress address = BeanUtil.copyProperties(addressRequest, SocialCircleAddress::new);
        
        // 设置关联信息和系统字段
        address.setSocialCircleId(circleId);
        address.setCreatorId(userId);
        address.setEditorId(userId);
        address.setInfoAddressStatus(true);  // 地址有效
        
        // 设置系统字段
        LocalDateTime now = LocalDateTime.now();
        address.setCreatedTime(now);
        address.setModifiedTime(now);

        // 保存地址信息
        int saveResult = socialCircleAddressMapper.insert(address);
        if (saveResult <= 0) {
            throw new ZxException(500, "圈子地址信息创建失败，请稍后重试");
        }
    }

    /**
     * 批量创建圈子媒体信息
     * 支持多张图片/视频的批量创建
     *
     * @param circleId     圈子ID
     * @param mediaList    媒体信息请求列表
     * @param userId       创建者用户ID
     * @throws ZxException 数据库操作失败时抛出异常
     */
    private void createCircleMedias(Long circleId, List<SocialCircleMediaRequest> mediaList, Long userId) {
        for (SocialCircleMediaRequest mediaRequest : mediaList) {
            // 使用BeanUtil进行对象拷贝
            SocialCircleMedia media = BeanUtil.copyProperties(mediaRequest, SocialCircleMedia::new);
            
            // 设置关联信息和系统字段
            media.setSocialCircleId(circleId);
            media.setCreatorId(userId);
            media.setEditorId(userId);
            media.setPictureStatus("待审核");  // 媒体需要审核
            
            // 设置系统字段
            LocalDateTime now = LocalDateTime.now();
            media.setCreatedTime(now);
            media.setModifiedTime(now);

            // 保存媒体信息
            int saveResult = socialCircleMediaMapper.insert(media);
            if (saveResult <= 0) {
                throw new ZxException(500, "圈子媒体信息创建失败，请稍后重试");
            }
        }
    }

    /**
     * 创建圈主成员信息
     * 将圈子创建者自动添加为圈主成员
     * 注意：isDefault字段在创建时不设置，需要等待圈子审批通过后才会设置默认状态
     *
     * @param circleId 圈子ID
     * @param userId   创建者用户ID
     * @throws ZxException 数据库操作失败时抛出异常
     */
    private SocialCircleMembers createCircleOwnerMember(Long circleId, Long userId) {
        SocialCircleMembers ownerMember = new SocialCircleMembers();
        
        // 设置成员基本信息
        ownerMember.setSocialCircleId(circleId);
        ownerMember.setUserId(userId);
        ownerMember.setUserRole("圈主");  // 设置为圈主角色
        ownerMember.setUserStatus("申请");  // 状态为已入圈
        ownerMember.setUserCircleRelatAddSource("主动加入");  // 来源为主动加入
        ownerMember.setReasonForAdd("圈子创建中，等待审核");  // 申请理由
        
        // 默认圈子字段在创建时设置为false，等待审批通过后才会设置为默认
        ownerMember.setIsDefault(false);  // 初始状态不设置为默认，等待审批流程处理
        
        // 设置系统字段
        ownerMember.setCreatorId(userId);
        ownerMember.setEditorId(userId);
        LocalDateTime now = LocalDateTime.now();
        ownerMember.setCreatedTime(now);
        ownerMember.setModifiedTime(now);

        // 保存圈主成员信息
        int saveResult = socialCircleMembersMapper.insert(ownerMember);
        if (saveResult <= 0) {
            throw new ZxException(500, "圈主成员信息创建失败，请稍后重试");
        }
        
        return ownerMember;
    }

    // ==================== 私有方法 - 查询详情业务逻辑拆分 ====================

    /**
     * 查询圈子基本信息
     * 根据用户ID查询该用户创建的圈子基本信息
     *
     * @param userId 用户ID
     * @return 圈子基本信息，如果没有则返回null
     */
    private SocialCircleInfo queryCircleBasicInfo(Long userId) {
        // 使用LambdaQueryWrapper构建类型安全的查询条件
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleInfo::getUserId, userId);
        queryWrapper.eq(SocialCircleInfo::getIsDeleted, 0);  // 只查询未删除的圈子
        
        return this.getOne(queryWrapper);
    }

    /**
     * 查询圈子地址信息
     * 根据圈子ID查询对应的地址信息
     *
     * @param circleId 圈子ID
     * @return 地址信息，如果没有地址信息则返回null
     */
    private SocialCircleAddress queryCircleAddress(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleAddress::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleAddress::getIsDeleted, 0);  // 只查询未删除的地址
        queryWrapper.eq(SocialCircleAddress::getInfoAddressStatus, true);  // 只查询有效的地址
        
        return socialCircleAddressMapper.selectOne(queryWrapper);
    }

    /**
     * 查询圈子媒体信息列表
     * 根据圈子ID查询所有相关的媒体信息，按排序指数升序排列
     *
     * @param circleId 圈子ID
     * @return 媒体信息列表，如果没有媒体则返回空列表
     */
    private List<SocialCircleMedia> queryCircleMediaList(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMedia> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMedia::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMedia::getIsDeleted, 0);  // 只查询未删除的媒体
        queryWrapper.ne(SocialCircleMedia::getPictureStatus, "驳回");  // 排除已驳回的媒体
        queryWrapper.orderByAsc(SocialCircleMedia::getSortNumber);  // 按排序指数升序
        queryWrapper.orderByDesc(SocialCircleMedia::getCreatedTime);  // 时间倒序作为第二排序条件
        
        return socialCircleMediaMapper.selectList(queryWrapper);
    }

    /**
     * 查询圈子成员信息列表
     * 根据圈子ID查询所有成员信息，优先显示圈主和管理员
     *
     * @param circleId 圈子ID
     * @return 成员信息列表，如果没有成员则返回空列表
     */
    private List<SocialCircleMembers> queryCircleMemberList(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMembers::getIsDeleted, 0);  // 只查询未删除的成员
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");  // 只查询已入圈的成员
        queryWrapper.orderByAsc(SocialCircleMembers::getCreatedTime);  // 按加入时间升序
        
        // 查询所有成员后进行Java层面的排序
        List<SocialCircleMembers> memberList = socialCircleMembersMapper.selectList(queryWrapper);
        
        // 按角色排序：圈主 > 管理员 > 普通成员
        memberList.sort((member1, member2) -> {
            int priority1 = getRolePriority(member1.getUserRole());
            int priority2 = getRolePriority(member2.getUserRole());
            
            // 优先级高的排在前面（降序）
            if (priority1 != priority2) {
                return Integer.compare(priority2, priority1);
            }
            
            // 优先级相同时按加入时间排序（升序）
            return member1.getCreatedTime().compareTo(member2.getCreatedTime());
        });
        
        return memberList;
    }
    
    /**
     * 获取用户角色的优先级
     * 用于成员列表排序
     *
     * @param userRole 用户角色
     * @return 优先级数值，数值越大优先级越高
     */
    private int getRolePriority(String userRole) {
        if ("圈主".equals(userRole)) {
            return 3;
        } else if ("管理员".equals(userRole)) {
            return 2;
        } else if ("普通成员".equals(userRole)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 组装圈子详情VO对象
     * 将查询到的各项信息组装成完整的详情对象，并计算统计信息
     *
     * @param circleInfo  圈子基本信息
     * @param addressInfo 地址信息（可能为null）
     * @param mediaList   媒体信息列表
     * @param memberList  成员信息列表
     * @return 完整的圈子详情VO对象
     */
    private SocialCircleDetailVO buildCircleDetailVO(SocialCircleInfo circleInfo, 
                                                   SocialCircleAddress addressInfo, 
                                                   List<SocialCircleMedia> mediaList, 
                                                   List<SocialCircleMembers> memberList) {
        
        // 创建详情VO对象
        SocialCircleDetailVO detailVO = new SocialCircleDetailVO();
        
        // 设置基本信息
        detailVO.setCircleInfo(circleInfo);
        detailVO.setAddressInfo(addressInfo);
        detailVO.setMediaList(mediaList);
        detailVO.setMemberList(memberList);
        
        // 计算统计信息
        detailVO.setTotalMemberCount(CollectionUtils.isEmpty(memberList) ? 0 : memberList.size());
        detailVO.setTotalMediaCount(CollectionUtils.isEmpty(mediaList) ? 0 : mediaList.size());
        detailVO.setHasAddressInfo(addressInfo != null);
        
        return detailVO;
    }

    // ==================== 私有方法 - 默认圈子查询业务逻辑拆分 ====================

    /**
     * 查询用户的默认圈子成员记录
     * 根据用户ID和默认标识查询用户设置为默认的圈子成员记录
     *
     * @param userId 用户ID
     * @return 默认圈子成员记录，如果没有设置默认圈子则返回null
     */
    private SocialCircleMembers queryDefaultCircleMember(Long userId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getUserId, userId);
        queryWrapper.eq(SocialCircleMembers::getIsDefault, true);  // 查询默认圈子
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");  // 只查询已入圈状态的记录
        
        return socialCircleMembersMapper.selectOne(queryWrapper);
    }

    /**
     * 根据圈子ID查询圈子基本信息
     * 用于查询默认圈子的基本信息
     *
     * @param circleId 圈子ID
     * @return 圈子基本信息，如果圈子不存在则返回null
     */
    private SocialCircleInfo queryCircleBasicInfoById(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleInfo::getSocialCircleId, circleId);
        queryWrapper.ne(SocialCircleInfo::getSocialCircleStatus, "注销");  // 排除已注销的圈子
        
        return this.getOne(queryWrapper);
    }

    /**
     * 统计圈子成员总数
     * 统计指定圈子所有已入圈状态的成员数量
     *
     * @param circleId 圈子ID
     * @return 成员总数
     */
    private Integer countCircleMembers(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMembers::getIsDeleted, 0);  // 只统计未删除的成员
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");  // 只统计已入圈的成员
        
        return Math.toIntExact(socialCircleMembersMapper.selectCount(queryWrapper));
    }

    /**
     * 统计圈子帖子总数
     * 统计指定圈子所有有效状态的帖子数量
     *
     * @param circleId 圈子ID
     * @return 帖子总数
     */
    private Integer countCirclePosts(Long circleId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCirclePost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePost::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCirclePost::getIsDeleted, 0);  // 只统计未删除的帖子
        queryWrapper.ne(SocialCirclePost::getPostStatus, "删除");  // 排除已删除状态的帖子
        
        return Math.toIntExact(socialCirclePostMapper.selectCount(queryWrapper));
    }

    /**
     * 组装默认圈子VO对象
     * 将查询到的圈子信息、成员信息、统计数据组装成完整的默认圈子VO对象
     *
     * @param circleInfo    圈子基本信息
     * @param defaultMember 用户的默认圈子成员记录
     * @param memberCount   圈子成员总数
     * @param dailyActiveCount   圈子日活跃数
     * @param totalPostCount   圈子帖子总数
     * @param userMenuList  用户菜单列表
     * @param postCategList 圈子支持的帖子分类列表
     * @return 完整的默认圈子VO对象
     */
    private DefaultCircleVO buildDefaultCircleVO(SocialCircleInfo circleInfo,
                                               SocialCircleMembers defaultMember,
                                               Integer memberCount,
                                               Integer dailyActiveCount,
                                               Integer totalPostCount,
                                               List<CircleFunctionMenuVO> userMenuList,
                                               List<PlatfPostCategVO> postCategList) {
        
        // 创建默认圈子VO对象
        DefaultCircleVO defaultCircleVO = new DefaultCircleVO();
        
        // 设置圈子基本信息
        defaultCircleVO.setCircleInfo(circleInfo);
        
        // 设置统计信息
        defaultCircleVO.setTotalMemberCount(memberCount);
        defaultCircleVO.setDailyActiveCount(dailyActiveCount);
        defaultCircleVO.setTotalPostCount(totalPostCount);
        defaultCircleVO.setHasDefaultCircle(true);
        
        // 设置用户在该圈子的扩展信息
        defaultCircleVO.setUserRole(defaultMember.getUserRole());
        if (defaultMember.getCreatedTime() != null) {
            // 直接设置Date对象，Jackson会自动按照注解格式化
            defaultCircleVO.setJoinTime(Timestamp.valueOf(defaultMember.getCreatedTime()));
        }
        
        // 设置用户菜单列表
        defaultCircleVO.setUserMenuList(userMenuList);

        // 设置圈子支持的帖子分类列表
        defaultCircleVO.setPostCategList(postCategList);

        return defaultCircleVO;
    }

    /**
     * 查询用户在指定圈子中的功能菜单列表
     * @param socialCircleId 圈子ID
     * @param userRole 用户角色
     * @return 用户菜单列表
     */
    private List<CircleFunctionMenuVO> queryUserFunctionMenus(Long socialCircleId, String userRole) {

        try {
            // 查询用户的功能菜单数据
            List<PlatfCircleFunction> functions = socialCircleFuncRelatMapper.selectUserFunctionMenus(
                socialCircleId, userRole
            );
            
            // 转换为VO对象
            return functions.stream()
                    .map(this::convertToCircleFunctionMenuVO)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            // 查询失败时返回空列表，不影响主流程
            return new ArrayList<>();
        }
    }

    /**
     * 将PlatfCircleFunction实体转换为CircleFunctionMenuVO对象
     * @param function 功能菜单实体
     * @return 菜单VO对象
     */
    private CircleFunctionMenuVO convertToCircleFunctionMenuVO(PlatfCircleFunction function) {
        CircleFunctionMenuVO vo = new CircleFunctionMenuVO();
        
        // 基本信息
        vo.setFunctionId(function.getFunctionId());
        vo.setFunctionTitle(function.getFunctionTitle());
        vo.setParentFunctionId(function.getParentFunctionId());
        vo.setAssociateFunctionId(function.getAssociateFunctionId());
        vo.setSortNumber(function.getSortNumber());
        
        // 路由和组件信息
        vo.setPath(function.getPath());
        vo.setComponent(function.getComponent());
        vo.setFunctionType(function.getFunctionType());
        vo.setPerms(function.getPerms());
        
        // 显示相关信息
        vo.setIcon(function.getIcon());
        vo.setIconUrl(function.getIconUrl());
        vo.setPromptMessage(function.getPromptMessage());
        
        // 功能配置信息
        vo.setStorageSize(function.getStorageSize());
        vo.setIsCharged(function.getIsCharged());
        vo.setIsFrame(function.getIsFrame());
        vo.setIsVisible(function.getIsVisible());
        vo.setStatus(function.getStatus());
        vo.setIsDefault(function.getIsDefault() != null && function.getIsDefault() ? 1 : 0);
        
        // 分类和位置信息
        vo.setFunctionCateg(function.getFunctionCateg());
        vo.setFunctionLocation(function.getFunctionLocation());
        vo.setFunctionStatus(function.getFunctionStatus());
        vo.setIsLeaf(function.getIsLeaf());
        
        // 模块和操作信息
        vo.setModuleName(function.getModuleName());
        vo.setOperationName(function.getOperationName());
        
        // 请求相关信息
        vo.setRequestUrl(function.getRequestUrl());
        vo.setTargetType(function.getTargetType());
        
        return vo;
    }

    // ==================== 私有方法 - 圈子列表查询业务逻辑拆分 ====================

    /**
     * 查询用户的所有圈子成员记录
     * 包含用户作为圈主、管理员、普通成员的所有圈子
     *
     * @param userId 用户ID
     * @return 用户的圈子成员记录列表
     */
    private List<SocialCircleMembers> queryUserCircleMemberList(Long userId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getUserId, userId);
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");  // 只查询已入圈状态的记录
        queryWrapper.orderByAsc(SocialCircleMembers::getCreatedTime);  // 按加入时间升序

        return socialCircleMembersMapper.selectList(queryWrapper);
    }

    /**
     * 批量查询圈子基本信息
     * 根据圈子ID列表批量查询有效的圈子信息
     *
     * @param circleIds 圈子ID列表
     * @return 圈子基本信息列表
     */
    private List<SocialCircleInfo> queryCircleInfoByIds(List<Long> circleIds) {
        if (CollectionUtils.isEmpty(circleIds)) {
            return Collections.emptyList();
        }

        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SocialCircleInfo::getSocialCircleId, circleIds);
        queryWrapper.eq(SocialCircleInfo::getIsDeleted, 0);  // 只查询未删除的圈子
        queryWrapper.ne(SocialCircleInfo::getSocialCircleStatus, "注销");  // 排除已注销的圈子

        return this.list(queryWrapper);
    }

    /**
     * 组装圈子信息VO列表
     * 将成员记录和圈子信息组装成完整的VO列表，并按角色优先级排序
     *
     * @param memberList     用户的圈子成员记录列表
     * @param circleInfoList 圈子基本信息列表
     * @return 排序后的圈子信息VO列表
     */
    private List<SocialCircleInfoVO> buildSocialCircleInfoVOList(List<SocialCircleMembers> memberList,
                                                               List<SocialCircleInfo> circleInfoList) {
        
        // 将圈子信息转换为Map，便于快速查找
        Map<Long, SocialCircleInfo> circleInfoMap = circleInfoList.stream()
                .collect(Collectors.toMap(
                        SocialCircleInfo::getSocialCircleId,
                        circleInfo -> circleInfo
                ));

        // 组装VO列表
        List<SocialCircleInfoVO> voList = new ArrayList<>();
        for (SocialCircleMembers member : memberList) {
            SocialCircleInfo circleInfo = circleInfoMap.get(member.getSocialCircleId());
            if (circleInfo != null) {
                SocialCircleInfoVO vo = buildSingleCircleInfoVO(member, circleInfo);
                voList.add(vo);
            }
        }

        // 按角色优先级和加入时间排序
        sortCircleInfoVOList(voList);

        return voList;
    }

    /**
     * 组装单个圈子信息VO
     * 将成员记录和圈子信息组装成单个VO对象
     *
     * @param member     圈子成员记录
     * @param circleInfo 圈子基本信息
     * @return 圈子信息VO
     */
    private SocialCircleInfoVO buildSingleCircleInfoVO(SocialCircleMembers member, SocialCircleInfo circleInfo) {

        // 使用BeanUtil进行对象拷贝
        SocialCircleInfoVO vo = BeanUtil.copyProperties(circleInfo, SocialCircleInfoVO::new);

        // 设置用户在圈子中的扩展信息
        vo.setIsDefault(member.getIsDefault());
        vo.setUserRole(member.getUserRole());
        vo.setUserStatus(member.getUserStatus());
        if (member.getCreatedTime() != null) {
            // 直接设置Date对象，Jackson会自动按照注解格式化
            vo.setJoinTime(java.sql.Timestamp.valueOf(member.getCreatedTime()));
        }

        // 设置角色优先级（用于排序）
        vo.setRolePriority(getRolePriority(member.getUserRole()));

        // 统计该圈子的成员总数
        Integer memberCount = countCircleMembers(circleInfo.getSocialCircleId());
        vo.setMemberCount(memberCount);

        return vo;

    }

    /**
     * 对圈子信息VO列表进行排序
     * 按角色优先级降序排序（圈主 > 管理员 > 普通成员），相同角色按加入时间倒序排序（最新的在前面）
     *
     * @param voList 圈子信息VO列表
     */
    private void sortCircleInfoVOList(List<SocialCircleInfoVO> voList) {
        voList.sort((vo1, vo2) -> {
            // 优先级高的排在前面（降序）
            int priorityCompare = Integer.compare(vo2.getRolePriority(), vo1.getRolePriority());
            if (priorityCompare != 0) {
                return priorityCompare;
            }

            // 优先级相同时按加入时间倒序排序（最新的在前面）
            if (vo1.getJoinTime() != null && vo2.getJoinTime() != null) {
                return vo2.getJoinTime().compareTo(vo1.getJoinTime());
            }

            return 0;
        });
    }

    /**
     * 检查用户是否已创建过指定名称的圈子
     * 用于防止同一用户创建重复名称的圈子
     *
     * @param userId     用户ID
     * @param circleName 圈子名称
     * @return 如果存在同名圈子返回true，否则返回false
     */
    private boolean checkCircleNameExists(Long userId, String circleName) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleInfo::getUserId, userId);
        queryWrapper.eq(SocialCircleInfo::getSocialCircleName, circleName);
        queryWrapper.ne(SocialCircleInfo::getSocialCircleStatus, "注销");  // 排除已注销的圈子

        // 查询数量，如果大于0说明存在同名圈子
        return this.count(queryWrapper) > 0;
    }

    /**
     * 条件搜索圈子列表（分页）
     * 支持按圈子名称、分类、地址等多维度条件进行搜索
     * 只返回审核通过且公开的圈子
     *
     * @param request 搜索条件和分页参数
     * @return 分页搜索结果
     */
    @Override
    public PageObj<SocialCircleSearchVO> searchCircles(SocialCircleSearchRequest request) {
        
        // 参数校验：确保分页参数有效
        if (request.getPage() == null || request.getPage() < 1) {
            request.setPage(1L);
        }
        if (request.getLimit() == null || request.getLimit() < 1) {
            request.setLimit(10L);
        }
        
        // 分页参数限制：防止查询过大的数据量
        if (request.getLimit() > 100) {
            request.setLimit(100L);
        }
        
        // 排序参数校验
        if (!StringUtils.hasText(request.getOrderBy())) {
            request.setOrderBy("createTime");
        }
        if (!StringUtils.hasText(request.getOrderDirection())) {
            request.setOrderDirection("desc");
        }
        
        // 调用Mapper层进行分页查询
        List<SocialCircleSearchVO> records = socialCircleInfoMapper.searchCirclesWithPaging(request);
        Long total = socialCircleInfoMapper.countSearchCircles(request);
        
        // 构建分页结果对象
        PageObj<SocialCircleSearchVO> pageResult = new PageObj<>();
        pageResult.setRecords(records != null ? records : new ArrayList<>());
        pageResult.setTotal(total != null ? total : 0L);
        
        // 处理分类信息和地址信息
        if (!CollectionUtils.isEmpty(records)) {
            processCategoryAndAddressInfo(records);
        }
        
        return pageResult;
    }

    /**
     * 处理圈子列表的分类信息和地址信息
     * 为每个圈子填充完整的分类列表和格式化的地址信息
     *
     * @param circles 圈子列表
     */
    private void processCategoryAndAddressInfo(List<SocialCircleSearchVO> circles) {
        for (SocialCircleSearchVO circle : circles) {
            // 处理分类信息
            List<SocialCircleSearchVO.CategoryInfo> categoryList = 
                socialCircleInfoMapper.getCategoriesByCircleId(circle.getSocialCircleId());
            circle.setCategoryList(categoryList);
            
            // 设置主分类名称（第一个分类）
            if (!CollectionUtils.isEmpty(categoryList)) {
                circle.setPrimaryCategoryName(categoryList.get(0).getCategoryName());
            }
            
            // 处理地址信息 - 拼接完整地址
            if (StringUtils.hasText(circle.getProvinceName()) || StringUtils.hasText(circle.getCityName()) || 
                StringUtils.hasText(circle.getDistrictName()) || StringUtils.hasText(circle.getTownName()) || 
                StringUtils.hasText(circle.getFullAddress())) {
                
                StringBuilder completeAddress = new StringBuilder();
                if (StringUtils.hasText(circle.getProvinceName())) {
                    completeAddress.append(circle.getProvinceName());
                }
                if (StringUtils.hasText(circle.getCityName())) {
                    completeAddress.append(circle.getCityName());
                }
                if (StringUtils.hasText(circle.getDistrictName())) {
                    completeAddress.append(circle.getDistrictName());
                }
                if (StringUtils.hasText(circle.getTownName())) {
                    completeAddress.append(circle.getTownName());
                }
                if (StringUtils.hasText(circle.getFullAddress())) {
                    completeAddress.append(circle.getFullAddress());
                }
                
                circle.setCompleteAddress(completeAddress.toString());
                circle.setHasAddress(true);
            } else {
                circle.setHasAddress(false);
            }
        }
    }

    /**
     * 检查用户是否有权限查看指定圈子
     * 用户必须是该圈子的成员（圈主、管理员或普通成员）才能查看圈子详情
     *
     * @param circleId 圈子ID
     * @param userId   用户ID
     * @return 如果用户有权限返回true，否则返回false
     */
    private boolean checkUserCirclePermission(Long circleId, Long userId) {
        // 使用LambdaQueryWrapper构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMembers::getUserId, userId);
        queryWrapper.eq(SocialCircleMembers::getIsDeleted, 0);  // 只查询未删除的记录
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");  // 只查询已入圈状态的记录

        // 查询数量，如果大于0说明用户是该圈子的成员，有权限查看
        return socialCircleMembersMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查用户个人实名认证状态
     * 通过调用用户认证服务查询认证状态
     *
     * @param userId 用户ID
     * @return 圈子创建结果VO（包含认证状态信息）
     */
    private SocialCircleCreateResultVO checkUserPersonalAuth(Long userId) {
        try {
            // 调用用户认证服务查询个人认证状态
            ResultData<PersonalAuthInfoVo> authResult = userCertificationClient.getUserPersonalAuthStatus(userId);
            // 检查认证服务返回结果
            if (authResult != null && authResult.getData() != null) {
                PersonalAuthInfoVo authInfo = authResult.getData();
                
                // 检查是否已认证
                if (Boolean.TRUE.equals(authInfo.getIsPersonRealNameAuth())) {
                    // 已认证，允许继续创建圈子
                    SocialCircleCreateResultVO result = new SocialCircleCreateResultVO();
                    result.setIsPersonRealNameAuth(true);
                    return result;
                } else {
                    // 未认证，返回认证失败结果
                    return SocialCircleCreateResultVO.authFailed();
                }
            } else {
                // 认证服务返回异常数据
                return SocialCircleCreateResultVO.authServiceError();
            }
            
        } catch (Exception e) {
            // 认证服务调用异常，记录日志并返回服务错误
            return SocialCircleCreateResultVO.authServiceError();
        }
    }

    /**
     * 获取普通成员需要关联的菜单ID列表
     * 包括前端选择的菜单ID + 这些菜单下所有function_type为F（按钮）的子菜单ID
     * 
     * @param selectedFunctionIds 前端选择的菜单ID列表
     * @return 完整的菜单ID列表（包含按钮权限）
     */
    private List<Long> getMemberFunctionList(List<Long> selectedFunctionIds) {
        if (CollectionUtils.isEmpty(selectedFunctionIds)) {
            return new ArrayList<>();
        }
        
        // 1. 初始化结果列表，包含前端选择的菜单ID
        List<Long> allFunctionIds = new ArrayList<>(selectedFunctionIds);
        
        // 2. 递归查询选中菜单下所有function_type为F（按钮）的子菜单
        List<PlatfCircleFunction> buttonFunctions = platfCircleFunctionMapper.selectButtonSubMenusRecursive(selectedFunctionIds);
        
        // 3. 提取按钮菜单的ID并添加到结果列表
        if (!CollectionUtils.isEmpty(buttonFunctions)) {
            List<Long> buttonFunctionIds = buttonFunctions.stream()
                    .map(PlatfCircleFunction::getFunctionId)
                    .collect(Collectors.toList());
            allFunctionIds.addAll(buttonFunctionIds);
        }
        
        // 4. 去重并返回
        return allFunctionIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 查询普通成员在指定圈子中可访问的按钮权限列表
     * 通过递归查询圈子详情下的所有C类型菜单，然后查询这些菜单下的F类型按钮，
     * 最后与权限表匹配过滤出普通成员有权限的按钮
     *
     * @param socialCircleId 圈子ID
     * @param userId 用户ID（用于权限验证）
     * @return 普通成员可访问的按钮权限列表
     */
    @Override
    public List<CircleFunctionMenuVO> getMemberButtonPermissions(Long socialCircleId, Long userId) {
        // 1. 参数验证
        if (socialCircleId == null || userId == null) {
            return new ArrayList<>();
        }
        
        // 2. 验证用户是否为该圈子的成员
        boolean isMember = checkUserCirclePermission(socialCircleId, userId);
        if (!isMember) {
            return new ArrayList<>(); // 用户不是圈子成员，无权限查看
        }
        
        // 3. 查询普通成员的按钮权限
        List<PlatfCircleFunction> buttonFunctions = socialCircleInfoMapper.selectMemberButtonPermissions(socialCircleId);
        
        // 4. 转换为VO对象并返回
        if (CollectionUtils.isEmpty(buttonFunctions)) {
            return new ArrayList<>();
        }
        
        return buttonFunctions.stream()
                .map(this::convertPlatfCircleFunctionToVO)
                .collect(Collectors.toList());
    }

    /**
     * 将PlatfCircleFunction实体转换为CircleFunctionMenuVO对象
     * @param function 功能菜单实体
     * @return 菜单VO对象
     */
    private CircleFunctionMenuVO convertPlatfCircleFunctionToVO(PlatfCircleFunction function) {

        CircleFunctionMenuVO vo = new CircleFunctionMenuVO();
        
        // 基本信息
        vo.setFunctionId(function.getFunctionId());
        vo.setFunctionTitle(function.getFunctionTitle());
        vo.setParentFunctionId(function.getParentFunctionId());
        vo.setAssociateFunctionId(function.getAssociateFunctionId());
        vo.setSortNumber(function.getSortNumber());
        
        // 路由和组件信息
        vo.setPath(function.getPath());
        vo.setComponent(function.getComponent());
        vo.setFunctionType(function.getFunctionType());
        vo.setPerms(function.getPerms());
        
        // 显示相关信息
        vo.setIcon(function.getIcon());
        vo.setIconUrl(function.getIconUrl());
        vo.setPromptMessage(function.getPromptMessage());
        
        // 功能配置信息
        vo.setStorageSize(function.getStorageSize());
        vo.setIsCharged(function.getIsCharged());
        vo.setIsFrame(function.getIsFrame());
        vo.setIsVisible(function.getIsVisible());
        vo.setStatus(function.getStatus());
        vo.setIsDefault(function.getIsDefault() != null && function.getIsDefault() ? 1 : 0);
        
        // 分类和位置信息
        vo.setFunctionCateg(function.getFunctionCateg());
        vo.setFunctionLocation(function.getFunctionLocation());
        vo.setFunctionStatus(function.getFunctionStatus());
        vo.setIsLeaf(function.getIsLeaf());
        
        // 模块和操作信息
        vo.setModuleName(function.getModuleName());
        vo.setOperationName(function.getOperationName());
        
        // 请求相关信息
        vo.setRequestUrl(function.getRequestUrl());
        vo.setTargetType(function.getTargetType());
        
        return vo;
    }

    /**
     * 创建圈子帖子分类关联
     * 用于圈子创建时，建立圈子与其支持的帖子分类的关联关系
     *
     * @param socialCircleId 圈子ID
     * @param postCategIds 帖子分类ID列表
     * @throws ZxException 业务异常（参数验证失败、数据库操作失败等）
     */
    private void createCirclePostCategRelations(Long socialCircleId, List<Long> postCategIds) {
        try {
            // 调用帖子分类关联服务进行批量创建
            socialCirclePostCategRelatService.createCirclePostCategRelations(socialCircleId, postCategIds);
        } catch (Exception e) {
            // 记录异常日志（这里简化处理，实际项目中应该使用日志框架）
            throw new ZxException(500, "圈子帖子分类关联创建失败：" + e.getMessage());
        }
    }

    /**
     * 查询圈子支持的帖子分类列表
     * 根据圈子ID查询该圈子创建时选择的帖子分类，并转换为完整的分类信息
     *
     * @param socialCircleId 圈子ID
     * @return 圈子支持的帖子分类列表，如果没有关联分类则返回空列表
     */
    private List<PlatfPostCategVO> queryCirclePostCategories(Long socialCircleId) {
        // 1. 参数验证
        if (socialCircleId == null) {
            return new ArrayList<>();
        }

        try {
            // 2. 查询圈子帖子分类关联记录
            List<SocialCirclePostCategRelat> postCategRelations =
                socialCirclePostCategRelatService.getPostCategsByCircleId(socialCircleId);

            // 3. 如果没有关联分类，则返回空列表
            if (CollectionUtils.isEmpty(postCategRelations)) {
                return new ArrayList<>();  // 该圈子没有设置帖子分类限制
            }

            // 3. 提取帖子分类ID列表
            List<Long> postCategIds = postCategRelations.stream()
                    .map(SocialCirclePostCategRelat::getPostCategId)
                    .collect(Collectors.toList());

            // 4. 根据分类ID列表查询完整的分类信息
            List<PlatfPostCateg> postCategList = platfPostCategService.listByIds(postCategIds);

            if (CollectionUtils.isEmpty(postCategList)) {
                return new ArrayList<>();
            }

            // 5. 转换为VO对象并按排序指数排序
            return postCategList.stream()
                    .map(this::convertPostCategToVO)
                    .sorted(Comparator.comparing(PlatfPostCategVO::getSortNumber,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            // 记录异常日志，但不影响主流程
            // 实际项目中应该使用日志框架记录异常
            return new ArrayList<>();
        }
    }

    /**
     * 将PlatfPostCateg实体转换为PlatfPostCategVO对象
     *
     * @param postCateg 帖子分类实体
     * @return 帖子分类VO对象
     */
    private PlatfPostCategVO convertPostCategToVO(PlatfPostCateg postCateg) {
        if (postCateg == null) {
            return null;
        }

        // 使用BeanUtil进行对象拷贝
        PlatfPostCategVO vo = BeanUtil.copyProperties(postCateg, PlatfPostCategVO::new);

        // 设置便捷属性
        vo.setHasChildren(false);  // 在圈子分类列表中，不需要展示子分类
        vo.setChildrenCount(0);

        return vo;
    }

}
