package zx.vanx.social_circle.mapper;

import org.apache.ibatis.annotations.Param;
import zx.vanx.social_circle.entity.SocialCirclePost;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo;

import java.util.List;

/**
 * <p>
 * 用户圈子帖子表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface SocialCirclePostMapper extends BaseMapper<SocialCirclePost> {

    /**
     * 查询最近帖子列表（用于Redis同步）
     * 联表查询帖子主表、快表和媒体表，构建完整的RedisCirclePostVo对象
     * 
     * @param limit 查询限制数量
     * @return 帖子列表
     */
    List<RedisCirclePostVo> selectRecentPostsForRedis(@Param("limit") Integer limit);
}
