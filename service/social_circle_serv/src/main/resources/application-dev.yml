server:
  port: 9090
spring:
  application:
    name: social-circle

--- # nacos
spring:
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        ip: *************
        port: 9090
        # 心跳配置
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        ip-delete-timeout: 30000
        # 服务注册配置
        register-enabled: true
        cluster-name: DEFAULT
        group: DEFAULT_GROUP
        namespace: public
        # 连接稳定性配置
        naming-load-cache-at-start: true
        watch-delay: 5000


  # 数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3306/vanx_social_circle?serverTimezone=GMT%2B8&useLegacyDatetimeCode=false&zeroDateTimeBehavior=convertToNull
    password: ruoyi123
    username: root

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: zx.vanx.social_circle.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'



#设置远程调用链接超时时间和读取超时时间
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 20000

--- # redis
spring:
  redis:
    host: *************
    port: 6379
    password: ruoyi123
    database: 1
    timeout: 1800000

--- # rabbitmq
spring:
  rabbitmq:
    template:
      retry:
        enabled: true # 开启重试功能
        initial-interval: 1000ms # 第一次重试间隔
        max-attempts: 10 # 最大重试次数
        multiplier: 2 # 间隔倍数

    host: *************
    port: 5672
    username: zx-vanx
    password: zx-vanx123
    virtual-host: /zx-vanx # 虚拟主机

    connection-timeout: 1s # 连接超时时间
    publisher-returns: true # 发布确认
    publisher-confirm-type: correlated # 发布确认类型
    listener:
      simple:
        acknowledge-mode: manual # 手动确认
        prefetch: 3 # 预取消息数量
        retry:
          enabled: true # 开启重试功能
          initial-interval: 100ms # 第一次重试间隔
          max-attempts: 10 # 最大重试次数
          multiplier: 2 # 间隔倍数

--- # sa-token
# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: token
  # token有效期 设为一天 (必定过期) 单位: 秒
  timeout: 86400
  # 多端不同 token 有效期 可查看 LoginHelper.loginByDevice 方法自定义
  # token最低活跃时间 (指定时间无操作就过期) 单位: 秒
  active-timeout: 86400
  # 允许动态设置 token 有效期
  dynamic-active-timeout: true
  # 开启内网服务调用鉴权
  check-same-token: true
  # Same-Token的有效期 (单位: 秒)(默认一天）
  # same-token-timeout: 600
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # token前缀
  token-prefix:
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz
--- # redisson
# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${spring.application.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

