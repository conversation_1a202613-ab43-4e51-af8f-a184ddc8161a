<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.SocialCirclePostMapper">

    <!-- 结果映射 -->
    <resultMap id="RedisCirclePostVoMap" type="zx.vanx.social_circle.redis.circle_post.RedisCirclePostVo">
        <id property="circlePostId" column="circlePostId"/>
        <result property="socialCircleId" column="socialCircleId"/>
        <result property="userId" column="userId"/>
        <result property="userNickName" column="userNickName"/>
        <result property="userAvatar" column="userAvatar"/>
        <result property="circlePostTitle" column="circlePostTitle"/>
        <result property="circlePostContent" column="circlePostContent"/>
        <result property="postStatus" column="postStatus"/>
        <result property="postCategId" column="postCategId"/>
        <result property="createdTime" column="createdTime"/>
        <result property="numOfLikes" column="numOfLikes"/>
        <result property="numOfCollect" column="numOfCollect"/>
        <result property="numOfComments" column="numOfComments"/>
        <result property="numOfPlayed" column="numOfPlayed"/>
        <result property="numOfShared" column="numOfShared"/>
        <result property="isLoggedIn" column="isLoggedIn"/>
        <result property="isFollowed" column="isFollowed"/>
        <!-- 媒体信息集合 -->
        <collection property="postMediaList" ofType="zx.vanx.social_circle.redis.circle_post.RedisCirclePostMediaVo">
            <id property="postMediaId" column="postMediaId"/>
            <result property="mediaUrl" column="mediaUrl"/>
            <result property="mediaName" column="mediaName"/>
            <result property="mediaCoverUrl" column="mediaCoverUrl"/>
            <result property="mediaType" column="mediaType"/>
            <result property="sortNumber" column="sortNumber"/>
        </collection>
    </resultMap>

    <!-- 查询最近帖子列表（用于Redis同步） -->
    <select id="selectRecentPostsForRedis" resultMap="RedisCirclePostVoMap">
        SELECT 
            p.circle_post_id AS circlePostId,
            p.social_circle_id AS socialCircleId,
            p.user_id AS userId,
            p.user_name AS userNickName,
            p.circle_post_title AS circlePostTitle,
            p.circle_post_content AS circlePostContent,
            p.post_status AS postStatus,
            p.post_categ_id AS postCategId,
            p.created_time AS createdTime,
            
            <!-- 快表统计信息 -->
            IFNULL(f.num_of_likes, 0) AS numOfLikes,
            IFNULL(f.num_of_collect, 0) AS numOfCollect,
            IFNULL(f.num_of_comments, 0) AS numOfComments,
            IFNULL(f.num_of_played, 0) AS numOfPlayed,
            IFNULL(f.num_of_shared, 0) AS numOfShared,
            
            <!-- 媒体信息 -->
            m.post_media_id AS postMediaId,
            m.media_url AS mediaUrl,
            m.media_name AS mediaName,
            m.media_cover_url AS mediaCoverUrl,
            m.media_type AS mediaType,
            m.sort_number AS sortNumber,
            
            <!-- 默认值 -->
            '' AS userAvatar,
            false AS isLoggedIn,
            0 AS isFollowed
            
        FROM vanx_social_circle_post p
        LEFT JOIN vanx_social_circle_post_fast f ON p.circle_post_id = f.circle_post_id
        LEFT JOIN vanx_social_circle_post_media m ON p.circle_post_id = m.circle_post_id
        WHERE p.post_status = '上架'
          AND p.is_deleted = 0
          AND f.is_deleted = 0
          AND (m.is_deleted = 0 OR m.is_deleted IS NULL)
        ORDER BY p.created_time DESC, m.sort_number ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询今日发帖数量 -->
    <select id="getTodayPostCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM vanx_social_circle_post p
        WHERE p.is_deleted = 0
          AND p.post_status = '上架'
          AND DATE(p.created_time) = CURDATE()
    </select>

</mapper>
