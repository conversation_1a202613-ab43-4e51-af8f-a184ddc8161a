/*
 Navicat Premium Dump SQL

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31)
 Source Host           : *************:3306
 Source Schema         : vanx_social_circle

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31)
 File Encoding         : 65001

 Date: 16/07/2025 15:59:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for vanx_platf_circle_func_cost
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_circle_func_cost`;
CREATE TABLE `vanx_platf_circle_func_cost`  (
  `cost_id` bigint NOT NULL COMMENT '支付的id号',
  `function_id` bigint NULL DEFAULT NULL COMMENT '菜单的id号',
  `function_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单的标题',
  `cost_start_time` datetime NULL DEFAULT NULL COMMENT '菜单收费的开始时间',
  `cost_end_time` datetime NULL DEFAULT NULL COMMENT '菜单收费的开始时间',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `function_cost_model` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户缴费信息',
  `other_info_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除',
  PRIMARY KEY (`cost_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '系统菜单费用表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_platf_circle_func_cost
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_platf_circle_function
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_circle_function`;
CREATE TABLE `vanx_platf_circle_function`  (
  `function_id` bigint NOT NULL COMMENT '菜单的id号',
  `function_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单标题',
  `func_front_shown_title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单在前端页面的标题',
  `parent_function_id` bigint NULL DEFAULT NULL COMMENT '菜单父类id',
  `associate_function_id` bigint NULL DEFAULT NULL COMMENT '该功能相关联的配套功能的ID',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `func_operation_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '该菜单对应后端功能操作的类型：（1.添加；2.浏览；3.等）',
  `function_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单类型（M目录 C菜单 F按钮 ）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `storage_size` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单对应服务器上文件夹的容量大小',
  `is_charged` tinyint NULL DEFAULT NULL COMMENT '是否收费',
  `is_frame` tinyint(1) NULL DEFAULT 1 COMMENT '是否为新窗口打开（0 否 1是）',
  `is_visible` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示（0隐藏 1显示）',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '菜单状态（0停用 1正常）',
  `is_default` tinyint(1) NULL DEFAULT NULL COMMENT '是否圈子的默认功能',
  `function_categ` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '功能菜单的类别',
  `function_location` tinyint NULL DEFAULT 0 COMMENT '菜单项在前端界面的位置（0 上部分 1 下部分，默认0）',
  `prompt_message` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单提示信息',
  `icon_url` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单对应图片的url',
  `request_url` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单请求url',
  `target_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '请求目标类型：请求页面；请求函数接口',
  `function_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单的状态 0 显示 1 隐藏',
  `is_leaf` tinyint NULL DEFAULT 0 COMMENT '是否叶子菜单项（0 不是 1 是 ，默认0）',
  `module_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜单所在模块名称',
  `operation_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '所在菜单的操作名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除 默认1',
  PRIMARY KEY (`function_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '菜单功能表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_platf_circle_function
-- ----------------------------
INSERT INTO `vanx_platf_circle_function` VALUES (100, '圈子系统', NULL, 0, NULL, '1', '', NULL, NULL, 'M', NULL, '', NULL, 0, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, '', 0, '', NULL, '2025-07-09 10:18:01', NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (101, '用户管理', NULL, 115, NULL, '1', '', '', NULL, 'C', '', '', NULL, 0, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, '', 0, '', NULL, '2023-07-07 10:16:21', NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (102, '公告管理', NULL, 116, NULL, '1', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (103, '视频/图片管理', NULL, 116, NULL, '2', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (104, '用户审核', NULL, 117, NULL, '1', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (105, '置顶审核', NULL, 117, NULL, '2', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (107, '趣野管理', NULL, 116, NULL, '3', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (108, '互助管理', NULL, 116, NULL, '4', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (109, '优惠促销管理', NULL, 116, NULL, '5', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (110, '圈子详情', NULL, 100, NULL, '2', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (111, '圈子管理', NULL, 100, NULL, '1', NULL, NULL, NULL, 'M', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (112, '趣野', NULL, 110, 107, '3', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (113, '互助', NULL, 110, 108, '4', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (114, '优惠促销', NULL, 110, 109, '5', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (115, '常用管理', NULL, 111, NULL, '1', NULL, NULL, NULL, 'M', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (116, '内容管理', NULL, 111, NULL, '2', NULL, NULL, NULL, 'M', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (117, '审核中心', NULL, 111, NULL, '3', NULL, NULL, NULL, 'M', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (118, '处罚管理', NULL, 115, NULL, '2', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (119, '举报管理', NULL, 115, NULL, '3', NULL, NULL, NULL, 'C', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (120, '发布趣野', NULL, 112, NULL, '3', NULL, NULL, NULL, 'F', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_platf_circle_function` VALUES (121, '发布互助', NULL, 113, NULL, '3', NULL, NULL, NULL, 'F', NULL, NULL, NULL, NULL, 1, 1, 1, 1, NULL, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_platf_post_categ
-- ----------------------------
DROP TABLE IF EXISTS `vanx_platf_post_categ`;
CREATE TABLE `vanx_platf_post_categ`  (
  `post_categ_id` bigint NOT NULL COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '该用户的id',
  `shop_id` bigint NULL DEFAULT NULL COMMENT '该用户商铺的id',
  `categ_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '作品分类类目名称',
  `categ_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '作品分类的类型，如：视频分类；照片分类',
  `icon_url` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '作品分类的图标路径url',
  `backg_image_url` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '作品分类的背景图片路径url',
  `backg_colour` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '作品分类的背景颜色',
  `categ_level` tinyint NULL DEFAULT NULL COMMENT '类目级别，1、一级类目2、二级类目3、三级类目4、四级类目',
  `categ_crit_id` bigint NULL DEFAULT NULL COMMENT '分类的标准的ID，如：对于视频作品来说，其分类可以有多方面，比如：内容分类；题材分类；长短分类；。。。。',
  `categ_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '商品分类类目编码',
  `parent_categ_id` bigint NULL DEFAULT NULL COMMENT '父级分类，顶级目录填0',
  `is_leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否为叶子节点',
  `sort_number` int NULL DEFAULT NULL COMMENT '排序指数,越小越靠前',
  `add_source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类的类目来源：1、自定义添加2、平台添加',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_categ_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_media_category' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_platf_post_categ
-- ----------------------------
INSERT INTO `vanx_platf_post_categ` VALUES (4, NULL, NULL, '骑行', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 2, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (5, NULL, NULL, '登山', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 3, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (6, NULL, NULL, '游泳', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (7, NULL, NULL, '健身房', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (8, NULL, NULL, '足球', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, 3, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (9, NULL, NULL, '结伴旅游', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 3, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (10, NULL, NULL, '国内旅游', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 9, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (11, NULL, NULL, '国外旅游', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 9, NULL, 2, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (12, NULL, NULL, '短途旅行', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 10, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (13, NULL, NULL, '周末游', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 10, NULL, 2, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (1001, NULL, NULL, '户外体育', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (1002, NULL, NULL, '运动健身', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 2, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `vanx_platf_post_categ` VALUES (1003, NULL, NULL, '广场舞', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_address
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_address`;
CREATE TABLE `vanx_social_circle_address`  (
  `social_circle_address_id` bigint NOT NULL COMMENT 'id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子id',
  `country_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在国家国名',
  `province_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在省',
  `city_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在市',
  `district_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在地区',
  `town_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在镇、街道名称',
  `full_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户详情地址',
  `postal_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮编',
  `location` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '坐标',
  `location_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户位置类型：家庭地址、工作地址、老家地址',
  `is_default` tinyint(1) NULL DEFAULT NULL COMMENT '地址是否默认使用',
  `info_address_status` tinyint(1) NULL DEFAULT NULL COMMENT '地址是否有效',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`social_circle_address_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统圈子地址表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_address
-- ----------------------------
INSERT INTO `vanx_social_circle_address` VALUES (2001, 1001, '中国', '北京市', '朝阳区', '三里屯街道', '工体北路', '工体北路甲2号', '100027', '116.465,39.932', '工作地址', 1, 1, 421054829645824000, 421054829645824000, '2023-05-10 09:20:00', '2023-05-10 09:20:00', 0, NULL, NULL, '摄影器材集散地');
INSERT INTO `vanx_social_circle_address` VALUES (2002, 1002, '中国', '上海市', '黄浦区', '南京东路街道', '南京东路', '南京东路123号书店二楼', '200001', '121.485,31.240', '家庭地址', 1, 1, 421369491834830848, 421369491834830848, '2023-04-15 14:35:00', '2023-04-18 10:25:00', 0, NULL, NULL, '知名书店聚集区');
INSERT INTO `vanx_social_circle_address` VALUES (2003, 1003, '中国', '广东省', '广州市', '天河区', '体育西路', '体育西路购物中心B1美食城', '510620', '113.326,23.137', '工作地址', 1, 1, 425450591234326528, 425450591234326528, '2023-06-05 11:25:00', '2023-06-05 15:50:00', 0, NULL, NULL, '美食文化中心');
INSERT INTO `vanx_social_circle_address` VALUES (2004, 1004, '中国', '浙江省', '杭州市', '西湖区', '文二路', '文二路健身中心', '310012', '120.131,30.274', '工作地址', 1, 1, 452999623867400192, 452999623867400192, '2023-03-20 08:50:00', '2023-03-22 09:35:00', 0, NULL, NULL, '专业健身场所');
INSERT INTO `vanx_social_circle_address` VALUES (2005, 1006, '中国', '北京市', '海淀区', '中关村街道', '中关村大街', '中关村科技园区A座', '100190', '116.310,39.983', '工作地址', 1, 1, 560933616599728128, 560933616599728128, '2023-02-18 10:20:00', '2023-02-20 14:30:00', 0, NULL, NULL, '科技创新中心');
INSERT INTO `vanx_social_circle_address` VALUES (638435897729122305, 638435897729122304, '中国', '北京市', '朝阳区', '三里屯街道', '工体北路', '工体北路甲2号', '100027', '116.465,39.932', '工作地址', 1, 1, 421054829645824000, 421054829645824000, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, '摄影器材集散地');

-- ----------------------------
-- Table structure for vanx_social_circle_category_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_category_relat`;
CREATE TABLE `vanx_social_circle_category_relat`  (
  `social_circle_category_relat_id` bigint NOT NULL COMMENT 'id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子id',
  `social_circle_categ_id` bigint NULL DEFAULT NULL COMMENT '圈子对应的分类的id',
  `social_circle_categ_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子对应的分类名称',
  `social_circle_categ_level` tinyint NULL DEFAULT NULL COMMENT '圈子分类级别',
  `social_circle_categ_crit_id` bigint NULL DEFAULT NULL COMMENT '圈子分类的标准的ID，如：对于视频作品来说，其分类可以有多方面，比如：内容分类；题材分类；长短分类；。。。。',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`social_circle_category_relat_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'vanx_media_categ_relat' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_social_circle_category_relat
-- ----------------------------
INSERT INTO `vanx_social_circle_category_relat` VALUES (638435897729122307, 638435897729122304, 5001, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_category_relat` VALUES (638435897729122308, 638435897729122304, 5011, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_func_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_func_relat`;
CREATE TABLE `vanx_social_circle_func_relat`  (
  `circle_func_id` bigint NOT NULL COMMENT '权限id号',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子id',
  `role_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子成员角色：圈主、管理员、普通成员',
  `function_id` bigint NULL DEFAULT NULL COMMENT '菜单id号',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '最后修改者用户id',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除 1 未删除 2 删除 默认 1',
  PRIMARY KEY (`circle_func_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '菜单权限功能表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_social_circle_func_relat
-- ----------------------------
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089601, 638435897729122304, '圈主', 101, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089602, 638435897729122304, '圈主', 102, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089603, 638435897729122304, '圈主', 103, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089604, 638435897729122304, '圈主', 104, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089605, 638435897729122304, '圈主', 105, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089606, 638435897729122304, '圈主', 107, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089607, 638435897729122304, '圈主', 108, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089608, 638435897729122304, '圈主', 109, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089609, 638435897729122304, '圈主', 115, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089610, 638435897729122304, '圈主', 116, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089611, 638435897729122304, '圈主', 117, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089612, 638435897729122304, '圈主', 118, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089613, 638435897729122304, '圈主', 119, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089614, 638435897729122304, '普通成员', 112, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089615, 638435897729122304, '普通成员', 113, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089616, 638435897729122304, '普通成员', 114, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089617, 638435897729122304, '普通成员', 120, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_func_relat` VALUES (638435902024089618, 638435897729122304, '普通成员', 121, '2025-07-14 10:57:22', '2025-07-14 10:57:22', NULL, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for vanx_social_circle_info
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_info`;
CREATE TABLE `vanx_social_circle_info`  (
  `social_circle_id` bigint NOT NULL COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '创建该圈子的用户id',
  `social_circle_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子唯一识别码',
  `social_circle_logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的LOGO',
  `social_circle_link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '圈子链接地址',
  `social_circle_logo_minio_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的minio名称',
  `social_circle_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子名称',
  `social_circle_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的描述',
  `social_circle_advertise` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的广告语',
  `social_circle_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的申请状态：申请、开通、驳回、暂停、撤销、注销',
  `visibility_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子公开状态：公开、私有',
  `social_circle_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社交圈子的来源：个人申请、平台创建',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`social_circle_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '圈子信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_info
-- ----------------------------
INSERT INTO `vanx_social_circle_info` VALUES (1001, 421054829645824000, 'circle_code_382826366', 'https://vanx-media.oss/logos/photography.png', NULL, 'circle/logo/photography_1001.png', '摄影爱好者', '分享摄影技巧和作品的圈子', '捕捉生活中的精彩瞬间', '开通', '公开', '个人申请', 421054829645824000, 421054829645824000, '2023-05-10 09:15:22', '2023-05-10 09:15:22', 0, NULL, NULL, '热门圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1002, 421369491834830848, 'circle_code_215118999', 'https://vanx-media.oss/logos/reading.png', NULL, 'circle/logo/reading_1002.png', '阅读分享会', '读书心得交流与好书推荐', '与书为伴，与思想同行', '开通', '公开', '个人申请', 421369491834830848, 421369491834830848, '2023-04-15 14:30:45', '2023-04-18 10:22:18', 0, NULL, NULL, '活跃圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1003, 425450591234326528, 'circle_code_381740422', 'https://vanx-media.oss/logos/cooking.png', NULL, 'circle/logo/cooking_1003.png', '美食厨房', '分享美食制作技巧和食谱', '舌尖上的艺术', '开通', '公开', '个人申请', 425450591234326528, 425450591234326528, '2023-06-05 11:20:36', '2023-06-05 15:45:10', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1004, 452999623867400192, 'circle_code_323093296', 'https://vanx-media.oss/logos/fitness.png', NULL, 'circle/logo/fitness_1004.png', '健身达人', '健身经验和健康生活方式分享', '强健体魄，享受生活', '开通', '公开', '个人申请', 452999623867400192, 452999623867400192, '2023-03-20 08:45:15', '2023-03-22 09:30:25', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1005, 529332256553074688, 'circle_code_777907010', 'https://vanx-media.oss/logos/travel.png', NULL, 'circle/logo/travel_1005.png', '旅行者联盟', '旅行经验分享和目的地推荐', '行走世界，收获感动', '申请', '公开', '个人申请', 529332256553074688, 529332256553074688, '2023-06-12 16:40:20', '2023-06-12 16:40:20', 0, NULL, NULL, '新建圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1006, 560933616599728128, 'circle_code_616427304', 'https://vanx-media.oss/logos/coding.png', NULL, 'circle/logo/coding_1006.png', '编程技术社区', '技术分享与问题解决的开发者圈子', '码出未来', '开通', '公开', '个人申请', 560933616599728128, 560933616599728128, '2023-02-18 10:15:30', '2023-02-20 14:25:40', 0, NULL, NULL, '技术圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1007, 561351662946516992, 'circle_code_749869754', 'https://vanx-media.oss/logos/painting.png', NULL, 'circle/logo/painting_1007.png', '绘画爱好者', '绘画作品分享与技巧交流', '用画笔描绘心中的世界', '申请', '公开', '个人申请', 561351662946516992, 561351662946516992, '2023-06-15 09:10:25', '2023-06-15 09:10:25', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1008, 561351675831418880, 'circle_code_284841445', 'https://vanx-media.oss/logos/music.png', NULL, 'circle/logo/music_1008.png', '音乐分享站', '音乐鉴赏与创作分享', '音符里的灵魂交流', '开通', '私有', '个人申请', 561351675831418880, 561351675831418880, '2023-04-05 15:35:40', '2023-04-06 11:20:15', 0, NULL, NULL, '私密圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1009, 561351680126386184, 'circle_code_350259849', 'https://vanx-media.oss/logos/gardening.png', NULL, 'circle/logo/gardening_1009.png', '园艺达人', '植物种植和养护经验交流', '让生活充满绿意', '开通', '公开', '个人申请', 561351680126386184, 561351680126386184, '2023-03-10 13:25:50', '2023-03-12 09:45:30', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1010, 561351684421353481, 'circle_code_749301941', 'https://vanx-media.oss/logos/gaming.png', NULL, 'circle/logo/gaming_1010.png', '游戏玩家社区', '游戏攻略与游戏体验分享', '游戏改变世界', '驳回', '公开', '个人申请', 561351684421353481, 561351684421353481, '2023-05-25 18:20:15', '2023-05-26 10:15:40', 0, NULL, NULL, '未通过审核');
INSERT INTO `vanx_social_circle_info` VALUES (1011, 561351688716320777, 'circle_code_866122833', 'https://vanx-media.oss/logos/movie.png', NULL, 'circle/logo/movie_1011.png', '电影爱好者', '电影鉴赏与影评分享', '光影中的艺术', '开通', '公开', '平台创建', 561351688716320777, 561351688716320777, '2023-01-15 12:30:45', '2023-01-16 09:45:20', 0, NULL, NULL, '官方圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1012, 561351693011288073, 'circle_code_172697011', 'https://vanx-media.oss/logos/fashion.png', NULL, 'circle/logo/fashion_1012.png', '时尚穿搭', '时尚趋势与穿搭分享', '做自己的时尚icon', '开通', '公开', '个人申请', 561351693011288073, 561351693011288073, '2023-04-28 14:50:35', '2023-04-30 09:25:10', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1013, 561351697306255368, 'circle_code_753028197', 'https://vanx-media.oss/logos/pets.png', NULL, 'circle/logo/pets_1013.png', '宠物之家', '宠物养护与训练经验交流', '爱宠如家人', '暂停', '公开', '个人申请', 561351697306255368, 561351697306255368, '2023-03-08 11:40:25', '2023-05-10 15:30:20', 0, NULL, NULL, '暂时停用');
INSERT INTO `vanx_social_circle_info` VALUES (1014, 561351701601222664, 'circle_code_946487260', 'https://vanx-media.oss/logos/finance.png', NULL, 'circle/logo/finance_1014.png', '理财投资圈', '投资理财经验与市场分析分享', '财富自由的第一步', '开通', '私有', '个人申请', 561351701601222664, 561351701601222664, '2023-05-02 09:15:40', '2023-05-03 10:45:25', 0, NULL, NULL, '金融圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1015, 561351710191157248, 'circle_code_479844292', 'https://vanx-media.oss/logos/education.png', NULL, 'circle/logo/education_1015.png', '教育交流平台', '教育资源与学习方法分享', '教育改变未来', '开通', '公开', '平台创建', 561351710191157248, 561351710191157248, '2023-02-10 10:20:35', '2023-02-12 14:30:45', 0, NULL, NULL, '官方圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1016, 629585696708919296, 'circle_code_951222421', 'https://vanx-media.oss/logos/startup.png', NULL, 'circle/logo/startup_1016.png', '创业者联盟', '创业经验与资源共享', '成就梦想的起点', '申请', '公开', '个人申请', 629585696708919296, 629585696708919296, '2023-06-18 13:25:40', '2023-06-18 13:25:40', 0, NULL, NULL, '新建圈子');
INSERT INTO `vanx_social_circle_info` VALUES (1017, 629588050350997504, 'circle_code_895886914', 'https://vanx-media.oss/logos/parenthood.png', NULL, 'circle/logo/parenthood_1017.png', '育儿交流会', '育儿经验与心得分享', '用爱陪伴成长', '开通', '公开', '个人申请', 629588050350997504, 629588050350997504, '2023-03-25 09:35:50', '2023-03-26 11:40:15', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_info` VALUES (1018, 629589390380793856, 'circle_code_570222119', 'https://vanx-media.oss/logos/crafting.png', NULL, 'circle/logo/crafting_1018.png', '手工艺术圈', '手工制作与艺术创作分享', '创意改变生活', '撤销', '私有', '个人申请', 629589390380793856, 629589390380793856, '2023-04-10 15:20:30', '2023-05-15 10:25:45', 0, NULL, NULL, '用户主动撤销');
INSERT INTO `vanx_social_circle_info` VALUES (1019, 629589768337915904, 'circle_code_101502633', 'https://vanx-media.oss/logos/language.png', NULL, 'circle/logo/language_1019.png', '语言学习交流', '外语学习方法与资源分享', '语言是打开世界的钥匙', '申请', '公开', '个人申请', 629589768337915904, 629589768337915904, '2023-06-20 11:30:40', '2023-06-20 11:30:40', 0, NULL, NULL, '待审核');
INSERT INTO `vanx_social_circle_info` VALUES (638435897729122304, 421054829645824000, 'circle_code_696985066', 'https://vanx-media.oss/logos/photography.png', 'https://example.com/circle/photography', 'circle/logo/photography_1001.png', '摄影爱好者1', '分享摄影技巧和作品的圈子', '捕捉生活中的精彩瞬间', '开通', '公开', '个人申请', 421054829645824000, 421054829645824000, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_media
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_media`;
CREATE TABLE `vanx_social_circle_media`  (
  `social_circle_media_id` bigint NOT NULL COMMENT '主键ID号',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户ID号',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '最后修改者用户ID号',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子ID号',
  `media_title` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片的标题',
  `media_type` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片类型',
  `media_url` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片URL',
  `media_minio_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片在minio中的名称',
  `sort_number` int NULL DEFAULT NULL COMMENT '排序指数',
  `picture_status` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片的认证状态：未认证、待审核、通过、驳回、已取消',
  `reject_reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子图片认证失败的原因',
  `platf_check_user_id` bigint NULL DEFAULT NULL COMMENT '审核人ID号',
  `platf_recheck_user_id` bigint NULL DEFAULT NULL COMMENT '复审人ID号',
  `check_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息one',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息two',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除（0：未删除、1：已删除、默认：0）',
  PRIMARY KEY (`social_circle_media_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_social_circle_media
-- ----------------------------
INSERT INTO `vanx_social_circle_media` VALUES (3001, '2023-05-10 10:00:00', '2023-05-10 10:00:00', 421054829645824000, 421054829645824000, 1001, '圈子头图', '图片', 'https://vanx-media.oss/circle/1001/header.jpg', 'circle/1001/header_20230510.jpg', 1, '通过', NULL, 1000001, NULL, '2023-05-10 12:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3002, '2023-05-10 10:30:00', '2023-05-10 10:30:00', 421054829645824000, 421054829645824000, 1001, '活动照片1', '图片', 'https://vanx-media.oss/circle/1001/activity1.jpg', 'circle/1001/activity1_20230510.jpg', 2, '通过', NULL, 1000001, NULL, '2023-05-10 12:30:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3003, '2023-04-15 15:00:00', '2023-04-18 10:30:00', 421369491834830848, 421369491834830848, 1002, '读书角落', '图片', 'https://vanx-media.oss/circle/1002/reading_corner.jpg', 'circle/1002/reading_corner_20230415.jpg', 1, '通过', NULL, 1000001, NULL, '2023-04-16 09:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3004, '2023-04-16 14:00:00', '2023-04-18 10:30:00', 421369491834830848, 421369491834830848, 1002, '书友聚会', '图片', 'https://vanx-media.oss/circle/1002/book_meeting.jpg', 'circle/1002/book_meeting_20230416.jpg', 2, '通过', NULL, 1000001, NULL, '2023-04-17 10:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3005, '2023-06-05 12:00:00', '2023-06-05 16:00:00', 425450591234326528, 425450591234326528, 1003, '招牌菜品', '图片', 'https://vanx-media.oss/circle/1003/signature_dish.jpg', 'circle/1003/signature_dish_20230605.jpg', 1, '通过', NULL, 1000002, NULL, '2023-06-05 18:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3006, '2023-06-05 14:00:00', '2023-06-05 16:00:00', 425450591234326528, 425450591234326528, 1003, '制作过程', '视频', 'https://vanx-media.oss/circle/1003/cooking_process.mp4', 'circle/1003/cooking_process_20230605.mp4', 2, '通过', NULL, 1000002, NULL, '2023-06-05 19:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3007, '2023-03-20 09:00:00', '2023-03-22 10:00:00', 452999623867400192, 452999623867400192, 1004, '训练场景', '图片', 'https://vanx-media.oss/circle/1004/training_scene.jpg', 'circle/1004/training_scene_20230320.jpg', 1, '通过', NULL, 1000001, NULL, '2023-03-21 08:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3008, '2023-03-21 15:00:00', '2023-03-22 10:00:00', 452999623867400192, 452999623867400192, 1004, '健身教程', '视频', 'https://vanx-media.oss/circle/1004/fitness_tutorial.mp4', 'circle/1004/fitness_tutorial_20230321.mp4', 2, '通过', NULL, 1000001, NULL, '2023-03-22 11:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3009, '2023-02-18 11:00:00', '2023-02-20 15:00:00', 560933616599728128, 560933616599728128, 1006, '代码展示', '图片', 'https://vanx-media.oss/circle/1006/code_showcase.jpg', 'circle/1006/code_showcase_20230218.jpg', 1, '通过', NULL, 1000003, NULL, '2023-02-19 10:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (3010, '2023-02-19 16:00:00', '2023-02-20 15:00:00', 560933616599728128, 560933616599728128, 1006, '技术分享会', '图片', 'https://vanx-media.oss/circle/1006/tech_sharing.jpg', 'circle/1006/tech_sharing_20230219.jpg', 2, '通过', NULL, 1000003, NULL, '2023-02-20 09:00:00', NULL, NULL, NULL, 0);
INSERT INTO `vanx_social_circle_media` VALUES (638435897729122306, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 421054829645824000, 421054829645824000, 638435897729122304, '圈子头图', '图片', 'https://vanx-media.oss/circle/1001/header.jpg', 'circle/1001/header_20230510.jpg', 1, '待审核', NULL, NULL, NULL, NULL, NULL, NULL, '圈子主要展示图片', 0);

-- ----------------------------
-- Table structure for vanx_social_circle_members
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_members`;
CREATE TABLE `vanx_social_circle_members`  (
  `circle_member_id` bigint NOT NULL COMMENT 'id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '该圈子的id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '圈子成员用户id',
  `user_nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '圈子成员用户昵称',
  `user_remark_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '圈子成员用户姓名',
  `user_photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '该用户好友头像url',
  `user_role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '该用户在圈子所属角色：普通成员、管理员、圈主、关注',
  `user_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '该成员在圈子中的状态：申请、已入圈、驳回、已退圈',
  `user_circle_relat_add_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '该成员与该圈子关系的来源：主动加入、系统推荐',
  `is_default` tinyint(1) NULL DEFAULT NULL COMMENT '是否默认',
  `reason_for_add` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请入圈理由',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`circle_member_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '圈子成员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_members
-- ----------------------------
INSERT INTO `vanx_social_circle_members` VALUES (4001, 1001, 421054829645824000, '摄影师小王', '王志强', 'https://vanx-media.oss/avatars/user_421054829645824000.jpg', '圈主', '已入圈', NULL, 0, '热爱摄影，希望分享经验', 421054829645824000, 421054829645824000, '2023-05-10 09:15:22', '2023-05-10 09:15:22', 0, NULL, NULL, '圈子创建者');
INSERT INTO `vanx_social_circle_members` VALUES (4002, 1001, 500001234567890123, '光影追逐者', '李晓明', 'https://vanx-media.oss/avatars/user_500001234567890123.jpg', '管理员', '已入圈', NULL, 1, '专业摄影师，乐于助人', 421054829645824000, 421054829645824000, '2023-05-11 14:20:30', '2023-05-11 14:20:30', 0, NULL, NULL, '活跃管理员');
INSERT INTO `vanx_social_circle_members` VALUES (4003, 1001, 500002234567890124, '镜头下的世界', '张小美', 'https://vanx-media.oss/avatars/user_500002234567890124.jpg', '普通成员', '已入圈', NULL, 1, '摄影爱好者，想学习技巧', 500001234567890123, 500001234567890123, '2023-05-12 10:15:45', '2023-05-12 10:15:45', 0, NULL, NULL, '新手摄影师');
INSERT INTO `vanx_social_circle_members` VALUES (4004, 1001, 500003234567890125, '风景猎人', '刘大伟', 'https://vanx-media.oss/avatars/user_500003234567890125.jpg', '普通成员', '已入圈', NULL, 1, '喜欢拍摄自然风景', 500001234567890123, 500001234567890123, '2023-05-13 16:30:20', '2023-05-13 16:30:20', 0, NULL, NULL, '风景摄影师');
INSERT INTO `vanx_social_circle_members` VALUES (4005, 1002, 421369491834830848, '书虫小雅', '陈雅文', 'https://vanx-media.oss/avatars/user_421369491834830848.jpg', '圈主', '已入圈', NULL, 1, '热爱阅读，分享读书心得', 421369491834830848, 421369491834830848, '2023-04-15 14:30:45', '2023-04-15 14:30:45', 0, NULL, NULL, '圈子创建者');
INSERT INTO `vanx_social_circle_members` VALUES (4006, 1002, 500004234567890126, '文学青年', '赵文博', 'https://vanx-media.oss/avatars/user_500004234567890126.jpg', '管理员', '已入圈', NULL, 1, '文学专业，喜欢分享好书', 421369491834830848, 421369491834830848, '2023-04-16 09:45:12', '2023-04-16 09:45:12', 0, NULL, NULL, '文学专家');
INSERT INTO `vanx_social_circle_members` VALUES (4007, 1002, 500005234567890127, '读书笔记', '孙小涵', 'https://vanx-media.oss/avatars/user_500005234567890127.jpg', '普通成员', '已入圈', NULL, 1, '喜欢做读书笔记，交流心得', 500004234567890126, 500004234567890126, '2023-04-17 15:20:38', '2023-04-17 15:20:38', 0, NULL, NULL, '笔记达人');
INSERT INTO `vanx_social_circle_members` VALUES (4008, 1002, 500006234567890128, '历史爱好者', '马建国', 'https://vanx-media.oss/avatars/user_500006234567890128.jpg', '普通成员', '申请', NULL, 1, '对历史类书籍很感兴趣', 500006234567890128, 500006234567890128, '2023-04-18 11:10:25', '2023-04-18 11:10:25', 0, NULL, NULL, '待审核用户');
INSERT INTO `vanx_social_circle_members` VALUES (4009, 1003, 425450591234326528, '美食达人', '黄小厨', 'https://vanx-media.oss/avatars/user_425450591234326528.jpg', '圈主', '已入圈', NULL, 1, '专业厨师，分享美食制作', 425450591234326528, 425450591234326528, '2023-06-05 11:20:36', '2023-06-05 11:20:36', 0, NULL, NULL, '圈子创建者');
INSERT INTO `vanx_social_circle_members` VALUES (4010, 1003, 500007234567890129, '烘焙小屋', '林甜甜', 'https://vanx-media.oss/avatars/user_500007234567890129.jpg', '管理员', '已入圈', NULL, 1, '烘焙专家，喜欢制作甜品', 425450591234326528, 425450591234326528, '2023-06-06 13:40:20', '2023-06-06 13:40:20', 0, NULL, NULL, '烘焙专家');
INSERT INTO `vanx_social_circle_members` VALUES (4011, 1003, 500008234567890130, '家常菜手', '王大妈', 'https://vanx-media.oss/avatars/user_500008234567890130.jpg', '普通成员', '已入圈', NULL, 1, '擅长做家常菜，想学新菜', 500007234567890129, 500007234567890129, '2023-06-07 18:25:15', '2023-06-07 18:25:15', 0, NULL, NULL, '家常菜高手');
INSERT INTO `vanx_social_circle_members` VALUES (4012, 1004, 452999623867400192, '健身教练', '李强壮', 'https://vanx-media.oss/avatars/user_452999623867400192.jpg', '圈主', '已入圈', NULL, 1, '专业健身教练，分享健身知识', 452999623867400192, 452999623867400192, '2023-03-20 08:45:15', '2023-03-20 08:45:15', 0, NULL, NULL, '圈子创建者');
INSERT INTO `vanx_social_circle_members` VALUES (4013, 1004, 500009234567890131, '瑜伽小仙女', '张柔美', 'https://vanx-media.oss/avatars/user_500009234567890131.jpg', '管理员', '已入圈', NULL, 1, '瑜伽教练，推广健康生活', 452999623867400192, 452999623867400192, '2023-03-21 12:15:45', '2023-03-21 12:15:45', 0, NULL, NULL, '瑜伽专家');
INSERT INTO `vanx_social_circle_members` VALUES (4014, 1004, 500010234567890132, '跑步达人', '陈飞奔', 'https://vanx-media.oss/avatars/user_500010234567890132.jpg', '普通成员', '已入圈', NULL, 1, '热爱跑步，每天坚持锻炼', 500009234567890131, 500009234567890131, '2023-03-22 07:30:12', '2023-03-22 07:30:12', 0, NULL, NULL, '跑步爱好者');
INSERT INTO `vanx_social_circle_members` VALUES (4015, 1006, 560933616599728128, '代码大师', '程序猿', 'https://vanx-media.oss/avatars/user_560933616599728128.jpg', '圈主', '已入圈', NULL, 1, '10年开发经验，乐于分享技术', 560933616599728128, 560933616599728128, '2023-02-18 10:15:30', '2023-02-18 10:15:30', 0, NULL, NULL, '圈子创建者');
INSERT INTO `vanx_social_circle_members` VALUES (4016, 1006, 500011234567890133, 'Java工程师', '杨小码', 'https://vanx-media.oss/avatars/user_500011234567890133.jpg', '管理员', '已入圈', NULL, 1, 'Java后端开发，熟悉Spring', 560933616599728128, 560933616599728128, '2023-02-19 14:20:40', '2023-02-19 14:20:40', 0, NULL, NULL, 'Java专家');
INSERT INTO `vanx_social_circle_members` VALUES (4017, 1006, 500012234567890134, '前端小姐姐', '李小美', 'https://vanx-media.oss/avatars/user_500012234567890134.jpg', '普通成员', '已入圈', NULL, 1, '前端开发，喜欢Vue和React', 500011234567890133, 500011234567890133, '2023-02-20 16:45:25', '2023-02-20 16:45:25', 0, NULL, NULL, '前端开发者');
INSERT INTO `vanx_social_circle_members` VALUES (4018, 1006, 500013234567890135, '算法小天才', '张小聪', 'https://vanx-media.oss/avatars/user_500013234567890135.jpg', '普通成员', '申请', NULL, 1, '计算机专业学生，喜欢算法', 500013234567890135, 500013234567890135, '2023-02-21 10:30:50', '2023-02-21 10:30:50', 0, NULL, NULL, '待审核学生');
INSERT INTO `vanx_social_circle_members` VALUES (638435902024089600, 638435897729122304, 421054829645824000, NULL, NULL, NULL, '圈主', '已入圈', '主动加入', 1, '圈子创建中，等待审核', 421054829645824000, 421054829645824000, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_platf_category
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_platf_category`;
CREATE TABLE `vanx_social_circle_platf_category`  (
  `social_circle_categ_id` bigint NOT NULL COMMENT 'id',
  `social_circle_categ_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子分类名称',
  `social_circle_categ_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类类目编码',
  `social_circle_categ_level` tinyint NULL DEFAULT NULL COMMENT '类目级别：1、一级类目2、二级类目3、三级类目4、四级类目，目前只支持四级类目',
  `parent_social_circle_categ_id` bigint NULL DEFAULT NULL COMMENT '圈子父级分类id',
  `is_leaf` tinyint(1) NULL DEFAULT NULL COMMENT '是否为叶子节点',
  `is_home_show` tinyint(1) NULL DEFAULT NULL COMMENT '是否前台首页展示',
  `attribute_template_id` bigint NULL DEFAULT NULL COMMENT '圈子类目对应的属性种类模板id号',
  `is_used` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `category_status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '种类状态：1、申请2、通过3、驳回4、关闭',
  `add_source` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子类目来源：1、自定义添加2、从商品平台库选择',
  `reject_reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '分类认证失败的原因',
  `platform_check_user_id` bigint NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`social_circle_categ_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'vanx_platf_item_category' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_social_circle_platf_category
-- ----------------------------
INSERT INTO `vanx_social_circle_platf_category` VALUES (5001, '兴趣爱好', 'HOBBY', 1, 0, 0, 1, NULL, 1, '1', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:00:00', '2023-01-01 10:00:00', 0, NULL, NULL, '兴趣爱好大类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5002, '生活方式', 'LIFESTYLE', 1, 0, 0, 1, NULL, 1, '2', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:00:00', '2023-01-01 10:00:00', 0, NULL, NULL, '生活方式大类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5003, '学习交流', 'LEARNING', 1, 0, 0, 1, NULL, 1, '3', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:00:00', '2023-01-01 10:00:00', 0, NULL, NULL, '学习交流大类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5004, '专业技能', 'PROFESSIONAL', 1, 0, 0, 1, NULL, 1, '4', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:00:00', '2023-01-01 10:00:00', 0, NULL, NULL, '专业技能大类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5011, '摄影摄像', 'HOBBY_PHOTO', 2, 5001, 1, 1, NULL, 1, '11', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:30:00', '2023-01-01 10:30:00', 0, NULL, NULL, '摄影摄像类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5012, '绘画艺术', 'HOBBY_PAINT', 2, 5001, 1, 1, NULL, 1, '12', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:30:00', '2023-01-01 10:30:00', 0, NULL, NULL, '绘画艺术类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5013, '音乐表演', 'HOBBY_MUSIC', 2, 5001, 1, 1, NULL, 1, '13', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:30:00', '2023-01-01 10:30:00', 0, NULL, NULL, '音乐表演类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5014, '手工制作', 'HOBBY_CRAFT', 2, 5001, 1, 1, NULL, 1, '14', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:30:00', '2023-01-01 10:30:00', 0, NULL, NULL, '手工制作类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5015, '游戏娱乐', 'HOBBY_GAME', 2, 5001, 1, 1, NULL, 1, '15', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 10:30:00', '2023-01-01 10:30:00', 0, NULL, NULL, '游戏娱乐类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5021, '健康健身', 'LIFE_FITNESS', 2, 5002, 1, 1, NULL, 1, '21', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '健康健身类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5022, '美食烹饪', 'LIFE_COOKING', 2, 5002, 1, 1, NULL, 1, '22', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '美食烹饪类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5023, '旅行探索', 'LIFE_TRAVEL', 2, 5002, 1, 1, NULL, 1, '23', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '旅行探索类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5024, '时尚穿搭', 'LIFE_FASHION', 2, 5002, 1, 1, NULL, 1, '24', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '时尚穿搭类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5025, '宠物养护', 'LIFE_PETS', 2, 5002, 1, 1, NULL, 1, '25', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '宠物养护类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5026, '园艺种植', 'LIFE_GARDEN', 2, 5002, 1, 1, NULL, 1, '26', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:00:00', '2023-01-01 11:00:00', 0, NULL, NULL, '园艺种植类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5031, '阅读分享', 'LEARN_READING', 2, 5003, 1, 1, NULL, 1, '31', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:30:00', '2023-01-01 11:30:00', 0, NULL, NULL, '阅读分享类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5032, '语言学习', 'LEARN_LANGUAGE', 2, 5003, 1, 1, NULL, 1, '32', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:30:00', '2023-01-01 11:30:00', 0, NULL, NULL, '语言学习类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5033, '教育培训', 'LEARN_EDUCATION', 2, 5003, 1, 1, NULL, 1, '33', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:30:00', '2023-01-01 11:30:00', 0, NULL, NULL, '教育培训类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5034, '育儿心得', 'LEARN_PARENTING', 2, 5003, 1, 1, NULL, 1, '34', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 11:30:00', '2023-01-01 11:30:00', 0, NULL, NULL, '育儿心得类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5041, '编程技术', 'PROF_CODING', 2, 5004, 1, 1, NULL, 1, '41', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 12:00:00', '2023-01-01 12:00:00', 0, NULL, NULL, '编程技术类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5042, '设计创意', 'PROF_DESIGN', 2, 5004, 1, 1, NULL, 1, '42', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 12:00:00', '2023-01-01 12:00:00', 0, NULL, NULL, '设计创意类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5043, '投资理财', 'PROF_FINANCE', 2, 5004, 1, 1, NULL, 1, '43', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 12:00:00', '2023-01-01 12:00:00', 0, NULL, NULL, '投资理财类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5044, '创业分享', 'PROF_STARTUP', 2, 5004, 1, 1, NULL, 1, '44', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 12:00:00', '2023-01-01 12:00:00', 0, NULL, NULL, '创业分享类');
INSERT INTO `vanx_social_circle_platf_category` VALUES (5045, '影视传媒', 'PROF_MEDIA', 2, 5004, 1, 1, NULL, 1, '45', '2', '1、自定义添加', NULL, NULL, NULL, NULL, 1000001, 1000001, '2023-01-01 12:00:00', '2023-01-01 12:00:00', 0, NULL, NULL, '影视传媒类');

-- ----------------------------
-- Table structure for vanx_social_circle_post
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_post`;
CREATE TABLE `vanx_social_circle_post`  (
  `circle_post_id` bigint NOT NULL COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '社交圈子id',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `circle_post_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子名称、标题',
  `circle_post_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '填写的帖子描述',
  `post_cover_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子的封面的路径url，也就是上传帖子中第一张图片时候存储的url',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子的地点',
  `province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在地（省）',
  `city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在地（市）',
  `district_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在地（区）',
  `town_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在镇、街道名称',
  `street_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在地（区）的街道',
  `addrees_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所在位置名称',
  `post_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子的状态1、下架2、上架3、删除',
  `shared_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分享的类型（未分享，分享至朋友圈，分享至周围，分享至全网）',
  `post_size` double(14, 2) NULL DEFAULT NULL COMMENT '私人笔记的尺寸大小',
  `add_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子的来源：自定义添加、平台添加',
  `reject_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类认证失败的原因',
  `platform_check_user_id` bigint NULL DEFAULT NULL COMMENT '审核人id',
  `platform_recheck_user_id` bigint NULL DEFAULT NULL COMMENT '复审人id',
  `check_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`circle_post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户圈子帖子表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_post
-- ----------------------------
INSERT INTO `vanx_social_circle_post` VALUES (700001, 421054829645824000, 638435897729122304, '摄影师小王', '夕阳下的湖光山色', '今天在西湖边拍摄的夕阳美景，使用了偏振镜和渐变镜，捕捉到了完美的光影效果。分享给大家一些风景摄影的小技巧：1.黄金时间拍摄 2.使用三脚架稳定 3.合理构图', 'https://vanx-media.oss/posts/700001/cover.jpg', '杭州西湖', '浙江省', '杭州市', '西湖区', '西湖街道', '湖滨路', '西湖风景区', '2', '分享至朋友圈', 2.50, '自定义添加', NULL, NULL, NULL, NULL, 421054829645824000, 421054829645824000, '2025-07-14 15:30:22', '2025-07-14 15:30:22', 0, NULL, NULL, '精品帖子');
INSERT INTO `vanx_social_circle_post` VALUES (700002, 421054829645824000, 638435897729122304, '摄影师小王', '室内人像摄影布光技巧分享', '分享一些室内人像摄影的布光心得，主要讲解三点布光法：主光、辅助光、背景光的使用。附上实拍样片和布光图解，希望对新手朋友有帮助。', 'https://vanx-media.oss/posts/700002/cover.jpg', '摄影工作室', '北京市', '朝阳区', '三里屯街道', '工体北路', '工体北路甲2号', '专业摄影棚', '2', '分享至周围', 1.80, '自定义添加', NULL, NULL, NULL, NULL, 421054829645824000, 421054829645824000, '2025-07-14 16:45:10', '2025-07-14 16:45:10', 0, NULL, NULL, '技术分享');
INSERT INTO `vanx_social_circle_post` VALUES (700003, 500001234567890123, 638435897729122304, '光影追逐者', '新入手的85mm定焦镜头评测', '刚入手了这支85mm f/1.4的定焦镜头，试拍了一周，分享一下使用感受。优点：成像锐利、虚化效果好、色彩还原准确。缺点：重量较重、价格偏高。总体来说值得推荐！', 'https://vanx-media.oss/posts/700003/cover.jpg', '器材店', '北京市', '海淀区', '中关村街道', '中关村大街', '中关村大街1号', '摄影器材城', '2', '分享至全网', 3.20, '自定义添加', NULL, NULL, NULL, NULL, 500001234567890123, 500001234567890123, '2025-07-14 18:20:35', '2025-07-14 18:20:35', 0, NULL, NULL, '器材评测');
INSERT INTO `vanx_social_circle_post` VALUES (700004, 500002234567890124, 638435897729122304, '镜头下的世界', '新手必学：相机基础设置详解', '很多新手朋友问相机怎么设置，今天详细讲解一下相机的基础设置：光圈、快门、ISO的关系和使用技巧。包含大量实例图片对比，建议收藏学习！', 'https://vanx-media.oss/posts/700004/cover.jpg', '摄影培训中心', '上海市', '黄浦区', '南京东路街道', '南京东路', '南京东路123号', '摄影学院', '2', '分享至朋友圈', 4.10, '自定义添加', NULL, NULL, NULL, NULL, 500002234567890124, 500002234567890124, '2025-07-14 19:15:42', '2025-07-14 19:15:42', 0, NULL, NULL, '教程帖子');
INSERT INTO `vanx_social_circle_post` VALUES (700005, 500003234567890125, 638435897729122304, '风景猎人', '川西高原星空摄影作品集', '上个月去川西拍摄星空，历时5天，终于拍到了满意的银河照片。海拔4000米的高原，条件艰苦但收获满满。分享几张精选作品，以及高原星空拍摄的注意事项。', 'https://vanx-media.oss/posts/700005/cover.jpg', '川西高原', '四川省', '甘孜州', '稻城县', '香格里拉镇', '亚丁村', '亚丁自然保护区', '2', '分享至全网', 5.00, '自定义添加', NULL, NULL, NULL, NULL, 500003234567890125, 500003234567890125, '2025-07-14 20:30:18', '2025-07-14 20:30:18', 0, NULL, NULL, '精品作品');
INSERT INTO `vanx_social_circle_post` VALUES (700006, 421054829645824000, 638435897729122304, '摄影师小王', '本周末圈子外拍活动通知', '各位摄友，本周末（7月20-21日）组织外拍活动，地点：香山公园。主题：秋色人像。活动免费，需要自备器材。有兴趣的朋友请在评论区报名，名额有限！', 'https://vanx-media.oss/posts/700006/cover.jpg', '香山公园', '北京市', '海淀区', '香山街道', '香山路', '香山公园路', '香山风景区', '2', '分享至朋友圈', 1.50, '自定义添加', NULL, NULL, NULL, NULL, 421054829645824000, 421054829645824000, '2025-07-15 09:00:00', '2025-07-15 09:00:00', 0, NULL, NULL, '活动通知');
INSERT INTO `vanx_social_circle_post` VALUES (700007, 500001234567890123, 638435897729122304, '光影追逐者', 'Lightroom调色教程：打造电影感色调', '很多朋友问如何调出电影感的色调，今天分享一个Lightroom的调色流程。主要步骤：1.调整曝光和对比度 2.HSL颜色调整 3.分离色调 4.添加胶片颗粒。附带原图和成片对比。', 'https://vanx-media.oss/posts/700007/cover.jpg', '后期工作室', '北京市', '朝阳区', '三里屯街道', '工体北路', '工体北路甲2号', '数字影像中心', '2', '分享至周围', 3.80, '自定义添加', NULL, NULL, NULL, NULL, 500001234567890123, 500001234567890123, '2025-07-15 10:30:25', '2025-07-15 10:30:25', 0, NULL, NULL, '后期教程');
INSERT INTO `vanx_social_circle_post` VALUES (700008, 500002234567890124, 638435897729122304, '镜头下的世界', '北京胡同街拍纪实', '用了一个下午时间在北京老胡同里街拍，记录了很多有趣的瞬间。街拍的魅力就在于捕捉生活中的真实瞬间，每一张照片都有自己的故事。', 'https://vanx-media.oss/posts/700008/cover.jpg', '南锣鼓巷', '北京市', '东城区', '交道口街道', '南锣鼓巷', '南锣鼓巷胡同', '历史文化街区', '2', '分享至朋友圈', 2.90, '自定义添加', NULL, NULL, NULL, NULL, 500002234567890124, 500002234567890124, '2025-07-15 14:20:15', '2025-07-15 14:20:15', 0, NULL, NULL, '街拍作品');
INSERT INTO `vanx_social_circle_post` VALUES (639242823299792896, 421054829645824000, 638435897729122304, NULL, '分享一张美丽的风景照', '今天在公园拍到的美丽夕阳，分享给大家', 'https://example.com/images/cover.jpg', '北京市朝阳区', '北京市', '北京市', '朝阳区', '三里屯街道', '工体北路', '三里屯太古里', '上架', '分享至朋友圈', NULL, '自定义添加', NULL, NULL, NULL, NULL, 421054829645824000, NULL, '2025-07-16 15:08:38', '2025-07-16 15:08:38', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_post_categ_relat
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_post_categ_relat`;
CREATE TABLE `vanx_social_circle_post_categ_relat`  (
  `circle_post_categ_relat_id` bigint NOT NULL COMMENT 'id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子id',
  `post_categ_id` bigint NULL DEFAULT NULL COMMENT '圈子对应的分类的id',
  `post_categ_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '圈子对应的分类名称',
  `post_categ_level` tinyint NULL DEFAULT NULL COMMENT '圈子分类级别',
  `post_categ_crit_id` bigint NULL DEFAULT NULL COMMENT '圈子分类的标准的ID，如：对于视频作品来说，其分类可以有多方面，比如：内容分类；题材分类；长短分类；。。。。',
  `sort_number` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '排序指数，越小越靠前',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`circle_post_categ_relat_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'vanx_media_categ_relat' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of vanx_social_circle_post_categ_relat
-- ----------------------------
INSERT INTO `vanx_social_circle_post_categ_relat` VALUES (638435902024089619, 638435897729122304, 1001, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_post_categ_relat` VALUES (638435902024089620, 638435897729122304, 1002, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_post_categ_relat` VALUES (638435902024089621, 638435897729122304, 1003, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 10:57:22', '2025-07-14 10:57:22', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_post_categ_relat` VALUES (639242823299792898, 638435897729122304, 1001, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-16 15:08:38', '2025-07-16 15:08:38', 0, NULL, NULL, NULL);
INSERT INTO `vanx_social_circle_post_categ_relat` VALUES (639242823299792899, 638435897729122304, 1002, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-16 15:08:38', '2025-07-16 15:08:38', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_post_fast
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_post_fast`;
CREATE TABLE `vanx_social_circle_post_fast`  (
  `circle_post_fast_id` bigint NOT NULL COMMENT 'id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '该用户的id',
  `user_nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `shop_id` bigint NULL DEFAULT NULL COMMENT '该用户商铺的id',
  `shop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '该用户商铺的昵称',
  `circle_post_id` bigint NULL DEFAULT NULL COMMENT '该圈子的帖子的id',
  `circle_post_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子名称、标题',
  `post_cover_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '帖子的封面的路径url，也就是上传帖子中第一张图片存储的url',
  `recommend_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '推荐指数',
  `num_of_likes` tinyint NULL DEFAULT NULL COMMENT '帖子的点赞数量',
  `num_of_collect` tinyint NULL DEFAULT NULL COMMENT '帖子的收藏数量',
  `num_of_comments` tinyint NULL DEFAULT NULL COMMENT '帖子的评论数量',
  `num_of_played` tinyint NULL DEFAULT NULL COMMENT '帖子的阅读数量',
  `num_of_shared` tinyint NULL DEFAULT NULL COMMENT '帖子的被分享数量',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`circle_post_fast_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户圈子的帖子的快表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_post_fast
-- ----------------------------
INSERT INTO `vanx_social_circle_post_fast` VALUES (639242823299792897, 421054829645824000, NULL, NULL, NULL, 639242823299792896, '分享一张美丽的风景照', 'https://example.com/images/cover.jpg', '0', 0, 0, 0, 0, 0, 421054829645824000, NULL, '2025-07-16 15:08:38', '2025-07-16 15:08:38', 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for vanx_social_circle_post_media
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_post_media`;
CREATE TABLE `vanx_social_circle_post_media`  (
  `post_media_id` bigint NOT NULL COMMENT 'id',
  `circle_post_id` bigint NULL DEFAULT NULL COMMENT '该圈子帖子的id',
  `media_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片、视频路径url',
  `media_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片、视频名称',
  `media_cover_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '如果是现成视频，则存放封面的url',
  `media_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片、视频类型',
  `sort_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '排序指数越小越靠前',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_media_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户的圈子帖子图片、视频表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_post_media
-- ----------------------------

-- ----------------------------
-- Table structure for vanx_social_circle_punishments
-- ----------------------------
DROP TABLE IF EXISTS `vanx_social_circle_punishments`;
CREATE TABLE `vanx_social_circle_punishments`  (
  `circle_punishments_id` bigint NOT NULL COMMENT 'id',
  `social_circle_id` bigint NULL DEFAULT NULL COMMENT '圈子id',
  `punishment_user_id` bigint NULL DEFAULT NULL COMMENT '被处罚的用户id',
  `punishment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处罚的类型：警告、禁言、封号、限制权限等',
  `punishment_start_date` datetime NULL DEFAULT NULL COMMENT '处罚的开始时间',
  `punishment_end_date` datetime NULL DEFAULT NULL COMMENT '处罚的结束时间',
  `punishment_duration` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处罚持续时间：禁言24小时',
  `punishment_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处罚状态：进行中、已完成',
  `check_user_id` bigint NULL DEFAULT NULL COMMENT '进行处罚的管理员或用户id',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者用户id',
  `editor_id` bigint NULL DEFAULT NULL COMMENT '修改者用户id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除',
  `other_info_one` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息1',
  `other_info_two` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其他信息2',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`circle_punishments_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '圈子处罚记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vanx_social_circle_punishments
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
