package zx.vanx.content_recommend.rabbimq.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.content_recommend.service.FirLevMediaOneService;
import zx.vanx.content_recommend.service.FirLevMediaTwoService;
import zx.vanx.content_recommend.service.PoolContDbRelatService;
import zx.vanx.content_recommend.service.TrafPoolInfoService;

@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ContentRecommendListenQueue {

    @Autowired
    private TrafPoolInfoService trafPoolInfoService;

    @Autowired
    private PoolContDbRelatService poolContDbRelatService;

    @Autowired
    private FirLevMediaOneService firLevMediaOneService;

    @Autowired
    private FirLevMediaTwoService firLevMediaTwoService;


    /**
     * 添加视频推荐信息
     * @param addMediaRecommendInfo 视频推荐信息
     * @param message 消息
     * @param channel 通道
     */
//    @SneakyThrows
//    @RabbitListener(bindings =
//    @QueueBinding(
//            exchange = @Exchange(value = MqConst.EXCHANGE_DIRECT_MEDIA_RECOMMEND_ADD,autoDelete = "false"),
//            key = {MqConst.ROUTING_MEDIA_RECOMMEND_ADD},
//            value = @Queue(
//                    value = MqConst.QUEUE_MEDIA_RECOMMEND_ADD,durable = "true",autoDelete = "false",
//                    arguments = {@Argument(name = "x-queue-mode", value = "lazy")
//
//                    })
//    ))
//    @Idempotent
//    @Transactional
//    public void addMediaRecommendInfo(AddMediaRecommendInfo addMediaRecommendInfo, Message message, Channel channel) {
//
//        // 1 查询视频对应的一级流量池
//        TrafPoolInfo trafPoolInfo = trafPoolInfoService.queryByPoolNameAndContentType("一级视频流量池", "视频");
//
//        Long contentFirstCategId = addMediaRecommendInfo.getCreationFirstCid();
//        // 如果视频没有分类，便是查询分类为其他
//        if (ObjectUtils.isEmpty(addMediaRecommendInfo.getCreationFirstCid())) {
//            contentFirstCategId = 0L;
//        }
//
//        // 2 查询该视频分类与一级流量池对应关系表查询数据需要添加到哪个流量池内容表中
//        PoolContDbRelat poolContDbRelat = poolContDbRelatService.selectOneByTrafficPoolIdAndcontentCateg(trafPoolInfo.getTrafPoolId(), contentFirstCategId);
//
//        if (ObjectUtils.isEmpty(poolContDbRelat)) {
//            throw new ZxException(204,addMediaRecommendInfo.getCreationFirstCname() + "该分类没有配置对应的流量池内容表");
//        }
//
//        // 数据库表名
//        String tableName = poolContDbRelat.getRelatDbTableName();
//
//        // 3. 添加视频推荐信息到对应的流量池内容表中
//
//        // 如果是一级流量池内容表1
//        if ("vanx_fir_lev_media_one".equals(tableName)) {
//            FirLevMediaOne firLevMediaOne = new FirLevMediaOne();
//            firLevMediaOne.setTrafPoolId(trafPoolInfo.getTrafPoolId());
//            firLevMediaOne.setCreationFirstCid(addMediaRecommendInfo.getCreationFirstCid());
//            firLevMediaOne.setCreationSecCid(addMediaRecommendInfo.getCreationSecCid());
//            firLevMediaOne.setContentType("视频");
//            firLevMediaOne.setContentCateg(addMediaRecommendInfo.getCreationFirstCname());
//            firLevMediaOne.setContentId(addMediaRecommendInfo.getMediaId());
//            firLevMediaOneService.save(firLevMediaOne);
//        }
//
//        // 如果是一级流量池内容表2
//        if ("vanx_fir_lev_media_two".equals(tableName)) {
//            FirLevMediaTwo firLevMediaTwo = new FirLevMediaTwo();
//            firLevMediaTwo.setTrafPoolId(trafPoolInfo.getTrafPoolId());
//            firLevMediaTwo.setCreationFirstCid(addMediaRecommendInfo.getCreationFirstCid());
//            firLevMediaTwo.setCreationSecCid(addMediaRecommendInfo.getCreationSecCid());
//            firLevMediaTwo.setContentType("视频");
//            firLevMediaTwo.setContentCateg(addMediaRecommendInfo.getCreationFirstCname());
//            firLevMediaTwo.setContentId(addMediaRecommendInfo.getMediaId());
//            firLevMediaTwoService.save(firLevMediaTwo);
//        }
//
//        System.out.println("成功处理消息"+ addMediaRecommendInfo + "---------------------------------");
//    }

}
