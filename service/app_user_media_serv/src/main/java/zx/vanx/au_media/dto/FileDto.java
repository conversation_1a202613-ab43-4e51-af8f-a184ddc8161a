// FileDto.java
package zx.vanx.au_media.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 文件上传对象
 */
@Data
public class FileDto {

    @ApiModelProperty(value = "资源id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "电子资源id不能为空")
    private Long elecResourceId;

    @ApiModelProperty(value = "添加资源的用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "资源用户id不能为空")
    private Long resourceUserId;

    @ApiModelProperty(value = "文件的md5")
    @NotEmpty(message = "文件MD5不能为空")
    private String fileMd5;

    @ApiModelProperty(value = "上传会话id")
    @NotEmpty(message = "上传会话id不能为空")
    private String uploadSessionId;

    @ApiModelProperty(value = "分片数量")
    private Integer chunkCount;

    @ApiModelProperty(value = "分片大小")
    private Integer chunkSize;

    @ApiModelProperty(value = "电子资源文件的大小")
    private Double fileSize;

    @ApiModelProperty(value = "选中文件时获取的文件名")
    @NotEmpty(message = "文件名不能为空")
    private String fileMinioName;

}