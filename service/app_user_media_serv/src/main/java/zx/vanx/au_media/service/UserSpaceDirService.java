package zx.vanx.au_media.service;

import zx.vanx.au_media.dto.UserSpaceDirDto;
import zx.vanx.au_media.entity.UserSpaceDir;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface UserSpaceDirService extends IService<UserSpaceDir> {


    /**
     * 删除-用户空间目录 及其数据
     * @param spaceDirId 用户空间目录id
     */
    void removeSpaceDirById(Long spaceDirId);

    /**
     * 查询我的顶层目录列表
     * @return 目录列表
     */
    UserSpaceDir queryMyTopDirectory(Long userId);

    /**
     * 查询我的目录列表
     * @param parentSpaceDirId 父目录id
     * @param currentUserId 当前用户id
     * @return 目录列表
     */
    List<UserSpaceDir> queryMyDirectoryList(Long parentSpaceDirId, Long currentUserId);

    /**
     * 添加-用户空间目录
     * @param userSpaceDir
     */
    boolean addSpaceDir(UserSpaceDir userSpaceDir);

    /**
     * 添加-用户空间目录
     * @param userSpaceDirDto
     */
    boolean addSpaceDir(UserSpaceDirDto userSpaceDirDto);

    /**
     * 用户目录返回
     * @param userSpaceDir 用户目录
     */
    UserSpaceDir returnSpaceDir(UserSpaceDir userSpaceDir);
}
