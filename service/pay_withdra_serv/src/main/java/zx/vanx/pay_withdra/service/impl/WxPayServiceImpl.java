//package zx.vanx.pay_withdra.service.impl;
//
//import com.wechat.pay.java.service.payments.app.AppServiceExtension;
//import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
//import com.wechat.pay.java.service.payments.app.model.PrepayWithRequestPaymentResponse;
//import org.springframework.stereotype.Service;
//import zx.vanx.common_util.utils.BeanUtil;
//import zx.vanx.pay_withdra.config.WechatPayConfig;
//import zx.vanx.pay_withdra.dto.OderInfoDto;
//import zx.vanx.pay_withdra.service.WxPayService;
//
//import javax.annotation.Resource;
//
//@Service
//public class WxPayServiceImpl implements WxPayService {
//
//    @Resource
//    private WechatPayConfig wechatPayConfig;
//
//    @Resource
//    private AppServiceExtension appService;
//
//    /**
//     * 微信app下单-预支付
//     * @param oderInfoDto 订单信息
//     * @return 预支付结果
//     */
//    @Override
//    public PrepayWithRequestPaymentResponse appPrepayWithRequestPayment(OderInfoDto oderInfoDto) {
//
//        //组装下单必填参数
//        PrepayRequest request = BeanUtil.copyProperties(oderInfoDto, PrepayRequest::new);
//        request.setAppid(wechatPayConfig.getAppId());
//        request.setMchid(wechatPayConfig.getMchId());
//        request.setNotifyUrl(wechatPayConfig.getNotifyUrl());
//
//        return appService.prepayWithRequestPayment(request);
//    }
//}
