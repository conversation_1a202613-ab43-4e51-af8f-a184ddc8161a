package zx.vanx.pay_withdra.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class IncomeAndExpenditureItem implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用 户账户余额的收支类型，即：收入、支出")
    private String changeType;

    @ApiModelProperty(value = "用户账户余额业务类型：提现、转账、退款、交易下单、充值、消费虚拟币、购买礼物")
    private String transacObjType;

    @ApiModelProperty(value = "用户账户余额的交易金额")
    private BigDecimal transacObjAmount;

    @ApiModelProperty(value = "用户当前剩余余额")
    private BigDecimal lastRemainAmount;

    @ApiModelProperty(value = "用户账户余额交易发生日期、时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date transacOccurTime;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

}
