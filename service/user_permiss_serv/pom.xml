<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>service</artifactId>
        <groupId>com.zx</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>user_permiss_serv</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>common-security</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>common-satoken</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>minio_util</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>zx_vanx_job_recruit_serv_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--自己服务接口及entity-->
        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>user_permiss_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>zx_vanx_market_manage_serv_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>zx_vanx_app_user_chat_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>zx_vanx_app_user_contacts_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--app_user_media_client服务远程调用接口-->
        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>app_user_media_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--app_sys_media_client服务远程调用接口-->
        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>app_sys_media_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--social_circle服务远程调用接口-->
        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>social_circle_api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>rabbit_util</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--短信验证码功能-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>2.0.13</version>
        </dependency>

        <!--腾讯云短信验证码功能-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
            <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询所有版本，最新版本如下 -->
            <version>3.1.969</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.zx</groupId>
            <artifactId>common-justauth</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>



    </dependencies>

    <build>
        <finalName>user_permission</finalName>
    </build>
</project>
