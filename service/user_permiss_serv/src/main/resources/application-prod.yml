server:
  port: 9081
spring:
  application:
    name: user-permiss

--- # nacos
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        ip: **************
        port: 9081
        # 心跳配置
        heart-beat-interval: 5000      # 改为5秒，更频繁的心跳
        heart-beat-timeout: 15000      # 改为15秒，更快感知服务状态
        ip-delete-timeout: 30000
        # 服务注册配置
        register-enabled: true
        cluster-name: DEFAULT
        group: DEFAULT_GROUP
        namespace: public
        # 连接稳定性配置
        naming-load-cache-at-start: true   # 改为true，启动时预加载
        watch-delay: 5000              # 改为5秒，更快发现服务变化
  # 数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************
    password: AsWPoL236
    username: root
#设置远程调用链接超时时间和读取超时时间
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 20000
--- # redis
spring:
  redis:
    host: **************
    port: 6379
    password: zx2023
    database: 1
    timeout: 1800000
--- # rabbitmq
spring:
  rabbitmq:
    template:
      retry:
        enabled: true # 开启重试功能
        initial-interval: 100ms # 第一次重试间隔
        max-attempts: 10 # 最大重试次数
        multiplier: 2 # 间隔倍数

    host: **************
    port: 5672
    username: zx-vanx
    password: zx-vanx123
    virtual-host: /zx-vanx # 虚拟主机

    connection-timeout: 1s # 连接超时时间
    publisher-returns: true # 发布确认
    publisher-confirm-type: correlated # 发布确认类型
    listener:
      simple:
        acknowledge-mode: manual # 手动确认
        prefetch: 3 # 预取消息数量
        retry:
          enabled: true # 开启重试功能
          initial-interval: 100ms # 第一次重试间隔
          max-attempts: 10 # 最大重试次数
          multiplier: 2 # 间隔倍数
