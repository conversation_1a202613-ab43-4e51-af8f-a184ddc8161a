<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PlatfPoinsRightsRulesMapper">

    <select id="selectTotalRuleObjectValue" resultType="Long">

        SELECT SUM(rule_object_value)
        FROM vanx_platf_poins_rights_rules
        WHERE
        (
        (user_behavior_type = #{userBehaviorType} AND  level_info_id = #{growthValueLevelId})
        OR (user_behavior_type = #{userBehaviorType} AND level_info_id = #{userShoppingPointsLevelId})
        OR (user_behavior_type = #{userBehaviorType} AND level_info_id = #{userGamePointsLevelId})
        )
        AND is_deleted = 0
        GROUP BY user_behavior_type
    </select>


    <select id="selectUserBehaviorList" resultType="string">
        SELECT DISTINCT user_behavior_type
        FROM vanx_platf_poins_rights_rules
        WHERE is_deleted = 0
        AND user_behavior_type NOT IN (
        SELECT DISTINCT user_behavior_type
        FROM vanx_platf_poins_rights_rules
        WHERE
        user_level_name = (SELECT user_level_name
                           FROM vanx_platf_user_level_info
                           WHERE level_info_id = #{levelInfoId} )
                           AND
                           is_deleted = 0
        );
    </select>

    <select id="selecUserRoleNameList" resultType="string">
        SELECT DISTINCT rule_name
        FROM vanx_platf_poins_rights_rules
        WHERE is_deleted = 0
          AND rule_name NOT IN (
            SELECT DISTINCT rule_name
            FROM vanx_platf_poins_rights_rules
            WHERE
                    user_level_name = (SELECT user_level_name
                                       FROM vanx_platf_user_level_info
                                       WHERE level_info_id = #{levelInfoId} )
              AND
                is_deleted = 0
        );
    </select>

    <select id="selectRuleObjectTypeList" resultType="string">
        SELECT DISTINCT rule_object_type
        FROM vanx_platf_poins_rights_rules
        WHERE is_deleted = 0;
    </select>


    <!--查询用户积分类型，通过用户行为类型-->
    <select id="selectRuleObjectTypeByUserBehaviorType" resultType="java.lang.String">
        SELECT DISTINCT rule_object_type
        FROM vanx_platf_poins_rights_rules
        WHERE user_behavior_type = #{userBehaviorType} and is_deleted = 0;
    </select>

    <!--通过用户行为类型和等级id查询 PlatformPoinsRules-->
    <select id="selectOneByUserBehaviorTypeAndLevelInfoId" resultType="zx.vanx.user_permiss.entity.PlatfPoinsRightsRules">
        SELECT *
        FROM vanx_platf_poins_rights_rules
        WHERE user_behavior_type = #{userBehaviorType}
          AND level_info_id = #{levelInfoId}
          AND rule_object_type is not null
          AND is_deleted = 0;
    </select>

    <!--查询权益列表-->
    <select id="selectRightsNameList" resultType="java.lang.String">
        select distinct rule_name from `vanx_platf_poins_rights_rules` where is_deleted = 0;
    </select>

    <select id="selectUserSystemRight" resultType="zx.vanx.user_permiss.vo.UserSystemRightsVo">
        select
            vpr.rule_name,
            MAX(vpr.rule_obj_value_time_unit) as rule_obj_value_time_unit,
            SUM(vpr.rule_object_value) as total_rule_object_value,
            MAX(vpr.rule_object_value_unit) as rule_object_value_unit,
            SUM(vpr.rule_object_get_points) as total_rule_object_get_points,
            MAX(vpr.rule_object_type) as rule_object_type,
            MAX(vpr.rule_category_id) as rule_category_id,
            MAX(vpr.rule_category_name) as rule_category_name,
            MAX(vpr.rule_category_url) as rule_category_url,
            MAX(vpr.user_consume_type) as user_consume_type,
            MAX(vpr.value_change_type) as value_change_type
        from
            vanx_platf_poins_rights_rules vpr
                join
            (
                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_behavior_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '行为等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_shopping_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '购物等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_game_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '游戏等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.growth_value between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '成长值等级'
                  and uf.is_deleted = 0
            ) as user_levels
            on vpr.level_info_id = user_levels.level_info_id
        where vpr.user_behavior_type = #{userBehaviorType}
            and vpr.is_deleted = 0
        group by
            vpr.rule_name;
    </select>

    <select id="selectUserSystemRights" resultType="zx.vanx.user_permiss.vo.UserSystemRightsVo">
        select
            vpr.rule_name,
            MAX(vpr.rule_obj_value_time_unit) as rule_obj_value_time_unit,
            SUM(vpr.rule_object_value) as total_rule_object_value,
            MAX(vpr.rule_object_value_unit) as rule_object_value_unit,
            SUM(vpr.rule_object_get_points) as total_rule_object_get_points,
            MAX(vpr.rule_object_type) as rule_object_type,
            MAX(vpr.rule_category_id) as rule_category_id,
            MAX(vpr.rule_category_name) as rule_category_name,
            MAX(vpr.rule_category_url) as rule_category_url,
            MAX(vpr.user_consume_type) as user_consume_type,
            MAX(vpr.value_change_type) as value_change_type
        from
            vanx_platf_poins_rights_rules vpr
                join
            (
                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_behavior_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '行为等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_shopping_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '购物等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_game_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '游戏等级'
                  and uf.is_deleted = 0

                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.growth_value between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '成长值等级'
                  and uf.is_deleted = 0
            ) as user_levels
            on vpr.level_info_id = user_levels.level_info_id
                where vpr.is_deleted = 0

        group by
            vpr.rule_name;
    </select>

    <!--查询用户系统指定分类的权益列表-->
    <select id="selectUserSystemCategRights" resultType="zx.vanx.user_permiss.vo.UserSystemRightsVo">
        select
            vpr.rule_name,
            MAX(vpr.rule_obj_value_time_unit) as rule_obj_value_time_unit,
            SUM(vpr.rule_object_value) as total_rule_object_value,
            MAX(vpr.rule_object_value_unit) as rule_object_value_unit,
            SUM(vpr.rule_object_get_points) as total_rule_object_get_points,
            MAX(vpr.rule_object_type) as rule_object_type,
            MAX(vpr.rule_category_id) as rule_category_id,
            MAX(vpr.rule_category_name) as rule_category_name,
            MAX(vpr.rule_category_url) as rule_category_url,
            MAX(vpr.user_consume_type) as user_consume_type,
            MAX(vpr.value_change_type) as value_change_type
        from
            vanx_platf_poins_rights_rules vpr
                join
            (
                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_behavior_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  AND vl.user_level_type = '行为等级'
                  and uf.is_deleted = 0
                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_shopping_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  AND vl.user_level_type = '购物等级'
                  and uf.is_deleted = 0
                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.user_game_points between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  AND vl.user_level_type = '游戏等级'
                  and uf.is_deleted = 0
                union

                select vl.level_info_id
                from user_info_fast uf
                         join vanx_platf_user_level_info vl
                              on uf.growth_value between vl.required_min_poins and vl.required_max_poins
                where uf.user_id = #{userId}
                  and vl.user_level_type = '成长值等级'
                  and uf.is_deleted = 0
            ) as user_levels
            on vpr.level_info_id = user_levels.level_info_id

        where vpr.rule_category_id = #{ruleCategoryId}
          and vpr.is_deleted = 0
        group by
            vpr.rule_name;
    </select>
</mapper>
