<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserCompanyVerifyInfoMapper">

    <!--根据认证来源查询最新认证信息-->
    <select id="selectLatestByAddSource" resultType="zx.vanx.user_permiss.entity.UserCompanyVerifyInfo">

        SELECT
            user_com_verify_id,
            user_id,
            linkman,
            linkman_phone,
            id_pic_front_url,
            id_pic_front_minio_name,
            id_pic_back_url,
            id_pic_back_minio_name,
            business_pic_url,
            business_pic_minio_name,
            address_province,
            address_city,
            address_district,
            full_address,
            artificial_person_status,
            reject_reason,
            platform_id,
            platform_user_id,
            pass_time,
            add_source,
            id_type,
            creator_id,
            editor_id,
            created_time,
            modified_time,
            is_deleted,
            other_info_one,
            other_info_two,
            remark
        FROM (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_time DESC) AS rn
                 FROM
                     user_company_verify_info
                 WHERE
                     add_source = #{addSource} and is_deleted = 0
                 ORDER BY user_com_verify_id DESC
             ) AS subquery
        WHERE
            rn = 1
    </select>

    <!--根据认证来源和是否为人工审核查询最新认证信息-->
    <select id="selectLatestByAddSourceAndStatus" resultType="zx.vanx.user_permiss.entity.UserCompanyVerifyInfo">
        SELECT
            user_com_verify_id,
            user_id,
            linkman,
            linkman_phone,
            id_pic_front_url,
            id_pic_front_minio_name,
            id_pic_back_url,
            id_pic_back_minio_name,
            business_pic_url,
            business_pic_minio_name,
            address_province,
            address_city,
            address_district,
            full_address,
            artificial_person_status,
            reject_reason,
            platform_id,
            platform_user_id,
            pass_time,
            add_source,
            id_type,
            creator_id,
            editor_id,
            created_time,
            modified_time,
            is_deleted,
            other_info_one,
            other_info_two,
            remark
        FROM (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_time DESC) AS rn
                 FROM
                     user_company_verify_info
                 WHERE
                     add_source = #{addSource} and artificial_person_status = #{artificialPersonStatus} and is_deleted = 0
                 ORDER BY user_com_verify_id DESC
             ) AS subquery
        WHERE
            rn = 1


    </select>
</mapper>
