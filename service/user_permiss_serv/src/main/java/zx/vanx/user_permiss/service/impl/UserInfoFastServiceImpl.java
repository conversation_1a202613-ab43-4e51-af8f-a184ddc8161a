package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.mapper.UserInfoFastMapper;
import zx.vanx.user_permiss.service.UserInfoFastService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserInfoFastServiceImpl extends ServiceImpl<UserInfoFastMapper, UserInfoFast> implements UserInfoFastService {

}
