package zx.vanx.user_permiss.timer;

import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.model.RedisCorrelationData;
import zx.vanx.redis.key.RabbitmqKey;
import zx.vanx.service.RabbitService;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.mapper.UserInfoFastMapper;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.service.UserInfoService;
import zx.vanx.user_permiss.service.UserService;

import java.util.List;
/**
 * author
 * Date: 2023/8/813:33
 **/
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class UserPermissTimer {

    private final RedisCache redisCache;

    private final RabbitService rabbitService;

    private final UserInfoFastMapper userInfoFastMapper;

    private final UserService userService;

    private final UserInfoService userInfoService;


    /**
     * 定时同步userinfoFast的redis数据到mysql中
     */
//    @Scheduled(cron = "0 0 */2 * * *")  //2小时
    @Scheduled(cron = "0/10 * * * * *")
    public void reUserInfoFast() {
        List<String> keys = redisCache.scanKeysWithPrefix(PermissKey.USER_FAST_INFO);

        for (String key : keys) {
            UserInfoFast userInfoFast = redisCache.getCacheObject(key);
            userInfoFastMapper.updateById(userInfoFast);
        }

        System.out.println("数据同步一次");
    }

    /**
     * 定时重发rabbitmq发送失败的错误消息
     */
    @Scheduled(cron = "0/30 * * * * *") // 30秒
    public void reRabbitMQMessage() {
        List<String> keys = redisCache.scanKeysWithPrefix(RabbitmqKey.RABBIT_MQ_MESSAGE);

        for (String key : keys) {
            RedisCorrelationData redisCorrelationData = redisCache.getCacheObject(key);
            if(ObjectUtils.isEmpty(redisCorrelationData)){
                return;
            }
            // 消息发送时重试3次未发送成功，消息死亡，或者消息消费时超过3次未消费成功。重新发送消息
            if (redisCorrelationData.isAreadlyDead()) {
                rabbitService.reSendRabbitMQMessage(redisCorrelationData);
                System.out.println("重发错误消息" + redisCorrelationData);
            }
        }

        System.out.println("redis错误数据处理一次");
    }


    /**
     * 定时更新广告弹出内容
     */
//    @Scheduled(cron = "0/10 * * * * *") // 10秒
    @Scheduled(cron = "0 0 */2 * * *")  // 2小时
    public void reAdvertisingRecordForm() {

        // 获取所有用户信息
        List<UserInfo> userInfoList = userInfoService.list();

        for (UserInfo userInfo : userInfoList) {
            // 更新广告弹出记录
            userService.reAdPopUpRecord(userInfo.getUserId());
        }

        System.out.println("广告记录刷新一次");
    }

}
