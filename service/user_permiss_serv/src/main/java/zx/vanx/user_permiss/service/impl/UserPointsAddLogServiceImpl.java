package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;
import zx.vanx.user_permiss.entity.PlatfPointsCategory;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.entity.UserPointsAddLog;
import zx.vanx.user_permiss.enums.UserLevelType;
import zx.vanx.user_permiss.mapper.PlatfPoinsRightsRulesMapper;
import zx.vanx.user_permiss.mapper.PlatfUserLevelInfoMapper;
import zx.vanx.user_permiss.mapper.UserPointsAddLogMapper;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.request.NoSystemPointsAddRequest;
import zx.vanx.user_permiss.request.SystemPointsAddRequest;
import zx.vanx.user_permiss.service.GrowthValueWeightService;
import zx.vanx.user_permiss.service.PlatfPointsCategoryService;
import zx.vanx.user_permiss.service.UserPointsAddLogService;
import zx.vanx.user_permiss.vo.DynamicPointsVo;
import zx.vanx.user_permiss.vo.UserSystemRightsVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserPointsAddLogServiceImpl extends ServiceImpl<UserPointsAddLogMapper, UserPointsAddLog> implements UserPointsAddLogService {

    private final PlatfPoinsRightsRulesMapper platformPoinsRulesMapper;

    private final UserPointsAddLogMapper userPointsAddLogMapper;

    private final PlatfUserLevelInfoMapper userLevelInfoMapper;

    private final RedisCache redisCache;

    private final GrowthValueWeightService growthValueWeightService;

    private final PlatfPointsCategoryService platfPointsCategoryService;

    private final PlatfPoinsRightsRulesMapper platfPoinsRightsRulesMapper;

    /**
     * 用户加积分
     * @param systemPointsAddRequest 系统加积分请求对象
     * @param userId 用户id
     */
    @Override
    public void systemPointsGain(SystemPointsAddRequest systemPointsAddRequest, Long userId) {

        // 设置用户id
        systemPointsAddRequest.setUserId(userId);

        UserInfoFast userInfoFast = redisCache.getCacheObject(PermissKey.USER_FAST_INFO + userId);

        // 更新用户系统配置获得积分的成长信息
        UserInfoFast updatedUserInfoFast = updateUserSystemPointsGainGrowth(userInfoFast, systemPointsAddRequest);

        // 一次性更新Redis缓存
        redisCache.setCacheObject(PermissKey.USER_FAST_INFO + userId, updatedUserInfoFast);
    }

    /**
     * 非系统配置行为积分-增益
     * @param noSystemPointsAddRequest 非系统配置行为积分对象
     * @param userId 用户id
     */
    @Override
    public void noSystemPointsGain(NoSystemPointsAddRequest noSystemPointsAddRequest, Long userId) {

        UserInfoFast userInfoFast = redisCache.getCacheObject(PermissKey.USER_FAST_INFO + userId);
        // 更新用户的非系统配置获得的成长信息
        UserInfoFast updatedUserInfoFast = updateUserNoSystemPointsGainGrowth(userInfoFast, noSystemPointsAddRequest);

        // 一次性更新Redis缓存
        redisCache.setCacheObject(PermissKey.USER_FAST_INFO + userId, updatedUserInfoFast);

    }

    /**
     * 查询用户收入的动态积分记录list
     * @param userId 用户id
     * @param recentDateList 最近日期列表
     * @return 用户收入的动态积分记录list
     */
    @Override
    public List<DynamicPointsVo> selectUserRecentPointsList(Long userId, List<String> recentDateList) {

        PlatfPointsCategory behaviorPoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("行为积分");
        PlatfPointsCategory shoppingPoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("购物积分");
        PlatfPointsCategory gamePoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("游戏积分");

        QueryWrapper<UserPointsAddLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.in("created_date", recentDateList);
        queryWrapper.orderByDesc("created_date");
        List<UserPointsAddLog> userPointsAddLogList = this.list(queryWrapper);

        List<DynamicPointsVo> dynamicPointsVoList = BeanUtil.copyList(userPointsAddLogList, DynamicPointsVo.class);

        if (!CollectionUtils.isEmpty(dynamicPointsVoList)) {

            for (DynamicPointsVo dynamicPointsVo : dynamicPointsVoList) {
                if (dynamicPointsVo.getUserPointsType().equals("行为积分")) {
                    dynamicPointsVo.setUserPointsUrl(behaviorPoints.getCategPicUrl());
                }

                if (dynamicPointsVo.getUserPointsType().equals("购物积分")) {
                    dynamicPointsVo.setUserPointsUrl(shoppingPoints.getCategPicUrl());
                }

                if (dynamicPointsVo.getUserPointsType().equals("游戏积分")) {
                    dynamicPointsVo.setUserPointsUrl(gamePoints.getCategPicUrl());
                }

            }
        }

        return dynamicPointsVoList;
    }

    /**
     * 更新用户的非系统配置获得的成长信息
     * @param userInfoFast 用户信息
     * @param noSystemPointsAddRequest 非系统配置行为积分对象
     * @return 更新后的用户信息
     */
    private UserInfoFast updateUserNoSystemPointsGainGrowth(UserInfoFast userInfoFast, NoSystemPointsAddRequest noSystemPointsAddRequest) {

        // 记录用户非系统配置获得积分加分日志
        recordUserNoSystemPointsGainLog(noSystemPointsAddRequest);

        // 更新用户非系统配置获得积分后的用户基本信息
        UserInfoFast updateUserPoints = updateUserNoSystemGainPoints(userInfoFast, noSystemPointsAddRequest);

        // 调用方法更新用户等级信息
        return growthValueWeightService.addUserGrowthValueByGrowthValueWeight(updateUserPoints);
    }

    /**
     * 更新用户非系统配置获得积分后的用户基本信息
     * @param userInfoFast 用户信息
     * @param noSystemPointsAddRequest 非系统配置行为积分对象
     * @return 更新后的用户信息
     */
    private UserInfoFast updateUserNoSystemGainPoints(UserInfoFast userInfoFast, NoSystemPointsAddRequest noSystemPointsAddRequest) {

        if (!ObjectUtils.isEmpty(noSystemPointsAddRequest.getUserPointsType())) {

            // 更新用户信息
            if (noSystemPointsAddRequest.getUserPointsType().equals("行为积分")) {

                userInfoFast.setDynamicBehaviorPoints(userInfoFast.getDynamicBehaviorPoints() + noSystemPointsAddRequest.getUserPoints()); // 更新用户动态行为积分

            }
            if (noSystemPointsAddRequest.getUserPointsType().equals("游戏积分")) {

                userInfoFast.setDynamicGamePoints(userInfoFast.getDynamicGamePoints() + noSystemPointsAddRequest.getUserPoints()); // 更新用户动态游戏积分

            }
            if (noSystemPointsAddRequest.getUserPointsType().equals("购物积分")) {

                userInfoFast.setDynamicShoppingPoints(userInfoFast.getDynamicShoppingPoints() + noSystemPointsAddRequest.getUserPoints()); // 更新用户动态购物积分

            }
        }

        return userInfoFast;
    }

    /**
     * 记录用户非系统配置获得积分加分日志
     * @param noSystemPointsAddRequest 非系统配置行为积分对象
     */
    private void recordUserNoSystemPointsGainLog(NoSystemPointsAddRequest noSystemPointsAddRequest) {
        // 记录该次加分日志
        UserPointsAddLog userPointsAddLog = BeanUtil.copyProperties(noSystemPointsAddRequest, UserPointsAddLog::new); // 复制属性
        userPointsAddLog.setValueChangeType("增加"); // 设置改变类型,增加
        userPointsAddLog.setCreatedDate(new Date());
        userPointsAddLog.setCreatorId(noSystemPointsAddRequest.getUserId()); // 设置创建者id
        userPointsAddLogMapper.insert(userPointsAddLog); // 插入数据库
    }


    /**
     * 更新用户系统配置获得积分的成长信息
     * @param userInfoFast 用户信息
     * @param systemPointsAddRequest 用户成长事件信息
     * @return 更新后的用户信息
     * @throws ZxException 如果找不到对应的平台积分规则
     */
    private UserInfoFast updateUserSystemPointsGainGrowth(UserInfoFast userInfoFast, SystemPointsAddRequest systemPointsAddRequest){

        // 在内存中查找 某行为在 某等级 下对应的平台积分规则记录
        PlatfPoinsRightsRules platformPoinsRules = findPlatformPoinsRules(systemPointsAddRequest.getUserBehaviorType(),userInfoFast);
        UserSystemRightsVo userSystemRightsVo = platfPoinsRightsRulesMapper.selectUserSystemRight(userInfoFast.getUserId(), systemPointsAddRequest.getUserBehaviorType());


        if (userSystemRightsVo == null) {
            throw new ZxException(20001,"userBehaviorType:用户加分行为输入错误或不存在");
        }

        // 记录该次加分日志
        recordUserGrowthLog(systemPointsAddRequest,userSystemRightsVo);

        UserInfoFast updateUserPoints = updateUserPoints(userInfoFast, userSystemRightsVo);

        // 调用方法更新用户等级信息
        return growthValueWeightService.addUserGrowthValueByGrowthValueWeight(updateUserPoints);
    }

    /**
     * 记录该次加分日志
     * @param systemPointsAddRequest 用户成长事件信息
     */
    private void recordUserGrowthLog(SystemPointsAddRequest systemPointsAddRequest, UserSystemRightsVo userSystemRightsVo) {


        if (userSystemRightsVo.getUserConsumeType().equals("非消费")) {
            // 记录该次加分日志
            UserPointsAddLog userPointsAddLog = BeanUtil.copyProperties(systemPointsAddRequest, UserPointsAddLog::new); // 复制属性
            userPointsAddLog.setValueChangeType("增加"); // 设置改变类型,增加
            userPointsAddLog.setUserBehaviorType(userSystemRightsVo.getRuleName()); // 设置行为类型
            userPointsAddLog.setUserPoints(userSystemRightsVo.getTotalRuleObjectGetPoints()); // 设置积分值
            userPointsAddLog.setCreatedDate(new Date()); // 设置添加日期
            userPointsAddLog.setUserPointsType(userSystemRightsVo.getRuleObjectType()); // 设置积分类型
            userPointsAddLog.setCreatorId(systemPointsAddRequest.getUserId()); // 设置创建者id
            userPointsAddLogMapper.insert(userPointsAddLog); // 插入数据库
        }

        if (userSystemRightsVo.getUserConsumeType().equals("消费")) {

            if (ObjectUtils.isEmpty(systemPointsAddRequest.getConsumeMoney())) {
                throw new ZxException(20001,"消费类型积分规则，消费金额不能为空");
            }

            // 记录该次加分日志
            UserPointsAddLog userPointsAddLog = BeanUtil.copyProperties(systemPointsAddRequest, UserPointsAddLog::new); // 复制属性
            userPointsAddLog.setValueChangeType("增加"); // 设置改变类型,增加
            userPointsAddLog.setUserBehaviorType(userSystemRightsVo.getRuleName()); // 设置行为类型

            BigDecimal longValue = BigDecimal.valueOf(userSystemRightsVo.getTotalRuleObjectGetPoints());
            BigDecimal result = longValue.multiply(systemPointsAddRequest.getConsumeMoney());
            Double finalResult = result.doubleValue();
            userPointsAddLog.setUserPoints(finalResult); // 设置积分值

            userPointsAddLog.setUserPointsType(userSystemRightsVo.getRuleObjectType()); // 设置积分类型
            userPointsAddLog.setCreatorId(systemPointsAddRequest.getUserId()); // 设置创建者id
            userPointsAddLogMapper.insert(userPointsAddLog); // 插入数据库
        }

    }

    /**
     * 把用户加分记录到数据库中
     * @param userInfoFast 用户信息
     * @param userSystemRightsVo 平台积分规则
     * @return 更新后的用户信息
     */
    private UserInfoFast updateUserPoints(UserInfoFast userInfoFast, UserSystemRightsVo userSystemRightsVo) {

        if (!ObjectUtils.isEmpty(userSystemRightsVo.getRuleObjectType())) {

            // 更新用户信息
            if (userSystemRightsVo.getRuleObjectType().equals("行为积分")) {

                if (userSystemRightsVo.getValueChangeType().equals("增加")) {
                    userInfoFast.setUserBehaviorPoints(userInfoFast.getUserBehaviorPoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户行为积分
                    userInfoFast.setDynamicBehaviorPoints(userInfoFast.getDynamicBehaviorPoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态行为积分
                }

                if (userSystemRightsVo.getValueChangeType().equals("减少")) {
                    userInfoFast.setDynamicBehaviorPoints(userInfoFast.getDynamicBehaviorPoints() - userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态行为积分
                }
            }
            if (userSystemRightsVo.getRuleObjectType().equals("游戏积分")) {

                if (userSystemRightsVo.getValueChangeType().equals("增加")) {
                    userInfoFast.setUserGamePoints(userInfoFast.getUserGamePoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户游戏积分
                    userInfoFast.setDynamicGamePoints(userInfoFast.getDynamicGamePoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态游戏积分
                }

                if (userSystemRightsVo.getValueChangeType().equals("减少")) {
                    userInfoFast.setDynamicBehaviorPoints(userInfoFast.getDynamicGamePoints() - userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态游戏积分
                }

            }
            if (userSystemRightsVo.getRuleObjectType().equals("购物积分")) {

                if (userSystemRightsVo.getValueChangeType().equals("增加")) {
                    userInfoFast.setUserShoppingPoints(userInfoFast.getUserShoppingPoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户购物积分
                    userInfoFast.setDynamicShoppingPoints(userInfoFast.getDynamicShoppingPoints() + userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态购物积分
                }

                if (userSystemRightsVo.getValueChangeType().equals("减少")) {
                    userInfoFast.setDynamicBehaviorPoints(userInfoFast.getDynamicShoppingPoints() - userSystemRightsVo.getTotalRuleObjectGetPoints()); // 更新用户动态购物积分
                }

            }
        }

        return userInfoFast;

    }

    /**
     * 查询不为空的平台积分规则记录
     * @param userBehaviorLevel 用户行为等级的平台积分规则
     * @param gameLevel 游戏等级的平台积分规则
     * @param shopingLevel 购物等级的平台积分规则
     * @return 不为空的平台积分规则记录，如果找不到则返回 null
     */
    private PlatfPoinsRightsRules getIsNotNullPlatformPoinsRules(PlatfPoinsRightsRules userBehaviorLevel, PlatfPoinsRightsRules gameLevel, PlatfPoinsRightsRules shopingLevel) {
        if (ObjectUtils.isEmpty(userBehaviorLevel)) {
            if (ObjectUtils.isEmpty(gameLevel)) {
                if (ObjectUtils.isEmpty(shopingLevel)) {
                    return null;
                } else {
                    return shopingLevel;
                }
            } else {
                return gameLevel;
            }
        } else {
            return userBehaviorLevel;
        }

    }

    /**
     * 查找该某行为在某等级下对应的平台积分规则记录
     * @param userBehaviorType 用户行为类型
     * @param userInfoFast 用户信息
     * @return 对应的平台积分规则，如果找不到则返回 null
     */
    private PlatfPoinsRightsRules findPlatformPoinsRules(String userBehaviorType, UserInfoFast userInfoFast) {

        // 查询用户行为等级、游戏等级、购物等级
        Long userBehaviorLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getUserBehaviorPoints(), UserLevelType.行为等级.getName());// 用户行为等级的id
        Long gameLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getUserGamePoints(), UserLevelType.游戏等级.getName()); // 游戏等级的id
        Long shopingLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getUserShoppingPoints(), UserLevelType.购物等级.getName()); // 购物等级的id

        // 在数据库中查找 某行为在 某等级 下对应的平台积分规则记录
        PlatfPoinsRightsRules userBehaviorLevel = platformPoinsRulesMapper.selectOneByUserBehaviorTypeAndLevelInfoId(userBehaviorType, userBehaviorLevelId); // 用户行为等级的平台积分规则
        PlatfPoinsRightsRules gameLevel = platformPoinsRulesMapper.selectOneByUserBehaviorTypeAndLevelInfoId(userBehaviorType, gameLevelId); // 游戏等级的平台积分规则
        PlatfPoinsRightsRules shopingLevel = platformPoinsRulesMapper.selectOneByUserBehaviorTypeAndLevelInfoId(userBehaviorType, shopingLevelId); // 购物等级的平台积分规则


        // 返回不为空的平台积分规则记录
        return getIsNotNullPlatformPoinsRules(userBehaviorLevel,gameLevel,shopingLevel);
    }


}
