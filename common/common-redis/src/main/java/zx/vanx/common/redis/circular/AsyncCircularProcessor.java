package zx.vanx.common.redis.circular;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import zx.vanx.common.redis.circular.provider.CircularDataProvider;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiConsumer;

/**
 * 异步环形处理器 - 泛型版本
 * 
 * 核心功能：
 * 1. 定时扫描过期批次
 * 2. 异步获取新推荐数据
 * 3. 批量替换过期批次数据
 * 4. 监控处理状态和性能指标
 * 
 * 注意：不使用@Component注解，通过配置类手动创建Bean
 * 这样可以避免Spring自动装配泛型类型时的冲突问题
 * 
 * @param <T> 数据类型，可以是任意业务对象
 * <AUTHOR>
 */
@Slf4j
public class AsyncCircularProcessor<T> {

    /**
     * 环形数据提供者 - 通过构造函数注入
     */
    private final CircularDataProvider<T> dataProvider;

    /**
     * 调度器保存回调函数 - 用于保存调度器到Redis
     */
    private BiConsumer<Long, CircularBufferScheduler<T>> schedulerSaveCallback;

    /**
     * 处理配置
     */
    @Getter
    private final AsyncCircularProcessorConfig config = new AsyncCircularProcessorConfig();

    // 处理状态管理
    private final Map<Long, ProcessingTask> activeTasks = new ConcurrentHashMap<>();

    /**
     * 处理指标
     */
    @Getter
    private final ProcessingMetrics metrics = new ProcessingMetrics();

    // 调度器管理
    private final Map<Long, CircularBufferScheduler<T>> schedulerCache = new ConcurrentHashMap<>();

    /**
     * 构造函数 - 支持依赖注入
     */
    public AsyncCircularProcessor(CircularDataProvider<T> dataProvider) {
        this.dataProvider = dataProvider;
    }

    /**
     * 设置调度器保存回调函数
     * 
     * @param saveCallback 保存回调函数
     */
    public void setSchedulerSaveCallback(BiConsumer<Long, CircularBufferScheduler<T>> saveCallback) {
        this.schedulerSaveCallback = saveCallback;
    }

    /**
     * 初始化异步处理器
     */
    @PostConstruct
    public void initialize() {
        log.info("Spring @Async异步环形处理器初始化完成");
    }

    /**
     * 销毁资源
     */
    @PreDestroy
    public void destroy() {
        log.info("Spring @Async异步环形处理器已销毁");
    }

    /**
     * 扫描过期批次 - 供外部定时任务调用
     */
    public void scheduleExpiredBatchProcessing() {
        try {
            log.debug("开始扫描过期批次");

            // 获取所有需要处理的用户id
            List<Long> userIdsToProcess = findUserIdsWithExpiredBatches();

            if (userIdsToProcess.isEmpty()) {
                log.debug("没有发现需要处理的过期批次");
                return;
            }

            log.info("发现{}个标识需要处理过期批次: {}", userIdsToProcess.size(), userIdsToProcess);

            // 按优先级排序（过期批次多的优先）
            userIdsToProcess.sort((id1, id2) -> {
                CircularBufferScheduler<T> s1 = schedulerCache.get(id1);
                CircularBufferScheduler<T> s2 = schedulerCache.get(id2);
                if (s1 == null || s2 == null) return 0;
                return Integer.compare(s2.getExpiredBatchCount(), s1.getExpiredBatchCount());
            });

            // 使用Spring @Async异步处理
            for (Long identifier : userIdsToProcess) {
                processIdentifierBatchesAsync(identifier);
            }

            metrics.incrementScanCount();

        } catch (Exception e) {
            log.error("定时扫描过期批次失败: {}", e.getMessage(), e);
            metrics.incrementScanErrorCount();
        }
    }

    /**
     * 异步处理批次 - 使用Spring @Async
     */
    @Async("circularProcessorExecutor")
    public CompletableFuture<Boolean> processIdentifierBatchesAsync(Long userId) {

        // 如果标识有处理任务在进行中，则返回false
        if (activeTasks.containsKey(userId)) {
            log.debug("标识{}已有处理任务在进行中，跳过", userId);
            return CompletableFuture.completedFuture(false);
        }

        // 创建处理任务
        ProcessingTask task = new ProcessingTask(userId);
        activeTasks.put(userId, task);

        try {

            log.info("开始异步处理标识{}的过期批次", userId);

            // 获取调度器
            CircularBufferScheduler<T> scheduler = schedulerCache.get(userId);
            if (scheduler == null) {
                log.warn("标识{}的调度器不存在，跳过处理", userId);
                return CompletableFuture.completedFuture(false);
            }

            // 获取过期批次
            List<Integer> expiredBatchIds = scheduler.getExpiredBatchIds();
            if (expiredBatchIds.isEmpty()) {
                log.debug("标识{}没有过期批次，跳过处理", userId);
                return CompletableFuture.completedFuture(true);
            }

            task.setExpiredBatchIds(expiredBatchIds);
            log.info("处理标识{}的过期批次: {}", userId, expiredBatchIds);

            // 异步获取新数据
            Map<Integer, List<T>> newDataMap = dataProvider.generateBatchData(userId, expiredBatchIds);

            if (newDataMap.isEmpty()) {
                log.warn("标识{}没有获取到新数据，跳过替换", userId);
                return CompletableFuture.completedFuture(false);
            }

            // 批量替换过期数据（包含状态转换：expired -> ready）
            int successCount = scheduler.replaceAllExpiredBatchesData(newDataMap);

            // 验证状态转换是否成功
            List<Integer> remainingExpiredIds = scheduler.getExpiredBatchIds();
            if (!remainingExpiredIds.isEmpty()) {
                log.warn("标识{}仍有未处理的过期批次: {}, 可能存在状态转换问题", userId, remainingExpiredIds);
            }

            // 更新调度器缓存，确保内存中的引用是最新的
            schedulerCache.put(userId, scheduler);
            log.debug("更新标识{}的调度器缓存", userId);

            // 记录状态转换详情
            for (Integer batchId : expiredBatchIds) {
                if (newDataMap.containsKey(batchId)) {
                    String currentStatus = scheduler.getBatchStatus(batchId);
                    log.info("批次{}状态转换完成: expired -> {}, userId: {}", batchId, currentStatus, userId);
                }
            }

            task.setProcessedBatchCount(successCount);
            task.markCompleted();

            log.info("标识{}过期批次处理完成，成功替换: {}/{}", userId, successCount, expiredBatchIds.size());
            metrics.incrementProcessingSuccessCount();

            // 保存调度器到Redis（包含所有状态变化）
            if (schedulerSaveCallback != null) {
                schedulerSaveCallback.accept(userId, scheduler);
                log.info("调度器状态已保存到Redis, userId: {}, 处理批次: {}", userId, expiredBatchIds);
            } else {
                log.warn("调度器保存回调函数未设置, 无法保存到Redis, userId: {}", userId);
            }

            return CompletableFuture.completedFuture(true);

        } catch (Exception e) {
            task.markFailed(e.getMessage());
            log.error("标识{}异步处理失败: {}", userId, e.getMessage(), e);
            metrics.incrementProcessingErrorCount();
            return CompletableFuture.completedFuture(false);
        } finally {
            activeTasks.remove(userId);
        }
    }

    /**
     * 查找有过期批次的标识
     * 
     * @return 标识列表
     */
    private List<Long> findUserIdsWithExpiredBatches() {

        List<Long> userIdsToProcess = new ArrayList<>();

        for (Map.Entry<Long, CircularBufferScheduler<T>> entry : schedulerCache.entrySet()) {
            Long userId = entry.getKey();
            CircularBufferScheduler<T> scheduler = entry.getValue();

            if (scheduler != null && scheduler.getExpiredBatchCount() >= config.getExpiredBatchThreshold()) {
                // 检查是否已经在处理中
                if (!activeTasks.containsKey(userId)) {
                    userIdsToProcess.add(userId);
                }
            }
        }

        return userIdsToProcess;
    }

    /**
     * 触发处理
     * 
     * @param userId 用户id
     * @return 处理任务
     */
    @Async("circularProcessorExecutor")
    public CompletableFuture<Boolean> triggerProcessing(Long userId) {
        return processIdentifierBatchesAsync(userId);
    }

    /**
     * 注册调度器到处理器
     */
    public void registerScheduler(Long userId, CircularBufferScheduler<T> scheduler) {
        schedulerCache.put(userId, scheduler);
        log.debug("注册标识{}的调度器到Spring @Async异步处理器", userId);
    }

    /**
     * 注册调度器到处理器,加分类
     */
    public void registerScheduler(Long userId,Long categoryId, CircularBufferScheduler<T> scheduler) {
        schedulerCache.put(userId + categoryId, scheduler);
        log.debug("注册标识{}的调度器到Spring @Async异步处理器", userId + categoryId);
    }

    /**
     * 注销调度器
     */
    public void unregisterScheduler(Long userId) {
        schedulerCache.remove(userId);
        activeTasks.remove(userId);
        log.debug("注销标识{}的调度器", userId);
    }

    /**
     * 获取调度器（用于测试）
     * 
     * @param identifier 标识
     * @return 调度器对象，如果不存在则返回null
     */
    public CircularBufferScheduler<T> getScheduler(Long identifier) {
        return schedulerCache.get(identifier);
    }

    /**
     * 获取处理状态
     */
    public Map<String, Object> getProcessingStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("registeredSchedulers", schedulerCache.size());
        status.put("activeTasks", activeTasks.size());
        status.put("activeTaskIdentifiers", new ArrayList<>(activeTasks.keySet()));
        status.put("metrics", metrics.getMetricsSnapshot());
        status.put("framework", "Spring @Async");
        return status;
    }

    /**
     * 获取处理任务状态
     */
    public ProcessingTask getProcessingTask(Long identifier) {
        return activeTasks.get(identifier);
    }

    /**
     * 处理任务类
     */
    @Getter
    public static class ProcessingTask {
        // 标识
        private final Long identifier;
        // 开始时间
        private final LocalDateTime startTime;
        // 结束时间
        private LocalDateTime endTime;
        // 状态
        private String status;
        
        @Setter
        private List<Integer> expiredBatchIds;
        
        @Setter
        private Integer processedBatchCount;
        
        // 错误消息
        private String errorMessage;

        public ProcessingTask(Long identifier) {
            this.identifier = identifier;
            this.startTime = LocalDateTime.now();
            this.status = "PROCESSING";
            this.expiredBatchIds = new ArrayList<>();
            this.processedBatchCount = 0;
        }

        public void markCompleted() {
            this.endTime = LocalDateTime.now();
            this.status = "COMPLETED";
        }

        public void markFailed(String errorMessage) {
            this.endTime = LocalDateTime.now();
            this.status = "FAILED";
            this.errorMessage = errorMessage;
        }

        public long getDurationMillis() {
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            return java.time.Duration.between(startTime, end).toMillis();
        }
    }

    /**
     * 处理指标类
     */
    @Getter
    public static class ProcessingMetrics {
        private final AtomicLong scanCount = new AtomicLong(0);
        private final AtomicLong scanErrorCount = new AtomicLong(0);
        private final AtomicLong processingSuccessCount = new AtomicLong(0);
        private final AtomicLong processingErrorCount = new AtomicLong(0);
        private final AtomicLong totalProcessingTimeMillis = new AtomicLong(0);

        public void incrementScanCount() {
            scanCount.incrementAndGet();
        }

        public void incrementScanErrorCount() {
            scanErrorCount.incrementAndGet();
        }

        public void incrementProcessingSuccessCount() {
            processingSuccessCount.incrementAndGet();
        }

        public void incrementProcessingErrorCount() {
            processingErrorCount.incrementAndGet();
        }

        public void addProcessingTime(long millis) {
            totalProcessingTimeMillis.addAndGet(millis);
        }

        public Map<String, Object> getMetricsSnapshot() {
            Map<String, Object> snapshot = new HashMap<>();
            snapshot.put("scanCount", scanCount.get());
            snapshot.put("scanErrorCount", scanErrorCount.get());
            snapshot.put("processingSuccessCount", processingSuccessCount.get());
            snapshot.put("processingErrorCount", processingErrorCount.get());
            snapshot.put("totalProcessingTimeMillis", totalProcessingTimeMillis.get());

            long successCount = processingSuccessCount.get();
            long totalCount = successCount + processingErrorCount.get();
            double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
            snapshot.put("successRate", String.format("%.2f%%", successRate));

            double avgProcessingTime = successCount > 0 ? (double) totalProcessingTimeMillis.get() / successCount : 0;
            snapshot.put("avgProcessingTimeMillis", String.format("%.2f", avgProcessingTime));

            return snapshot;
        }
    }

    /**
     * 配置类
     */
    @Getter
    public static class AsyncCircularProcessorConfig {
        // 过期批次阈值
        private final int expiredBatchThreshold = 2;
        // 重试次数
        private final int retryCount = 3;
        // 重试延迟时间
        private final long retryDelayMillis = 1000;
    }
}