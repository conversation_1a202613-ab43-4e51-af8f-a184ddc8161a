package zx.vanx.common.redis.circular.provider;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import zx.vanx.common.redis.circular.config.CircularBufferProperties;
import zx.vanx.common.redis.circular.service.CircularDataService;
import zx.vanx.common.redis.circular.strategy.StrategyManager;

import java.util.*;

/**
 * 基于策略的数据提供者实现
 * 
 * 支持多种数据生成策略：
 * 1. 基于推荐算法的数据生成
 * 2. 基于热门内容的数据填充
 * 3. 基于随机选择的数据生成
 * 4. 基于混合策略的数据生成
 *  主要就是通过系统的默认策略或者用户设定的策略来生成数据
 * @param <T> 数据类型
 * <AUTHOR>
 */
@Slf4j
public class StrategyBasedDataProvider<T> implements CircularDataProvider<T> {

    private final CircularDataService<T> dataService;
    private final StrategyManager<T> strategyManager;
    private final CircularBufferProperties circularBufferProperties;

    // 策略常量
    public static final String STRATEGY_RECOMMENDATION = "recommendation";
    public static final String STRATEGY_POPULAR = "popular";
    public static final String STRATEGY_RANDOM = "random";
    public static final String STRATEGY_MIXED = "mixed";

    /**
     * 构造函数
     * 
     * @param dataService 数据服务
     * @param circularBufferProperties 环形缓冲配置
     */
    public StrategyBasedDataProvider(CircularDataService<T> dataService, CircularBufferProperties circularBufferProperties) {
        this.dataService = dataService;
        this.circularBufferProperties = circularBufferProperties != null ? circularBufferProperties : new CircularBufferProperties();
        this.strategyManager = new StrategyManager<>(dataService);
        
        // 配置混合策略
        strategyManager.configureMixedStrategy(
            this.circularBufferProperties.getMixedStrategyRatio().getRecommendation(),
            this.circularBufferProperties.getMixedStrategyRatio().getPopular()
        );
        
        log.info("StrategyBasedDataProvider初始化完成，批次大小: {}, 唯一策略: {}", 
                this.circularBufferProperties.getDefaultBatchSize(), this.circularBufferProperties.getUniqueStrategy());
    }

    /**
     * 兼容性构造函数（不使用CircularBufferProperties）
     * 
     * @param dataService 数据服务
     */
    public StrategyBasedDataProvider(CircularDataService<T> dataService) {
        this(dataService, null);
    }

    /**
     * 生成多个批次的数据
     * 
     * @param userId 用户id
     * @param batchIds   需要生成数据的批次ID列表【1，2，3，4】
     * @return 批次ID到数据列表的映射
     */
    @Override
    public Map<Integer, List<T>> generateBatchData(Long userId, List<Integer> batchIds) {
        Map<Integer, List<T>> result = new HashMap<>();

        log.info("开始为标识{}生成{}个批次的数据", userId, batchIds.size());

        for (Integer batchId : batchIds) {
            try {
                // 使用CircularBufferProperties中的批次大小配置
                List<T> batchData = generateSingleBatchData(userId, batchId, circularBufferProperties.getDefaultBatchSize());
                result.put(batchId, batchData);
                log.debug("为标识{}生成批次{}数据，数量: {}", userId, batchId, batchData.size());
            } catch (Exception e) {
                log.error("为标识{}生成批次{}数据失败: {}", userId, batchId, e.getMessage(), e);
                // 生成失败时提供空列表，避免整个处理失败
                result.put(batchId, new ArrayList<>());
            }
        }

        log.info("为标识{}生成批次数据完成，成功生成: {}/{}", userId, result.size(), batchIds.size());
        return result;
    }

    /**
     * 生成多个批次的数据
     *
     * @param userId 用户id
     * @param categoryId 分类id
     * @param batchIds   需要生成数据的批次ID列表【1，2，3，4】
     * @return 批次ID到数据列表的映射
     */
    @Override
    public Map<Integer, List<T>> generateBatchData(Long userId,Long categoryId, List<Integer> batchIds) {
        Map<Integer, List<T>> result = new HashMap<>();

        log.info("开始为标识{}生成{}个批次的数据", userId, batchIds.size());

        for (Integer batchId : batchIds) {
            try {
                // 使用CircularBufferProperties中的批次大小配置
                List<T> batchData = generateSingleBatchData(userId,categoryId, batchId, circularBufferProperties.getDefaultBatchSize());
                result.put(batchId, batchData);
                log.debug("为标识{}生成批次{}数据，数量: {}", userId, batchId, batchData.size());
            } catch (Exception e) {
                log.error("为标识{}生成批次{}数据失败: {}", userId, batchId, e.getMessage(), e);
                // 生成失败时提供空列表，避免整个处理失败
                result.put(batchId, new ArrayList<>());
            }
        }

        log.info("为标识{}生成批次数据完成，成功生成: {}/{}", userId, result.size(), batchIds.size());
        return result;
    }

    /**
     * 生成单个批次的数据
     * 
     * @param userId 用户id
     * @param batchId    批次ID
     * @param batchSize  批次大小
     * @return 生成的数据列表
     */
    @Override
    public List<T> generateSingleBatchData(Long userId, Integer batchId, Integer batchSize) {
        String strategy = determineStrategy(batchId);
        log.debug("为标识{}批次{}使用策略: {}", userId, batchId, strategy);

        // 使用策略管理器获取数据
        List<T> data = strategyManager.fetchData(strategy, userId, batchSize);
        
        // 确保返回的数据不超过批次大小
        if (data.size() > batchSize) {
            return new ArrayList<>(data.subList(0, batchSize));
        }
        
        return data;
    }

    /**
     * 生成单个批次的数据
     *
     * @param userId 用户id
     * @param categoryId 分类id
     * @param batchId    批次ID
     * @param batchSize  批次大小
     * @return 生成的数据列表
     */
    @Override
    public List<T> generateSingleBatchData(Long userId,Long categoryId, Integer batchId, Integer batchSize) {
        String strategy = determineStrategy(batchId);
        log.debug("为标识{}批次{}使用策略: {}", userId, batchId, strategy);

        // 使用策略管理器获取数据
        List<T> data = strategyManager.fetchData(strategy, userId,categoryId, batchSize);

        // 确保返回的数据不超过批次大小
        if (data.size() > batchSize) {
            return new ArrayList<>(data.subList(0, batchSize));
        }

        return data;
    }

    /**
     * 确定数据生成策略
     * 支持唯一推荐算法配置项，从CircularBufferProperties中读取
     * 如果指定了唯一推荐算法，那就所有数据都用该策略
     * 
     * @param batchId    批次ID
     * @return 策略名称
     */
    private String determineStrategy(Integer batchId) {

        // 优先检查是否配置了唯一策略
        if (StringUtils.hasText(circularBufferProperties.getUniqueStrategy())) {
            log.debug("使用配置的唯一策略: {}", circularBufferProperties.getUniqueStrategy());
            return circularBufferProperties.getUniqueStrategy();
        }
        
        // 启用智能策略选择（基于配置的权重）
        return selectSmartStrategy(batchId);
    }

    /**
     * 智能选择策略
     */
    private String selectSmartStrategy(Integer batchId) {
        // 第一批次优先使用配置的唯一策略，如果没有则使用推荐策略
        if (batchId == 1) {
            return StringUtils.hasText(circularBufferProperties.getUniqueStrategy()) ? 
                   circularBufferProperties.getUniqueStrategy() : STRATEGY_RECOMMENDATION;
        }
        
        // 其他批次根据配置的权重选择
        CircularBufferProperties.StrategyWeights weights = circularBufferProperties.getStrategyWeights();
        double random = Math.random();
        
        if (random < weights.getRecommendation()) {
            return STRATEGY_RECOMMENDATION;
        } else if (random < weights.getRecommendation() + weights.getPopular()) {
            return STRATEGY_POPULAR;
        } else if (random < weights.getRecommendation() + weights.getPopular() + weights.getRandom()) {
            return STRATEGY_RANDOM;
        } else {
            return STRATEGY_MIXED;
        }
    }


    /**
     * 检查数据提供者是否可用
     */
    @Override
    public boolean isAvailable() {
        try {
            return dataService != null && dataService.isAvailable();
        } catch (Exception e) {
            log.error("检查数据提供者可用性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取数据提供者名称
     */
    @Override
    public String getProviderName() {
        return "StrategyBasedDataProvider<" + dataService.getServiceName() + ">";
    }
}