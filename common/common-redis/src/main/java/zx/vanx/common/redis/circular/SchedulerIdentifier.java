package zx.vanx.common.redis.circular;

import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Objects;

/**
 * 调度器统一标识符
 * 
 * 封装用户ID和分类ID的组合，提供统一的标识符管理
 * 支持只有userId或userId+categoryId的场景
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode
public class SchedulerIdentifier implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（必须）
     */
    private final Long userId;

    /**
     * 分类ID（可选）
     */
    private final Long categoryId;

    /**
     * 创建只包含用户ID的标识符
     * 
     * @param userId 用户ID
     */
    public SchedulerIdentifier(Long userId) {
        this.userId = validateUserId(userId);
        this.categoryId = null;
    }

    /**
     * 创建包含用户ID和分类ID的标识符
     * 
     * @param userId 用户ID
     * @param categoryId 分类ID
     */
    public SchedulerIdentifier(Long userId, Long categoryId) {
        this.userId = validateUserId(userId);
        this.categoryId = categoryId;
    }

    /**
     * 从NextDataRequest创建标识符的工厂方法
     * 
     * @param userId 用户ID
     * @param categoryId 分类ID（可为null）
     * @return 标识符实例
     */
    public static SchedulerIdentifier of(Long userId, Long categoryId) {
        if (categoryId == null) {
            return new SchedulerIdentifier(userId);
        }
        return new SchedulerIdentifier(userId, categoryId);
    }

    /**
     * 从用户ID创建标识符的工厂方法
     * 
     * @param userId 用户ID
     * @return 标识符实例
     */
    public static SchedulerIdentifier of(Long userId) {
        return new SchedulerIdentifier(userId);
    }

    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 获取分类ID
     * 
     * @return 分类ID，可能为null
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * 检查是否包含分类ID
     * 
     * @return true表示包含分类ID，false表示只有用户ID
     */
    public boolean hasCategory() {
        return categoryId != null;
    }

    /**
     * 获取用于Redis键的字符串表示
     * 格式：userId 或 userId:categoryId
     * 
     * @return Redis键字符串
     */
    public String toKeyString() {
        if (categoryId == null) {
            return String.valueOf(userId);
        }
        return userId + ":" + categoryId;
    }

    /**
     * 获取用于异步处理器注册的组合键
     * 当有分类ID时，返回 userId + categoryId 的组合值
     * 
     * @return 组合键
     */
    public Long getProcessorKey() {
        if (categoryId == null) {
            return userId;
        }
        // 使用数学方式组合两个ID，避免字符串拼接的问题
        return userId * 1000000L + categoryId;
    }

    /**
     * 生成调度器Redis键
     * 
     * @param prefix 键前缀
     * @return 完整的Redis键
     */
    public String generateRedisKey(String prefix) {
        return prefix + toKeyString();
    }

    /**
     * 验证用户ID的有效性
     * 
     * @param userId 用户ID
     * @return 验证后的用户ID
     * @throws IllegalArgumentException 如果用户ID无效
     */
    private Long validateUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID必须大于0");
        }
        return userId;
    }

    /**
     * 重写toString方法，便于日志输出
     */
    @Override
    public String toString() {
        if (categoryId == null) {
            return "SchedulerIdentifier{userId=" + userId + "}";
        }
        return "SchedulerIdentifier{userId=" + userId + ", categoryId=" + categoryId + "}";
    }

    /**
     * 重写equals方法，确保标识符比较的准确性
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SchedulerIdentifier that = (SchedulerIdentifier) obj;
        return Objects.equals(userId, that.userId) && 
               Objects.equals(categoryId, that.categoryId);
    }

    /**
     * 重写hashCode方法，确保作为Map键时的正确性
     */
    @Override
    public int hashCode() {
        return Objects.hash(userId, categoryId);
    }
}