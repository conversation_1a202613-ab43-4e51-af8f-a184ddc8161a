package zx.vanx.common.redis.circular;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import zx.vanx.common.redis.circular.metadata.SchedulerMetadata;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 环形缓冲调度器 - 支持可配置的批次数量，高性能数据访问
 * 
 * 核心特性：
 * 1. 环形缓冲设计，避免频繁内存分配
 * 2. 批次状态管理（active、ready、expired）
 * 3. 支持泛型，可存储任意类型数据
 * 4. 线程安全，使用ConcurrentHashMap
 * 
 * @param <T> 数据类型，如VideoVO、ArticleVO等
 * <AUTHOR>
 */
@Slf4j
@Data
public class CircularBufferScheduler<T> {

    // 可配置常量
    private final int totalBatches; // 总批次数，默认为4
    private final int batchSize; // 每批次大小，默认为50
    private final int activeBatchCount; // 活跃批次数量，默认为2
    private static final String VERSION = "1.0"; // 版本号

    // 批次状态常量
    public static final String STATUS_ACTIVE = "active"; // 活跃状态
    public static final String STATUS_READY = "ready"; // 准备状态
    public static final String STATUS_EXPIRED = "expired"; // 过期状态

    // 核心属性
    private Long userId; // 用户ID（业务标识，可选）
    private LocalDateTime createdAt; // 创建时间
    private LocalDateTime lastUpdated; // 最后更新时间

    // 动态批次数据存储 - 使用ConcurrentHashMap保证线程安全和高性能
    private final Map<Integer, List<T>> batchDataMap;

    // 批次状态管理
    private Map<Integer, String> batchStatuses;

    // 活跃批次列表 - 存储当前活跃的批次ID
    private List<Integer> activeBatchIds;

    // 调度器元数据
    private SchedulerMetadata metadata;

    /**
     * 默认构造函数 - 使用默认配置（4批次，50条数据，2个活跃批次）
     * 
     * @param userId      用户ID（业务标识）
     * @param initialData 初始数据，包含所有批次的数据列表
     */
    public CircularBufferScheduler(Long userId, List<List<T>> initialData) {
        this(userId, initialData, 4, 50, 2);
    }

    /**
     * 完整构造函数 - 支持自定义批次配置
     * 
     * @param userId           用户ID（业务标识）
     * @param initialData      初始数据
     * @param totalBatches     总批次数
     * @param batchSize        每批次大小
     * @param activeBatchCount 活跃批次数量
     */
    public CircularBufferScheduler(Long userId, List<List<T>> initialData, int totalBatches, int batchSize, int activeBatchCount) {

        // 参数验证
        if (totalBatches < 3) {
            throw new IllegalArgumentException("总批次数不能少于3个");
        }
        if (activeBatchCount < 2 || activeBatchCount > totalBatches - 1) {
            throw new IllegalArgumentException("活跃批次数量必须在2到总批次数-1之间");
        }
        if (batchSize < 1) {
            throw new IllegalArgumentException("批次大小必须大于0");
        }

        this.totalBatches = totalBatches;
        this.batchSize = batchSize;
        this.activeBatchCount = activeBatchCount;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.metadata = new SchedulerMetadata(batchSize);

        // 初始化批次数据存储
        this.batchDataMap = new ConcurrentHashMap<>(totalBatches);

        // 初始化批次状态
        this.batchStatuses = new HashMap<>();

        // 初始化活跃批次列表（从批次1开始）
        this.activeBatchIds = new ArrayList<>();
        for (int i = 1; i <= activeBatchCount; i++) {
            this.activeBatchIds.add(i);
        }

        // 初始化批次数据
        initializeBatchData(initialData);

        // 初始化批次状态
        initializeBatchStatuses();

        log.info("环形缓冲调度器初始化完成, userId: {}, 总批次数: {}, 批次大小: {}, 活跃批次: {}", 
                userId, totalBatches, batchSize, activeBatchIds);
    }

    /**
     * 仅使用用户ID创建空调度器 - 使用默认配置
     * 
     * @param userId 用户ID（业务标识）
     */
    public CircularBufferScheduler(Long userId) {
        this(userId, null, 4, 50, 2);
    }

    /**
     * 初始化批次数据
     */
    private void initializeBatchData(List<List<T>> initialData) {
        for (int i = 1; i <= totalBatches; i++) {
            if (initialData != null && initialData.size() >= i && initialData.get(i - 1) != null) {
                batchDataMap.put(i, new ArrayList<>(initialData.get(i - 1)));
            } else {
                batchDataMap.put(i, new ArrayList<>());
            }
        }
    }

    /**
     * 初始化批次状态
     */
    private void initializeBatchStatuses() {
        for (int i = 1; i <= totalBatches; i++) {
            String status = activeBatchIds.contains(i) ? STATUS_ACTIVE : STATUS_READY;
            batchStatuses.put(i, status);
        }
    }

    /**
     * 获取指定批次的数据 - 高性能Map访问
     */
    public List<T> getBatchData(int batchId) {
        if (batchId < 1 || batchId > totalBatches) {
            log.warn("无效的批次ID: {}, 有效范围: 1-{}", batchId, totalBatches);
            return new ArrayList<>();
        }
        return batchDataMap.getOrDefault(batchId, new ArrayList<>());
    }

    /**
     * 更新指定批次的数据
     */
    public void updateBatchData(int batchId, List<T> newData) {
        if (batchId < 1 || batchId > totalBatches) {
            log.warn("无效的批次ID: {}, 无法更新数据", batchId);
            return;
        }

        List<T> targetList = batchDataMap.get(batchId);
        if (targetList != null) {
            targetList.clear();
            if (newData != null) {
                targetList.addAll(newData);
            }
        } else {
            batchDataMap.put(batchId, newData != null ? new ArrayList<>(newData) : new ArrayList<>());
        }

        this.lastUpdated = LocalDateTime.now();
        log.debug("更新批次{}数据, userId: {}, 数据量: {}", batchId, userId, newData != null ? newData.size() : 0);
    }

    /**
     * 检查批次数据是否存在
     */
    public boolean batchDataExists(int batchId) {
        List<T> batchData = getBatchData(batchId);
        return !batchData.isEmpty();
    }

    /**
     * 基于用户当前位置获取下一批数据 - 核心方法，支持动态批次
     * 环形访问实现，保持与原有逻辑完全一致
     */
    public List<T> getNextDataByPosition(int count) {
        try {
            log.debug("基于位置获取数据, userId: {}, count: {}, 总批次数: {}", userId, count, totalBatches);

            // 获取当前全局位置
            int currentPosition = metadata.getGlobalPosition() != null ? metadata.getGlobalPosition() : 0;

            // 计算当前应该访问的批次（基于全局位置和动态批次数）
            int currentBatch = (currentPosition / batchSize) % totalBatches + 1;

            // 计算在当前批次内的起始位置
            int startPosition = currentPosition % batchSize;

            // 计算当前批次所要访问的结束位置
            int endPosition = startPosition + count - 1;

            // 获取当前批次数据
            List<T> queryList = new ArrayList<>(getBatchData(currentBatch));

            // 跨批次获取数据
            List<Integer> accessedBatches = new ArrayList<>();
            accessedBatches.add(currentBatch);

            // 是否需要更新批次状态
            boolean needUpdateBatchStatus = false;

            // 当前批次数据不够时，获取下一批数据进行补充
            if (endPosition + 1 > queryList.size()) {
                List<T> nextBatchData = getNextBatchData(currentBatch);
                queryList.addAll(nextBatchData);

                needUpdateBatchStatus = true;

                // 记录访问的批次
                int nextBatchId = (currentBatch % totalBatches) + 1;
                accessedBatches.add(nextBatchId);
            }

            // 确保结果列表不越界
            int toIndex = Math.min(endPosition + 1, queryList.size());
            List<T> result = queryList.subList(startPosition, toIndex);

            // 更新元数据
            metadata.setGlobalPosition(currentPosition + count);
            if (currentPosition + count >= totalBatches * batchSize) {
                metadata.setGlobalPosition((currentPosition + count) % (totalBatches * batchSize));
                needUpdateBatchStatus = true;
            }

            // 更新lastAccessedBatch
            Integer previousLastAccessedBatch = metadata.getLastAccessedBatch();
            metadata.setLastAccessedBatch(currentBatch);

            // 记录访问元数据
            metadata.setLastSourceBatches(new ArrayList<>(accessedBatches));
            metadata.setLastStartPosition(startPosition);
            metadata.setLastEndPosition(endPosition);

            // 如果批次发生了变化，需要更新批次状态
            if (previousLastAccessedBatch != null && !previousLastAccessedBatch.equals(currentBatch)) {
                needUpdateBatchStatus = true;
            }

            // 更新批次状态（如果需要）
            if (needUpdateBatchStatus) {
                updateBatchStatusForNextBatch(currentBatch);
            }

            this.lastUpdated = LocalDateTime.now();

            log.debug("环形缓冲数据获取完成, 当前位置: {}, 当前批次: {}, 访问范围: {}-{}, 总批次数: {}", 
                    currentPosition, currentBatch, startPosition, endPosition, totalBatches);

            return result;

        } catch (Exception e) {
            log.error("基于位置获取数据失败, userId: {}, count: {}, 错误: {}", userId, count, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新批次状态以支持获取下一批数据 - 支持动态批次数量
     * 
     * @param currentBatchId 当前批次ID
     */
    private void updateBatchStatusForNextBatch(int currentBatchId) {
        try {
            if (currentBatchId < 1 || currentBatchId > totalBatches) {
                log.error("更新批次状态失败，批次ID不正确, userId: {}, currentBatchId: {}, 有效范围: 1-{}", 
                        userId, currentBatchId, totalBatches);
                return;
            }

            // 保存旧的活跃批次列表，用于确定哪些批次需要设为过期
            List<Integer> oldActiveBatchIds = new ArrayList<>(activeBatchIds);

            // 计算新的活跃批次列表
            List<Integer> newActiveBatchIds = new ArrayList<>();
            for (int i = 0; i < activeBatchCount; i++) {
                int batchId = (currentBatchId + i - 1) % totalBatches + 1;
                newActiveBatchIds.add(batchId);
            }

            log.info("更新批次状态, userId: {}, 当前批次: {}, 旧活跃批次: {}, 新活跃批次: {}", 
                    userId, currentBatchId, oldActiveBatchIds, newActiveBatchIds);

            // 确保batchStatuses已初始化
            if (batchStatuses == null) {
                batchStatuses = new HashMap<>();
            }

            // 找出被替换掉的批次（在旧活跃列表中但不在新活跃列表中）
            List<Integer> expiredBatches = new ArrayList<>();
            for (Integer oldBatchId : oldActiveBatchIds) {
                if (!newActiveBatchIds.contains(oldBatchId)) {
                    expiredBatches.add(oldBatchId);
                }
            }

            // 更新批次状态：
            // 1. 新活跃批次设为active
            for (Integer batchId : newActiveBatchIds) {
                batchStatuses.put(batchId, STATUS_ACTIVE);
            }

            // 2. 被替换的批次设为expired
            for (Integer batchId : expiredBatches) {
                batchStatuses.put(batchId, STATUS_EXPIRED);
                log.info("批次{}被设为过期状态, userId: {}", batchId, userId);
            }

            // 3. 其他批次保持ready状态（只在初始化时设置，这里不修改）
            for (int i = 1; i <= totalBatches; i++) {
                if (!newActiveBatchIds.contains(i) && !expiredBatches.contains(i)) {
                    // 如果这个批次之前没有状态，设为ready
                    if (!batchStatuses.containsKey(i)) {
                        batchStatuses.put(i, STATUS_READY);
                    }
                    // 如果已经有状态，保持不变（可能是ready或expired）
                }
            }

            // 更新活跃批次列表
            this.activeBatchIds = newActiveBatchIds;

            this.lastUpdated = LocalDateTime.now();

            log.info("环形缓冲状态更新成功, userId: {}, 新活跃批次: {}, 过期批次: {}", 
                    userId, newActiveBatchIds, expiredBatches);

        } catch (Exception e) {
            log.error("更新批次状态失败, userId: {}, currentBatchId: {}, 错误: {}", 
                    userId, currentBatchId, e.getMessage(), e);
        }
    }

    /**
     * 获取下一批数据 - 支持动态批次
     * 
     * @param currentBatchId 当前批次编号
     * @return 下一批数据
     */
    private List<T> getNextBatchData(int currentBatchId) {
        int nextBatchId = (currentBatchId % totalBatches) + 1;
        return getBatchData(nextBatchId);
    }

    /**
     * 检查是否需要预加载 - 暂时禁用
     */
    public boolean shouldPreload() {
        return false;
    }

    /**
     * 获取批次状态
     */
    public String getBatchStatus(int batchId) {
        if (batchStatuses == null || batchId < 1 || batchId > totalBatches) {
            return null;
        }
        return batchStatuses.get(batchId);
    }

    /**
     * 设置批次状态
     */
    public void setBatchStatus(int batchId, String status) {
        if (batchId < 1 || batchId > totalBatches) {
            log.warn("无效的批次ID: {}, 无法设置状态", batchId);
            return;
        }

        if (batchStatuses == null) {
            batchStatuses = new HashMap<>();
        }
        batchStatuses.put(batchId, status);
        this.lastUpdated = LocalDateTime.now();
    }

    /**
     * 获取批次状态映射
     */
    @JSONField(serialize = false, deserialize = false)
    public Map<String, String> getBatchStatusMap() {
        Map<String, String> result = new HashMap<>();
        if (batchStatuses != null) {
            batchStatuses.forEach((batchId, status) -> result.put(String.valueOf(batchId), status));
        }
        return result;
    }

    /**
     * 获取总数据量
     */
    public int getTotalDataSize() {
        return batchDataMap.values().stream().mapToInt(List::size).sum();
    }

    /**
     * 获取所有批次数据的副本 - 用于调试和测试
     */
    @JSONField(serialize = false, deserialize = false)
    public Map<Integer, List<T>> getAllBatchData() {
        Map<Integer, List<T>> result = new HashMap<>();
        batchDataMap.forEach((batchId, data) -> result.put(batchId, new ArrayList<>(data)));
        return result;
    }

    /**
     * 重置调度器到初始状态
     */
    public void reset() {
        metadata.setGlobalPosition(0);
        metadata.setLastAccessedBatch(1);

        // 重置活跃批次为前N个批次
        activeBatchIds.clear();
        for (int i = 1; i <= activeBatchCount; i++) {
            activeBatchIds.add(i);
        }

        // 重置批次状态
        initializeBatchStatuses();

        this.lastUpdated = LocalDateTime.now();
        log.info("调度器已重置, userId: {}, 活跃批次: {}", userId, activeBatchIds);
    }

    /**
     * 检查是否有过期批次
     * 
     * @return 是否存在过期批次
     */
    public boolean hasExpiredBatches() {
        if (batchStatuses == null) {
            return false;
        }
        return batchStatuses.containsValue(STATUS_EXPIRED);
    }

    /**
     * 获取过期批次数量
     * 
     * @return 过期批次数量
     */
    public int getExpiredBatchCount() {
        if (batchStatuses == null) {
            return 0;
        }
        int count = (int) batchStatuses.values().stream().filter(STATUS_EXPIRED::equals).count();

        // 更新元数据
        if (metadata != null) {
            metadata.setExpiredBatchesCount(count);
        }

        return count;
    }

    /**
     * 获取所有过期批次的ID列表
     * 
     * @return 过期批次ID列表
     */
    public List<Integer> getExpiredBatchIds() {
        if (batchStatuses == null) {
            return new ArrayList<>();
        }

        List<Integer> expiredIds = new ArrayList<>();
        batchStatuses.forEach((batchId, status) -> {
            if (STATUS_EXPIRED.equals(status)) {
                expiredIds.add(batchId);
            }
        });

        log.debug("获取过期批次ID列表, userId: {}, 过期批次: {}", userId, expiredIds);
        return expiredIds;
    }

    /**
     * 获取所有过期批次的数据映射
     * 
     * @return 过期批次数据映射 (批次ID -> 数据列表)
     */
    public Map<Integer, List<T>> getExpiredBatchesData() {
        List<Integer> expiredIds = getExpiredBatchIds();
        Map<Integer, List<T>> expiredData = new HashMap<>();

        for (Integer batchId : expiredIds) {
            List<T> data = getBatchData(batchId);
            expiredData.put(batchId, new ArrayList<>(data)); // 返回副本，避免外部修改
        }

        log.debug("获取过期批次数据, userId: {}, 过期批次数量: {}", userId, expiredData.size());
        return expiredData;
    }

    /**
     * 根据状态获取批次ID列表（通用方法）
     * 
     * @param status 批次状态
     * @return 指定状态的批次ID列表
     */
    public List<Integer> getBatchesByStatus(String status) {
        if (batchStatuses == null || status == null) {
            return new ArrayList<>();
        }

        List<Integer> batchIds = new ArrayList<>();
        batchStatuses.forEach((batchId, batchStatus) -> {
            if (status.equals(batchStatus)) {
                batchIds.add(batchId);
            }
        });

        return batchIds;
    }

    /**
     * 根据状态获取批次数据映射（通用方法）
     * 
     * @param status 批次状态
     * @return 指定状态的批次数据映射
     */
    public Map<Integer, List<T>> getBatchDataByStatus(String status) {
        List<Integer> batchIds = getBatchesByStatus(status);
        Map<Integer, List<T>> batchData = new HashMap<>();

        for (Integer batchId : batchIds) {
            List<T> data = getBatchData(batchId);
            batchData.put(batchId, new ArrayList<>(data)); // 返回副本
        }

        return batchData;
    }

    /**
     * 获取活跃批次数据
     * 
     * @return 活跃批次数据映射
     */
    public Map<Integer, List<T>> getActiveBatchesData() {
        return getBatchDataByStatus(STATUS_ACTIVE);
    }

    /**
     * 获取准备状态批次数据
     * 
     * @return 准备状态批次数据映射
     */
    public Map<Integer, List<T>> getReadyBatchesData() {
        return getBatchDataByStatus(STATUS_READY);
    }

    /**
     * 替换指定过期批次的数据并将状态改为ready
     * 
     * @param batchId 批次ID
     * @param newData 新数据
     * @return 是否替换成功
     */
    public boolean replaceExpiredBatchData(int batchId, List<T> newData) {

        // 参数验证
        if (batchId < 1 || batchId > totalBatches) {
            log.warn("无效的批次ID: {}, 无法替换数据", batchId);
            return false;
        }

        // 检查批次是否为过期状态
        String currentStatus = getBatchStatus(batchId);
        if (!STATUS_EXPIRED.equals(currentStatus)) {
            log.warn("批次{}不是过期状态(当前状态: {}), 无法替换数据, userId: {}", batchId, currentStatus, userId);
            return false;
        }

        try {
            // 标记异步更新开始
            if (metadata != null) {
                metadata.setAsyncUpdateInProgress(true);
            }

            // 更新数据
            updateBatchData(batchId, newData);

            // 将状态从过期改为ready
            setBatchStatus(batchId, STATUS_READY);

            // 更新元数据
            if (metadata != null) {
                metadata.setLastAsyncUpdateTime(LocalDateTime.now());
                metadata.setAsyncUpdateInProgress(false);
            }

            log.info("成功替换过期批次{}数据, userId: {}, 数据量: {}", batchId, userId, newData != null ? newData.size() : 0);
            return true;

        } catch (Exception e) {
            log.error("替换过期批次{}数据失败, userId: {}, 错误: {}", batchId, userId, e.getMessage(), e);

            // 重置异步更新标记
            if (metadata != null) {
                metadata.setAsyncUpdateInProgress(false);
            }
            return false;
        }
    }

    /**
     * 批量替换过期批次数据
     * 
     * @param newDataMap 新数据映射 (批次ID -> 新数据列表)
     * @return 成功替换的批次数量
     */
    public int replaceAllExpiredBatchesData(Map<Integer, List<T>> newDataMap) {
        if (newDataMap == null || newDataMap.isEmpty()) {
            log.warn("新数据映射为空, 无法进行批量替换, userId: {}", userId);
            return 0;
        }

        List<Integer> expiredIds = getExpiredBatchIds();
        int successCount = 0;

        log.info("开始批量替换过期批次数据, userId: {}, 过期批次: {}, 提供数据批次: {}", 
                userId, expiredIds, newDataMap.keySet());

        // 标记异步更新开始
        if (metadata != null) {
            metadata.setAsyncUpdateInProgress(true);
        }

        try {
            for (Integer batchId : expiredIds) {
                if (newDataMap.containsKey(batchId)) {
                    List<T> newData = newDataMap.get(batchId);
                    if (replaceExpiredBatchData(batchId, newData)) {
                        successCount++;
                    }
                } else {
                    log.warn("批次{}没有提供新数据, 跳过替换, userId: {}", batchId, userId);
                }
            }

            // 更新元数据
            if (metadata != null) {
                metadata.setLastAsyncUpdateTime(LocalDateTime.now());
            }

        } finally {
            // 重置异步更新标记
            if (metadata != null) {
                metadata.setAsyncUpdateInProgress(false);
            }
        }

        log.info("批量替换过期批次数据完成, userId: {}, 成功替换: {}/{}", userId, successCount, expiredIds.size());
        return successCount;
    }

    /**
     * 将过期批次状态重置为ready
     * 
     * @param batchId 批次ID
     * @return 是否重置成功
     */
    public boolean resetExpiredBatchToReady(int batchId) {
        String currentStatus = getBatchStatus(batchId);
        if (!STATUS_EXPIRED.equals(currentStatus)) {
            log.warn("批次{}不是过期状态(当前状态: {}), 无法重置为ready, userId: {}", batchId, currentStatus, userId);
            return false;
        }

        setBatchStatus(batchId, STATUS_READY);
        log.info("批次{}状态已从过期重置为ready, userId: {}", batchId, userId);
        return true;
    }

    /**
     * 将所有过期批次状态重置为ready
     * 
     * @return 成功重置的批次数量
     */
    public int resetAllExpiredBatchesToReady() {
        List<Integer> expiredIds = getExpiredBatchIds();
        int successCount = 0;

        for (Integer batchId : expiredIds) {
            if (resetExpiredBatchToReady(batchId)) {
                successCount++;
            }
        }

        log.info("批量重置过期批次状态完成, userId: {}, 成功重置: {}/{}", userId, successCount, expiredIds.size());
        return successCount;
    }
}