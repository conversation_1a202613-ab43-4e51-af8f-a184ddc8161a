package zx.vanx.common.redis.circular;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import zx.vanx.common.redis.circular.config.CircularBufferProperties;
import zx.vanx.common.redis.circular.provider.CircularDataProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 环形缓冲管理器 - 重构版
 * 
 * 职责：
 * 1. 统一管理调度器的创建和初始化
 * 2. 集成数据提供者，负责初始数据生成
 * 3. 提供基于SchedulerIdentifier的统一接口
 * 4. 管理调度器的完整生命周期
 * 
 * <AUTHOR>
 */
@Getter
@Slf4j
@Component
@RequiredArgsConstructor
public class CircularBufferManager {

    /**
     * 配置属性
     */
    private final CircularBufferProperties properties;

    /**
     * 创建环形缓冲调度器（使用默认配置和数据提供者）
     * 
     * @param <T>                数据类型
     * @param identifier         统一标识符
     * @param dataProvider       数据提供者
     * @param asyncProcessor     异步处理器
     * @return 环形缓冲调度器
     */
    public <T> CircularBufferScheduler<T> createScheduler(
            SchedulerIdentifier identifier,
            CircularDataProvider<T> dataProvider,
            AsyncCircularProcessor<T> asyncProcessor) {
        
        return createScheduler(
                identifier, 
                dataProvider, 
                asyncProcessor,
                properties.getDefaultTotalBatches(),
                properties.getDefaultBatchSize(),
                properties.getDefaultActiveBatchCount()
        );
    }

    /**
     * 创建环形缓冲调度器（完整版本 - 核心方法）
     * 
     * @param <T>                数据类型
     * @param identifier         统一标识符
     * @param dataProvider       数据提供者
     * @param asyncProcessor     异步处理器
     * @param totalBatches       总批次数
     * @param batchSize          每批次大小
     * @param activeBatchCount   活跃批次数量
     * @return 环形缓冲调度器
     */
    public <T> CircularBufferScheduler<T> createScheduler(
            SchedulerIdentifier identifier,
            CircularDataProvider<T> dataProvider,
            AsyncCircularProcessor<T> asyncProcessor,
            int totalBatches,
            int batchSize,
            int activeBatchCount) {

        log.info("创建环形缓冲调度器, identifier: {}, 配置: 批次数={}, 批次大小={}, 活跃批次数={}",
                identifier, totalBatches, batchSize, activeBatchCount);

        // 参数验证
        validateIdentifier(identifier);
        validateBatchConfiguration(totalBatches, batchSize, activeBatchCount);
        validateDataProvider(dataProvider);

        try {
            // 生成初始数据
            List<List<T>> initialData = generateInitialData(identifier, dataProvider, totalBatches, batchSize);

            // 创建调度器（使用identifier的userId作为兼容性参数）
            CircularBufferScheduler<T> scheduler = new CircularBufferScheduler<>(
                    identifier.getUserId(), initialData, totalBatches, batchSize, activeBatchCount);

            // 注册到异步处理器
            if (asyncProcessor != null) {
                if (identifier.hasCategory()) {
                    asyncProcessor.registerScheduler(identifier.getUserId(), identifier.getCategoryId(), scheduler);
                } else {
                    asyncProcessor.registerScheduler(identifier.getUserId(), scheduler);
                }
            }

            log.info("调度器创建成功, identifier: {}, 总数据量: {}", identifier, scheduler.getTotalDataSize());
            return scheduler;

        } catch (Exception e) {
            log.error("创建调度器失败, identifier: {}, 错误: {}", identifier, e.getMessage(), e);
            throw new RuntimeException("创建调度器失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成初始数据
     * 
     * @param identifier     统一标识符
     * @param dataProvider   数据提供者
     * @param totalBatches   总批次数
     * @param batchSize      批次大小
     * @return 初始数据列表
     */
    private <T> List<List<T>> generateInitialData(
            SchedulerIdentifier identifier,
            CircularDataProvider<T> dataProvider,
            int totalBatches,
            int batchSize) {

        log.info("生成初始数据, identifier: {}, 总批次: {}, 批次大小: {}, 提供者: {}",
                identifier, totalBatches, batchSize, dataProvider.getProviderName());

        // 生成批次ID列表 [1, 2, 3, 4]
        List<Integer> batchIds = new ArrayList<>();
        for (int i = 1; i <= totalBatches; i++) {
            batchIds.add(i);
        }

        // 使用数据提供者生成数据
        Map<Integer, List<T>> batchDataMap;
        if (identifier.hasCategory()) {
            batchDataMap = dataProvider.generateBatchData(identifier.getUserId(), identifier.getCategoryId(), batchIds);
        } else {
            batchDataMap = dataProvider.generateBatchData(identifier.getUserId(), batchIds);
        }

        // 转换为 List<List<T>> 格式并确保数据大小
        List<List<T>> initialData = new ArrayList<>();
        for (int i = 1; i <= totalBatches; i++) {
            List<T> batchData = batchDataMap.getOrDefault(i, new ArrayList<>());

            // 确保批次数据大小符合要求
            if (batchData.size() > batchSize) {
                batchData = new ArrayList<>(batchData.subList(0, batchSize));
                log.debug("批次{}数据超出限制，已截取到{}条, identifier: {}", i, batchSize, identifier);
            } else if (batchData.size() < batchSize) {
                log.warn("批次{}数据不足, identifier: {}, 期望: {}, 实际: {}", 
                        i, identifier, batchSize, batchData.size());
            }

            initialData.add(batchData);
        }

        log.info("初始数据生成完成, identifier: {}, 总批次: {}", identifier, totalBatches);
        return initialData;
    }

    /**
     * 创建异步环形处理器
     * 
     * @param <T>          数据类型
     * @param dataProvider 数据提供者
     * @return 异步环形处理器
     */
    public <T> AsyncCircularProcessor<T> createProcessor(CircularDataProvider<T> dataProvider) {
        AsyncCircularProcessor<T> processor = new AsyncCircularProcessor<>(dataProvider);
        log.info("创建异步环形处理器，数据提供者: {}", dataProvider.getProviderName());
        return processor;
    }

    /**
     * 验证标识符
     */
    private void validateIdentifier(SchedulerIdentifier identifier) {
        if (identifier == null) {
            throw new IllegalArgumentException("标识符不能为null");
        }
        // SchedulerIdentifier 内部已经做了userId的验证
    }

    /**
     * 验证数据提供者
     */
    private void validateDataProvider(CircularDataProvider<?> dataProvider) {
        if (dataProvider == null) {
            throw new IllegalArgumentException("数据提供者不能为null");
        }
        if (!dataProvider.isAvailable()) {
            throw new IllegalStateException("数据提供者不可用: " + dataProvider.getProviderName());
        }
    }

    /**
     * 验证批次配置
     */
    private void validateBatchConfiguration(int totalBatches, int batchSize, int activeBatchCount) {
        if (totalBatches < 3 || totalBatches > 20) {
            throw new IllegalArgumentException("总批次数必须在3-20之间");
        }
        if (batchSize < 10 || batchSize > 500) {
            throw new IllegalArgumentException("批次大小必须在10-500之间");
        }
        if (activeBatchCount < 2 || activeBatchCount > totalBatches - 1) {
            throw new IllegalArgumentException("活跃批次数量必须在2到总批次数-1之间");
        }
    }

    // ========== 旧版本兼容性方法（保留以避免破坏现有代码） ==========

    /**
     * @deprecated 使用 createScheduler(SchedulerIdentifier, CircularDataProvider, AsyncCircularProcessor) 替代
     */
    @Deprecated
    public <T> CircularBufferScheduler<T> createScheduler(Long identifier) {
        log.warn("使用了已弃用的createScheduler方法，建议迁移到新版本");
        return new CircularBufferScheduler<>(
                identifier, 
                null,
                properties.getDefaultTotalBatches(),
                properties.getDefaultBatchSize(),
                properties.getDefaultActiveBatchCount()
        );
    }

    /**
     * @deprecated 使用 createScheduler(SchedulerIdentifier, CircularDataProvider, AsyncCircularProcessor, int, int, int) 替代
     */
    @Deprecated
    public <T> CircularBufferScheduler<T> createScheduler(
            Long identifier, 
            int totalBatches, 
            int batchSize, 
            int activeBatchCount) {
        log.warn("使用了已弃用的createScheduler方法，建议迁移到新版本");
        return new CircularBufferScheduler<>(identifier, null, totalBatches, batchSize, activeBatchCount);
    }

}