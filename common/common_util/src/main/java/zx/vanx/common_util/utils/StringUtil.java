package zx.vanx.common_util.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * author
 * Date: 2023/9/1111:31
 **/
public class StringUtil {

    private static final String BASE_URL = "http://101.43.154.175:9003/zx-vanx/system-file/";

    /**
     * 从完整URL中提取文件路径部分
     *
     * @param fullUrl 完整的URL字符串
     * @return 提取出的文件路径，如果URL不包含基础前缀则返回原字符串
     */
    public static String extractFilePath(String fullUrl) {

        if (fullUrl == null || fullUrl.isEmpty()) {
            return "";
        }

        if (fullUrl.startsWith(BASE_URL)) {
            return fullUrl.substring(BASE_URL.length());
        }
        return fullUrl;
    }

    public static List<Long> convertStringToIdList(String input) {
        // 去除大括号和空格，得到纯数字字符串
        String digits = input.replace("{", "").replace("}", "").replace(" ", "");

        // 使用逗号作为分隔符，将纯数字字符串分割为字符串数组
        String[] digitsArray = digits.split(",");

        List<Long> idList = new ArrayList<>();
        for (String digit : digitsArray) {
            // 将每个字符串转换为Long类型，并添加到列表中
            Long id = Long.valueOf(digit);
            idList.add(id);
        }

        return idList;
    }

    /**
     * 将以逗号分隔的字符串转换为字符串 List.
     *
     * @param input 以逗号分隔的字符串
     * @return 字符串 List
     */
    public static List<String> splitStringToList(String input) {
        if (input == null || input.isEmpty()) {
            return new ArrayList<>(); // 返回空列表
        }

        // 将字符串分割并转换为列表
        return new ArrayList<>(Arrays.asList(input.split(",")));
    }

}
