package zx.vanx.common_util.core.redis;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis 工具类
 * <AUTHOR>
 **/
@SuppressWarnings(value = { "unchecked", "rawtypes" })
@Component
@RequiredArgsConstructor
public class RedisCache
{

    public final RedisTemplate redisTemplate;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value)
    {
        // 2. 再保存新数据
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit)
    {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }


    /**
     * 通过key前缀获取所有的key
     * @param prefix key前缀
     * @return key列表
     */
    public List<String> scanKeysWithPrefix(String prefix) {
        RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
        assert connectionFactory != null;
        RedisConnection redisConnection = connectionFactory.getConnection();

        ScanOptions options = ScanOptions.scanOptions()
                .match(prefix + "*")
                .count(50)
                .build();

        Cursor<byte[]> cursor = redisConnection.scan(options);
        List<String> keys = new ArrayList<>();

        while (cursor.hasNext()) {
            keys.add(new String(cursor.next()));
        }
        return keys;
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout)
    {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit)
    {
        return Boolean.TRUE.equals(redisTemplate.expire(key, timeout, unit));
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key)
    {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key)
    {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key)
    {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 删除单个对象
     *
     * @param key 键值
     */
    public void deleteObject(final String key)
    {
        redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return 删除的数量
     */
    public boolean deleteObject(final Collection collection)
    {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key 缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList)
    {
        // 1. 先删除旧的列表，确保覆盖
        redisTemplate.delete(key);

        // 2. 如果新数据为空，直接返回 0
        if (dataList == null || dataList.isEmpty()) {
            return 0;
        }

        // 3. 写入新数据
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key)
    {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key 缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet)
    {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext())
        {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key)
    {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap)
    {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key)
    {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value)
    {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey)
    {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys)
    {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey)
    {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern)
    {
        return redisTemplate.keys(pattern);
    }

    /**
     * 批量获取多个键对应的 Map 数据
     * 使用 Redis pipeline 提升性能
     *
     * @param keys 键列表
     * @return 每个键对应的 Map 数据列表
     */
    public <T> List<Map<String, T>> multiGetCacheMap(final List<String> keys)
    {
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用 pipeline 批量获取数据
        List<Object> results = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (String key : keys) {
                connection.hGetAll(key.getBytes());
            }
            return null;
        });
        
        // 处理结果，进行 null 判空处理
        List<Map<String, T>> mapList = new ArrayList<>();
        for (Object result : results) {
            if (result != null && result instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, T> map = (Map<String, T>) result;
                mapList.add(map);
            } else {
                // 对于 null 结果，添加空 Map 保持索引对应关系
                mapList.add(new HashMap<>());
            }
        }
        
        return mapList;
    }

    // ==================== 有序集合操作方法 ====================

    /**
     * 向有序集合添加成员
     *
     * @param key 缓存键值
     * @param value 成员值
     * @param score 分数
     * @return 是否添加成功
     */
    public Boolean addToSortedSet(final String key, final String value, final double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 从有序集合移除成员
     *
     * @param key 缓存键值
     * @param value 成员值
     * @return 移除的成员数量
     */
    public Long removeFromSortedSet(final String key, final String value) {
        return redisTemplate.opsForZSet().remove(key, value);
    }

    /**
     * 获取有序集合中成员的分数
     *
     * @param key 缓存键值
     * @param value 成员值
     * @return 成员分数，如果成员不存在则返回null
     */
    public Double getSortedSetScore(final String key, final String value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    /**
     * 获取有序集合的大小
     *
     * @param key 缓存键值
     * @return 集合大小
     */
    public Long getSortedSetSize(final String key) {
        return redisTemplate.opsForZSet().zCard(key);
    }

    /**
     * 检查有序集合中是否包含指定成员
     *
     * @param key 缓存键值
     * @param value 成员值
     * @return 是否包含该成员
     */
    public Boolean containsInSortedSet(final String key, final String value) {
        Double score = redisTemplate.opsForZSet().score(key, value);
        return score != null;
    }
}
