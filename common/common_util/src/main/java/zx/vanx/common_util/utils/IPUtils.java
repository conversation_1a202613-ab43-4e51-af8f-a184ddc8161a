package zx.vanx.common_util.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import zx.vanx.common_util.core.ip.IPInfo;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class IPUtils {
    public static String getCityByIP(String ipAddress) {

        String city = "";

        try {
            // 构建查询 URL
            String apiUrl = "http://ip-api.com/json/" + ipAddress;

            // 发送 HTTP 请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            // 读取响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            // 解析响应 JSON 数据
            String jsonResponse = response.toString();
            System.out.println("API 响应：" + jsonResponse);

            // 使用 Hutool 将 JSON 字符串转换为 JSONObject
            JSONObject jsonObject = JSONUtil.parseObj(jsonResponse);

            // 使用 Jackson 将 JSONObject 转换为 Java 对象
            ObjectMapper objectMapper = new ObjectMapper();
            IPInfo ipInfo = objectMapper.convertValue(jsonObject, IPInfo.class);


            return ipInfo.getCity();
            // 从 JSON 数据中获取省份信息（这里假设 JSON 数据中包含了省份信息）
            // 这里你需要根据 ip-api.com 返回的 JSON 数据结构来解析相应的省份信息
            // 示例中仅仅是打印了整个响应数据，实际情况下需要根据你的需求来解析相应的字段
            // 示例代码仅供参考，请根据实际情况做出相应修改
        } catch (IOException e) {
            e.printStackTrace();
        }

        return city;
    }

}
