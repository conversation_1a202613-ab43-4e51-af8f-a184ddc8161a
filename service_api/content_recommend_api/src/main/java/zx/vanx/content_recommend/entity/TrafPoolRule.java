package zx.vanx.content_recommend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;

import java.util.Date;

/**
 * <p>
 * vanx_fir_lev_pool_rule
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("vanx_traf_pool_rule")
@ApiModel(value="TrafPoolRule对象", description="vanx_traf_pool_rule")
public class TrafPoolRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "traf_pool_rule_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long trafPoolRuleId;

    @ApiModelProperty(value = "作品内容第一级流量池id号",required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long trafPoolId;

    @ApiModelProperty(value = "该流量池的推荐规则编码")
    private String ruleName;

    @ApiModelProperty(value = "该流量池的推荐规则编码")
    private String ruleCode;

    @ApiModelProperty(value = "属性取值类型：1、整数数值型2、浮点数值型3、时间数值型4、区间数值型5、字符数值型")
    private String ruleValueType;

    @ApiModelProperty(value = "属性取值为整数")
    private Integer ruleValueInt;

    @ApiModelProperty(value = "整数区间最小值")
    private Integer ruleValueMinInt;

    @ApiModelProperty(value = "整数区间最大值")
    private Integer ruleValueMaxInt;

    @ApiModelProperty(value = "属性取值字符串")
    private String ruleValueStr;

    @ApiModelProperty(value = "属性取值为时间，开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date ruleValueStartDate;

    @ApiModelProperty(value = "属性取值为时间，结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date ruleValueEndDate;


}
