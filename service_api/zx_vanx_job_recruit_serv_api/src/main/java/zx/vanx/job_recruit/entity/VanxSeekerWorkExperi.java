package zx.vanx.job_recruit.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxSeekerWorkExperi对象", description="")
public class VanxSeekerWorkExperi extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "seeker_work_experi_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long seekerWorkExperiId;

    @ApiModelProperty(value = "求职者ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jobSeekerId;

    @ApiModelProperty(value = "用户所在公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司所在行业的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long industryId;

    @ApiModelProperty(value = "公司所在行业的名称")
    private String industryName;

    @ApiModelProperty(value = "在职开始时间")
    private String startTime;

    @ApiModelProperty(value = "在职结束时间")
    private String endTime;

    @ApiModelProperty(value = "工作职位的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long jobPositionId;

    @ApiModelProperty(value = "工作职位的名称")
    private String jobPositionName;

    @ApiModelProperty(value = "工作内容的描述")
    private String jobDescription;

    @ApiModelProperty(value = "工作业绩")
    private String achievements;

    @ApiModelProperty(value = "所在部门名称")
    private String departmentName;

    @ApiModelProperty(value = "所处职级名称")
    private String jobLevelName;

    @ApiModelProperty(value = "是否隐藏本条信息")
    private String isHideToCompany;
}
