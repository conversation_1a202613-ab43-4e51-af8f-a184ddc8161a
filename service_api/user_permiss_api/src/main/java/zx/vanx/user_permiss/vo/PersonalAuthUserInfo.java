package zx.vanx.user_permiss.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 个人认证用户列表信息
 */
@Data
public class PersonalAuthUserInfo {

    @ApiModelProperty(value = "用户认证ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userVerifyId;

    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "个人真实姓名")
    private String artificialPersonName;

    @ApiModelProperty(value = "个人身份证号")
    private String artificialPersonNo;

    @ApiModelProperty(value = "个人身份证电子版图片地址(正面)")
    private String idPicFrontUrl;

    @ApiModelProperty(value = "个人身份证电子版图片地址（反面）")
    private String idPicBackUrl;

    @ApiModelProperty(value = "身份证审核状态")
    private String artificialPersonStatus;


    @ApiModelProperty(value = "用户头像地址")
    private String userAvatarUrl;

    @ApiModelProperty(value = "用户昵称")
    private String userNickName;

    @ApiModelProperty(value = "用户vanx号")
    private String userVanxId;

    @ApiModelProperty(value = "用户手机号")
    private String tel;

    @ApiModelProperty(value = "认证来源")
    private String addSource;

    @ApiModelProperty(value = "认证创建时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "用户真实姓名")
    private String userName;

    @ApiModelProperty(value = "用户证件类型")
    private String idType;

}
