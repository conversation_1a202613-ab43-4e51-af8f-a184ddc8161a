package zx.vanx.user_permiss.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * QQ登录请求对象
 */
@Data
@NoArgsConstructor
@ApiModel(value = "QQ登录请求", description = "处理移动APP从腾讯QQ SDK获取的授权信息")
public class QQLoginRequest {

    @ApiModelProperty(value = "QQ开放平台用户标识", required = true)
    @NotBlank(message = "openid不能为空")
    private String openid;

    @ApiModelProperty(value = "接口调用凭证", required = true)
    @NotBlank(message = "access_token不能为空")
    private String accessToken;

    @ApiModelProperty(value = "支付授权token")
    private String payToken;

    @ApiModelProperty(value = "token有效期，单位：秒")
    private String expiresIn;

    @ApiModelProperty(value = "QQ返回的完整授权信息(JSON格式)")
    private String oauthInfo;
}