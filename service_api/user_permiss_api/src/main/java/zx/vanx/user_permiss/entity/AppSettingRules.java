package zx.vanx.user_permiss.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 平台系统设置规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("vanx_app_setting_rules")
@ApiModel(value="AppSettingRules对象", description="平台系统设置规则表")
public class AppSettingRules extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "app_rule_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long appRuleId;

    @ApiModelProperty(value = "app系统设置规则名称")
    private String appRuleName;

    @ApiModelProperty(value = "app系统设置规则编码")
    private String appRuleCode;

    @ApiModelProperty(value = "app系统设置规则类型")
    private String appRuleType;

    @ApiModelProperty(value = "APP端系统设置规则图标的URL")
    private String appRuleIcon;

    @ApiModelProperty(value = "APP端系统设置规则图标的存放在MinIO上面的文件名称")
    private String ruleIconMinioName;

    @ApiModelProperty(value = "属于哪一个页面(栏目)：1：笔记；2:纪念日；3：事项")
    private String belongToColumn;

    @ApiModelProperty(value = "规则父级id，顶级为0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentAppRuleId;

    @ApiModelProperty(value = "是否为叶子节点")
    private Boolean isLeaf;

    @ApiModelProperty(value = "该规则是否在规则取值表中有相应的取值可供用户选择")
    private Boolean hasRulesValueForUser;

    @ApiModelProperty(value = "是否枚举属性")
    private Boolean isEnumeration;

    @ApiModelProperty(value = "是否必填")
    private Boolean optionType;

    @ApiModelProperty(value = "选择类型：1、单选2、多选")
    private String selectType;

    @ApiModelProperty(value = "排序指数，越小越靠前")
    private Integer sortNumber;

    @ApiModelProperty(value = "商品的类目来源：1、自定义添加2、平台库选择")
    private String addSource;

    @ApiModelProperty(value = "分类认证失败原因")
    private String rejectReason;

    @ApiModelProperty(value = "审核人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long platformCheckUserId;

    @ApiModelProperty(value = "复审人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long platformRecheckUserId;

    @ApiModelProperty(value = "审核时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date checkTime;

    @ApiModelProperty(value = "规则值列表，只有当前规则为叶子节点时才有值")
    @TableField(exist = false)
    private List<AppSettingRulesValue> appSettingRulesValueList = new ArrayList<>();

    @ApiModelProperty(value = "规则值列表，只有当前规则为叶子节点时才有值")
    @TableField(exist = false)
    private List<AppSettingRules> children = new ArrayList<>();


}
