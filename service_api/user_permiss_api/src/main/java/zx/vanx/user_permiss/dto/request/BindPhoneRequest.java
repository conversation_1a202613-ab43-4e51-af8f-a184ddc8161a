package zx.vanx.user_permiss.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 绑定手机号请求对象
 */
@Data
@NoArgsConstructor
@ApiModel(value = "绑定手机号请求", description = "第三方登录首次使用时绑定手机号")
public class BindPhoneRequest {

    @ApiModelProperty(value = "身份类型(qq, wechat等)", required = true)
    @NotBlank(message = "身份类型不能为空")
    private String identityType;

    @ApiModelProperty(value = "临时令牌", required = true)
    @NotBlank(message = "临时令牌不能为空")
    private String tempToken;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "短信验证码", required = true)
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确")
    private String smsCode;
}