package zx.vanx.ma_manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  系统任务对于操作及得到积分的设置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxTaskOperatePoints对象", description="")
public class VanxTaskOperateReward extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "task_operate_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskOperateId;

    @ApiModelProperty(value = "任务的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    @ApiModelProperty(value = "设置规则昵称")
    private String operateName;

    @ApiModelProperty(value = "设置的规则编码")
    private String operateCode;

    @ApiModelProperty(value = "该任务操作对象的类型：视频/照片、文章、动态、关注")
    private String operateObjectType;

    @ApiModelProperty(value = "该任务操作对象的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operateObjectId;

    @ApiModelProperty(value = "该任务操作对象如果为图片，则存放图片的URL")
    private String operateObjectUrl;

    @ApiModelProperty(value = "该规则所定义的需要用户完成的行为的类型：登录、签到、观看视频、视频点赞、视频评论、视频打赏、视频转发、视频收藏、发布视频")
    private String userBehaviorType;

    @ApiModelProperty(value = "该规则所定义的需要用户完成的行为的次数，默认每天一次")
    private String userBehaviorNum;

    @ApiModelProperty(value = "奖励的类型：积分、红包、优惠券、平台商品、商户商品")
    private String rewardType;

    @ApiModelProperty(value = "奖励的来源：平台商品、商户商品")
    private String rewardSource;

    @ApiModelProperty(value = "奖励的图片的URL")
    private String rewardPicUrl;

    @ApiModelProperty(value = "奖励的图片在minio中的名称")
    private String rewardPicMinioName;

    @ApiModelProperty(value = "奖励对应的对象的类型，主要用在当奖品类型为：积分类型时,该字段表示该积分的类型行为积分、游戏积分、购物积分的ID,默认均为行为积分")
    private Long rewardObjId;

    @ApiModelProperty(value = "奖励对应的对象的类型，主要用在奖品类型为：积分类型时，该字段表示该积分的类型行为积分、游戏积分、购物积分，默认为行为积分")
    private String rewardObjType;

    @ApiModelProperty(value = "积分类目的图片路径url")
    @TableField(exist = false)
    private String categPicUrl;

    @ApiModelProperty(value = "奖励对应的对象的类型为：积分类型时，该字段表示该积分的类型积分的取值")
    private String rewardObjValue;

    @ApiModelProperty(value = "完成任务的奖励分类：固有奖励、额外奖励")
    private String rewardCategory;

    @ApiModelProperty(value = "为了额外奖励需要操作的数")
    private Integer opNumForAddiReward;

    @ApiModelProperty(value = "改规则所定义的操作的限额数值：发布视频的次数/天、发布照片的次数/天、发布博客的次数/天")
    private Integer operateLimitedValue;

    @ApiModelProperty(value = "该规则使用应该对应的用户等级ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long levelInfoId;

    @ApiModelProperty(value = "等级分类：成长值等级、购物等级、游戏等级")
    private String userLevelType;

    @ApiModelProperty(value = "级别的名称")
    private String userLevelName;
}
