package zx.vanx.ma_manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  幸运大转盘游戏信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxLuckyWheelInfo对象", description="")
public class VanxLuckyWheelInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "lucky_wheel_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long luckyWheelId;

    @ApiModelProperty(value = "幸运大转盘游戏的名称如：春节幸运大赚盘、双十一特别大赚盘等")
    private String luckyWheelName;

    @ApiModelProperty(value = "活动的ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long promotionActivityId;

    @ApiModelProperty(value = "红包发起卖家的ID，可以留空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sellerId;

    @ApiModelProperty(value = "红包发起卖家的店铺ID，可以留空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty(value = "WEB版游戏的类型：幸运赚盘、红包雨等")
    private String webGameType;

    @ApiModelProperty(value = "幸运大赚盘游戏的编码")
    private String luckyWheelCode;

    @ApiModelProperty(value = "幸运大赚盘游戏的开始日期、时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wheelStartTime;

    @ApiModelProperty(value = "幸运大赚盘游戏的结束日期、时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wheelEndTime;

    @ApiModelProperty(value = "幸运大赚盘游戏的持续时间，单位：秒")
    private Integer wheelDuration;

    @ApiModelProperty(value = "幸运大赚盘的状态：已上线、已下线、中止、待送审、待审核、审核驳回")
    private String wheelStatus;

    @ApiModelProperty(value = "游戏的描述")
    private String luckyWheelDescrip;

    @ApiModelProperty(value = "游戏的广告语")
    private String luckyWheelAd;

    @ApiModelProperty(value = "当前玩幸运大赚盘游戏的人数")
    private Integer currentNumOfPlay;

    @ApiModelProperty(value = "是否启用，用于控制可选设置Hi对象的显示状态")
    private String isActive;

    @ApiModelProperty(value = "转盘外环图片")
    @TableField(exist = false)
    private String turntableOuterRingPic;

    @ApiModelProperty(value = "转盘指针按钮图片")
    @TableField(exist = false)
    private String turntablePointerBtnPic;

    @ApiModelProperty(value = "背景音乐")
    @TableField(exist = false)
    private String backgroundMusicFile;

    @ApiModelProperty(value = "转盘音效")
    @TableField(exist = false)
    private String rotarySoundFile;

    @ApiModelProperty(value = "游戏次数")
    @TableField(exist = false)
    private Long gameFrequency;

    @ApiModelProperty(value = "游戏配置信息")
    @TableField(exist = false)
    private List<VanxWebGameConfig> vanxWebGameConfigList;

    @ApiModelProperty(value = "奖品信息")
    @TableField(exist = false)
    private List<VanxLotteryPrizes> vanxLotteryPrizesList;
}
