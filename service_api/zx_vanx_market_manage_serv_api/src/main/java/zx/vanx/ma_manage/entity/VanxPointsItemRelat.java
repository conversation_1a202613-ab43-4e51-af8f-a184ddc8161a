package zx.vanx.ma_manage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  用户的积分商品关系表：即积分商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxPointsItemRelat对象", description="")
public class VanxPointsItemRelat extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "points_item_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pointsItemId;

    @ApiModelProperty(value = "活动发起卖家的ID,可以留空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sellerId;

    @ApiModelProperty(value = "参与积分兑换活动的卖家店铺ID,可以留空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    @ApiModelProperty(value = "参与积分兑换活动商品的ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;

    @ApiModelProperty(value = "参与该积分兑换活动商品sku的ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    @ApiModelProperty(value = "平台一级商品类目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long firstLevelCid;

    @ApiModelProperty(value = "平台二级商品类目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long secondLevelCid;

    @ApiModelProperty(value = "平台三级商品类目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long thirdLevelCid;

    @ApiModelProperty(value = "商品在平台上所起的sku名称（即店铺起的标题，该名称为系统自动生成字段）")
    private String itemName;

    @ApiModelProperty(value = "sku的图片路径")
    private String skuPicUrl;

    @ApiModelProperty(value = "商品在平台上所起的sku名称（即店铺起的标题，该名称为系统自动生成字段）")
    private String itemSkuName;

    @ApiModelProperty(value = "商品的单位比如：袋、瓶")
    private String productUnit;

    @ApiModelProperty(value = "一笔兑换所包含的商品数量")
    private Integer productNumInExchan;

    @ApiModelProperty(value = "积分的类类目在平台内部系统上所起的名称,即:行为积分、购物积分、游戏积分")
    private String exchaInternalPointCateg;

    @ApiModelProperty(value = "该商品能够兑换的积分的类类目在客户端显示的时候的名称,即:热豆、金豆、乐豆")
    private String exchaPointCateg;

    @ApiModelProperty(value = "积分分类目的的图片路径URL")
    private String categPicUrl;

    @ApiModelProperty(value = "该商品兑换所需的积分数量")
    private double neededPointsValue;

    @ApiModelProperty(value = "促销库存的数量")
    private Integer promotionStockNum;

    @ApiModelProperty(value = "销售数量")
    private Integer promotionSaleNum;

    @ApiModelProperty(value = "购买数量限制")
    private Integer promotionQuantityLimit;

    @ApiModelProperty(value = "排序指数、越小越靠前")
    private Integer sortNumber;

    @ApiModelProperty(value = "上架时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime onSaleTime;

    @ApiModelProperty(value = "下架时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime offSaleTime;

    @ApiModelProperty(value = "操作方：商家、平台")
    private String operator;

    @ApiModelProperty(value = "商品的状态：未发布、待审核、审核驳回、待上架、在售、已下架、锁定、申请解锁、删除")
    private String pointsItemStatus;

    @ApiModelProperty(value = "平台方下架或锁定或审核驳回时给出的理由")
    private String statusChangeReason;

    @ApiModelProperty(value = "审核人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long platfromCheckUserId;

    @ApiModelProperty(value = "复审人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long platfromRecheckUserId;

    @ApiModelProperty(value = "审核时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;
}
