package zx.vanx.ma_manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  Web游戏配置表（幸运大赢盘、红包雨等）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxWebGameConfig对象", description="")
public class VanxWebGameConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "game_config_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long gameConfigId;

    @ApiModelProperty(value = "Web版游戏的类型：幸运大赚盘、红包雨等")
    private String webGameType;

    @ApiModelProperty(value = "Web游戏的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long webGameId;

    @ApiModelProperty(value = "游戏设置的类型：转盘外环图、转盘指针按钮图、背景音乐、转盘音效等")
    private String gameSettingType;

    @ApiModelProperty(value = "游戏设置的名称如：新年年货市集、节日狂欢等")
    private String gameSettingName;

    @ApiModelProperty(value = "游戏设置的对象（图片、音频文件）的值，比如：圆盘背景颜色的数值")
    private String settingObjValue;

    @ApiModelProperty(value = "游戏设置的对象（图片、音频文件）的URL")
    private String settingObjUrl;

    @ApiModelProperty(value = "游戏设置的对象（图片、音频文件）的minio中的名称")
    private String settingObjMinioName;

    @ApiModelProperty(value = "游戏设置的对象（图片、音频文件）的描述")
    private String settingObjDescription;

    @ApiModelProperty(value = "是否启用，用于控制可选设置对象的显示状态")
    private String isActice;
}
