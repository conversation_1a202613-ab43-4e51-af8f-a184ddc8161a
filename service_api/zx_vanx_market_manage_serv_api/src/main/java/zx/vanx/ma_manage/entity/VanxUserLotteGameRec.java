package zx.vanx.ma_manage.entity;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  游戏记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxUserLotteGameRec对象", description="")
public class VanxUserLotteGameRec extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "user_lottery_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userLotteryId;

    @ApiModelProperty(value = "玩Web游戏的用户ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "Web游戏的ID号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long lotteryGameId;

    @ApiModelProperty(value = "Web版游戏的类型：幸运赚盘、红包雨")
    private String webGameType;

    @ApiModelProperty(value = "玩游戏的开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime playGameTime;

    @ApiModelProperty(value = "是否中奖")
    private String isWinner;

    @ApiModelProperty(value = "中奖的奖项ID号")
    private Long lotteryPrizeId;

    @ApiModelProperty(value = "当次红包雨游戏中获得红包的总金额")
    private BigDecimal redPacAmount;

    @ApiModelProperty(value = "当次红包雨中获得红包的总数量")
    private Integer redPacNum;

    @ApiModelProperty(value = "是否已领取")
    private String hasPrizeReceived;
}
