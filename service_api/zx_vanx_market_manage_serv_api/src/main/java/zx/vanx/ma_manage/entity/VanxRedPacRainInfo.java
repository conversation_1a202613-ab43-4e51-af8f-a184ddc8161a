package zx.vanx.ma_manage.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  红包雨游戏信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="VanxRedPacRainInfo对象", description="")
public class VanxRedPacRainInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID号")
    @TableId(value = "red_pac_rain_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long redPacRainId;

    @ApiModelProperty(value = "活动的ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long promotionActivityId;

    @ApiModelProperty(value = "红包ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long redPacketsId;

    @ApiModelProperty(value = "红包雨的名称如：春节红包雨、双十一特别活动等")
    private String redPacRainName;

    @ApiModelProperty(value = "Web版游戏的类型：幸运赚盘、红包雨等")
    private String webGameType;

    @ApiModelProperty(value = "红包雨的红包类型如：春节红包雨、双十一特别活动等")
    private String redPacType;

    @ApiModelProperty(value = "红包雨的编码")
    private String redPacRainCode;

    @ApiModelProperty(value = "红包雨的开始日期、时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rainStartTime;

    @ApiModelProperty(value = "红包雨的结束日期、时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rainEndTime;

    @ApiModelProperty(value = "红包雨的持续时间，单位：秒")
    private Integer rainDuration;

    @ApiModelProperty(value = "红包雨中红包产生的间隔时间，单位秒")
    private Integer redPacInterval;

    @ApiModelProperty(value = "当次红包雨中红包的总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "当次红包雨中红包的剩余金额")
    private BigDecimal remainingAmount;

    @ApiModelProperty(value = "当次红包雨中红包的总数量")
    private Integer totalNum;

    @ApiModelProperty(value = "游戏的描述")
    private String redPacRainDescrip;

    @ApiModelProperty(value = "游戏的广告语")
    private String redPacRainAd;

    @ApiModelProperty(value = "红包雨的状态：已上线、已下线、中止、待送审、待审核、审核驳回")
    private String redPacRainStatus;

    @ApiModelProperty(value = "当前玩红包雨游戏的人数")
    private Integer currentNumOfPlay;

    @ApiModelProperty(value = "是否启用，用于控制可选设置对象的显示状态")
    private String isActive;

    @ApiModelProperty(value = "红包雨游戏背景图片")
    @TableField(exist = false)
    private String redRainGameBgPic;

    @ApiModelProperty(value = "红包雨封面背景图片")
    @TableField(exist = false)
    private String redRainCoverPic;

    @ApiModelProperty(value = "红包雨背景音乐")
    @TableField(exist = false)
    private String redRainBgMusic;

    @ApiModelProperty(value = "点击红包音效")
    @TableField(exist = false)
    private String redRainSoundEffects;

    @ApiModelProperty(value = "红包图片")
    @TableField(exist = false)
    private String redPacketPic;

    @ApiModelProperty(value = "打开红包图片")
    @TableField(exist = false)
    private String openRedPacketPic;

    @ApiModelProperty(value = "剩余游戏次数")
    @TableField(exist = false)
    private Long gameFrequency;

    @ApiModelProperty(value = "游戏配置信息")
    @TableField(exist = false)
    private List<VanxWebGameConfig> vanxWebGameConfigList;
}
