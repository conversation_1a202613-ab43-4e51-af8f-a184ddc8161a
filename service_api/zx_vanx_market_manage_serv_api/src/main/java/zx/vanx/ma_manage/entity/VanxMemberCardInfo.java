package zx.vanx.ma_manage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import zx.vanx.base.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员卡基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="VanxMemberCardInfo对象", description="")
public class VanxMemberCardInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "card_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long cardId;

    @ApiModelProperty(value = "会员级别")
    private String memberLevel;

    @ApiModelProperty(value = "会员卡的名称/标题")
    private String cardTitle;

    @ApiModelProperty(value = "会员卡的副名称/标题")
    private String cardSubtitle;

    @ApiModelProperty(value = "商铺的ID号")
    private Long shopId;

    @ApiModelProperty(value = "卖家（商家）的ID号")
    private Long sellerId;

    @ApiModelProperty(value = "会员卡的类型：黄金VIP、铂金VIP、银钻VIP、金钻VIP")
    private String cardType;

    @ApiModelProperty(value = "会员卡的使用说明")
    private String cardExplain;

    @ApiModelProperty(value = "会员卡的广告词")
    private String advert;

    @ApiModelProperty(value = "会员卡图标URL")
    private String cardIconUrl;

    @ApiModelProperty(value = "会员卡图标在minio中的名称")
    private String cardIconMinioName;

    @ApiModelProperty(value = "商品的来源：自定义添加，从平台商品库选择")
    private String addSource;

    @ApiModelProperty(value = "是否自动续费：不自动续费，自动续费")
    private String isAutoed;

    @ApiModelProperty(value = "会员卡是否上下架")
    private String isSale;

    @ApiModelProperty(value = "上架时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime onSaleTime;

    @ApiModelProperty(value = "下架时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime offSaleTime;

    @ApiModelProperty(value = "操作方：商家，平台")
    private String operator;

    @ApiModelProperty(value = "会员卡是否促销")
    private String isSalePromotion;

    @ApiModelProperty(value = "定时上架，为空则表示未设置定时上架")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timingOnSale;

    @ApiModelProperty(value = "定时下架，为空则表示未设置定时下架")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timingOffSale;

    @ApiModelProperty(value = "排序指数，越小越靠前")
    private Integer sortNumber;

    @ApiModelProperty(value = "会员卡的状态：未发布，待审核，审核驳回，在售，已下架")
    private String cardStatus;

    @ApiModelProperty(value = "平台方下架或锁定或审核驳回时给出的理由")
    private String statusChangeReason;

    @ApiModelProperty(value = "审核人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long platformCheckUserId;

    @ApiModelProperty(value = "审核人姓名")
    private String platformCheckUserName;

    @ApiModelProperty(value = "会员卡图片信息")
    @TableField(exist = false)
    private List<VanxMembCardPicture> vanxMembCardPictureList;

    @ApiModelProperty(value = "会员卡权益信息")
    @TableField(exist = false)
    private List<VanxMembCardRightsRules> vanxMembCardRightsRulesList;

    @ApiModelProperty(value = "会员卡价格信息")
    @TableField(exist = false)
    private List<VanxMembCardPrice> vanxMembCardPriceList;
}
